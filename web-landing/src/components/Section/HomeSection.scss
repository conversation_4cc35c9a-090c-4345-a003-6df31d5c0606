@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

@use '../../app/landing-mixin.scss' as *;

.home_section_container {
    @include flex-column-center;

    padding: 60px 24px;
    position: relative;

    @include breakpoint(md) {
        padding: 80px 24px;
    }

    .home_section__background {
        position: absolute;
        inset: 0;
        height: 100%;
        width: 100%;
        z-index: 0;
        opacity: 0.5;
    }

    .home_section__header {
        @include h2;

        color: themed($text);
        text-align: center;
        z-index: 1;
    }

    .home_section__subheader {
        @include hero-button-header;

        line-height: 26px;
        font-weight: $font-weight-light;
        color: themed($text-secondary);
        padding: 0;
        text-align: center;
        z-index: 1;

        @include breakpoint(md) {
            line-height: 30px;
        }
    }

    .home_section__content {
        @include flex-column-center;
        @include fill-available;

        z-index: 1;
        max-width: $landing-container-max-width;
    }

    &.home_section_container--dark {
        background: $dark-purple-100;
        background: radial-gradient(77.23% 50% at 50% 0%, rgba(173, 0, 255, 0.32) 0%, rgba(173, 0, 255, 0.02) 100%),
            radial-gradient(72.71% 17.77% at 50% 100%, rgba(173, 0, 255, 0.24) 0%, rgba(173, 0, 255, 0.01) 100%),
            $dark-purple-100;
    }
}
