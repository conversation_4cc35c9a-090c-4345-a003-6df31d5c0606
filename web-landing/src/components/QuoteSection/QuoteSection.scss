@use 'theme' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'layout' as *;
@use 'fonts' as *;

.quote_section {
    .quote_section__title {
        background: linear-gradient(356deg, #362783, #7b6dc2);
        /* stylelint-disable-next-line property-no-vendor-prefix */
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        text-align: center;
        padding-bottom: 2px;
        margin-bottom: 0;
    }

    .quote_section__body {
        display: flex;

        margin-top: -32px;
        margin-bottom: -44px;

        padding-top: $spacer-60;
        padding-bottom: $spacer-80;

        overflow-x: scroll;
        -webkit-overflow-scrolling: touch;
        scroll-snap-type: x mandatory;
        scroll-behavior: smooth;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    .quote_section_block {
        scroll-snap-align: center;
        display: grid;

        min-width: 80%;
        margin: 0 ($spacer-4 * -1);
        // stylelint-disable scss/operator-no-unspaced

        @include breakpoint(md) {
            min-width: 60%;
            margin: 0;
        }

        @include breakpoint(xl) {
            min-width: 800px;
        }

        > * {
            transition:
                scale 0.4s ease,
                opacity 0.4s ease;
        }

        &:not(.quote_section_block__selected) > * {
            opacity: 0.4;
            scale: 0.92;
        }
    }

    .quote_section__spacer {
        min-width: 10%;

        @include breakpoint(md) {
            min-width: 20%;
        }

        @include breakpoint(xl) {
            min-width: calc((100vw - 800px) / 2);
        }
    }

    .quote_section__dots {
        z-index: 10;
        position: relative;
        @include flex-center-center;
    }
}
