package com.nextchaptersoftware.plugin.zally

import com.nextchaptersoftware.plugin.zally.internal.ZallyLint
import com.nextchaptersoftware.plugin.zally.internal.ZallyLintTask
import org.gradle.api.Plugin
import org.gradle.api.Project

class ZallyGradlePlugin : Plugin<Project> {
    override fun apply(project: Project) {
        project.extensions.run {
            create("zallyLint", ZallyLint::class.java)
        }
        with(project.tasks) {
            with(register("zallyLint", ZallyLintTask::class.java)) {
                get().outputs.upToDateWhen { false }
            }
        }
    }
}
