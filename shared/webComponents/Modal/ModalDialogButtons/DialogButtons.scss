@import '../../styles/layout';
@import '../../styles/misc';
@import '../../styles/fonts';
@import '../../styles/flex';

.dialog_buttons {
    display: flex;
    width: 100%;

    // Single full width variant
    &.dialog_buttons__single_full_width {
        justify-content: center;

        .dialog_buttons__full_width {
            width: 100%;
        }
    }

    // Single right-aligned variant
    &.dialog_buttons__single_right {
        justify-content: flex-end;

        .dialog_buttons__spacer {
            flex: 1;
        }
    }

    // Two equal width buttons variant
    &.dialog_buttons__two_equal {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: $spacer-16;

        .dialog_buttons__primary,
        .dialog_buttons__secondary {
            width: 100%;
        }
    }

    // Split left/right variant
    &.dialog_buttons__split_left_right {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .dialog_buttons__left_section {
            display: flex;
            align-items: center;

            .dialog_buttons__tertiary {
                margin-right: $spacer-16;
            }
        }

        .dialog_buttons__right_section {
            display: flex;
            gap: $spacer-16;
        }
    }
}
