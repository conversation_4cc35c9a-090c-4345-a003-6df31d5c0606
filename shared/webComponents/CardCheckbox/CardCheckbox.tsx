import classNames from 'classnames';
import { ReactNode } from 'react';

import { faCircle } from '@fortawesome/pro-regular-svg-icons/faCircle';
import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons/faCircleCheck';

import { Icon } from '../Icon/Icon';

import './CardCheckbox.scss';

interface Props {
    children: ReactNode;

    isChecked: boolean;
    onChange?: (isChecked: boolean) => void;
}

export function CardCheckbox({ children, isChecked, onChange }: Props) {
    const classes = classNames({
        card_checkbox: true,
        card_checkbox__checked: isChecked,
    });

    return (
        <div className={classes} onClick={() => onChange?.(!isChecked)}>
            <Icon icon={isChecked ? faCircleCheck : faCircle} className="card_checkbox__check" size="medium" />
            {children}
        </div>
    );
}
