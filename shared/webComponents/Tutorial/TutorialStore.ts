import { ValueStream } from '@shared/stores/NewValueStream';

import { TutorialRegistry } from './TutorialRegistry';
import { ActiveTutorialState, TutorialState, TutorialType } from './TutorialTypes';

export abstract class TutorialStore {
    private currentState: TutorialState = { $case: 'inactive' };
    private valueStream = new ValueStream<TutorialState>({ $case: 'inactive' });
    readonly stream = this.valueStream;
    protected abstract readonly nextLabel: string;
    protected abstract readonly finishLabel: string;
    protected abstract readonly tutorialType: TutorialType;

    public async goNext() {
        const currentIndex = this.currentState.$case === 'inactive' ? -1 : this.currentState.state.currentIndex;
        const stepOrder = TutorialRegistry.getStepOrder(this.tutorialType);
        const nextIndex = currentIndex + 1;
        if (nextIndex < stepOrder.length) {
            this.currentState = { $case: 'active', state: this.getActiveState(nextIndex) };
            this.valueStream.value = this.currentState;
        } else {
            this.reset();
        }
    }

    private getActiveState(currentIndex: number): ActiveTutorialState {
        const stepOrder = TutorialRegistry.getStepOrder(this.tutorialType);
        const stepCount = stepOrder.length;

        const nextLabel = currentIndex === stepCount - 1 ? this.finishLabel : this.nextLabel;

        return {
            tutorialType: this.tutorialType,
            currentIndex,
            steps: stepOrder,
            stepCount,
            nextLabel,
        };
    }

    public async reset() {
        this.currentState = { $case: 'inactive' };
        this.valueStream.value = this.currentState;
    }
}
