@use '../styles/misc' as *;
@use '../styles/layout' as *;

.message_editor {
    border: 1px solid goldenrod;
    padding: $spacer-6;

    &:focus-within {
        border-color: magenta;
    }
}

.code_element {
    margin-left: $spacer-8;
    padding: $spacer-8;
    border: grey 1px solid;
    background: lightgrey;
}

.image_element {
    position: relative;
    width: fit-content;

    &.image_element__selected {
        box-shadow: 0 0 0 3px green;
    }

    .image_element__img {
        max-width: 100%;
    }

    .image_element__spinner {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto; /* presto! */
    }
}

.quote_element {
    color: grey;
    border-left: 2px solid grey;
    margin-left: $spacer-8;
    padding-left: $spacer-8;
}
