@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'misc' as *;

.icon.badge_icon {
    &.badge_icon__large {
        height: $size-32;
        width: $size-32;
        padding: $spacer-12;
        border-radius: $border-radius-16;
    }

    &.badge_icon__medium {
        height: $size-24;
        width: $size-24;
        padding: $spacer-8;
        border-radius: $border-radius-10;
    }

    &.badge_icon__small {
        height: $size-16;
        width: $size-16;
        padding: $spacer-4;
        border-radius: $border-radius-8;
    }

    &.badge_icon__xSmall {
        height: $size-12;
        width: $size-12;
        padding: $spacer-4;
        border-radius: $border-radius-8;
    }
}
