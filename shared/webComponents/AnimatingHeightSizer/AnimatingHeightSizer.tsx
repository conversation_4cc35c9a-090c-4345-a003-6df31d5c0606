import { CSSProperties, ReactNode, TransitionEvent, useCallback, useLayoutEffect, useRef } from 'react';

interface Props extends React.HTMLAttributes<HTMLElement> {
    isShown: boolean;
    children: ReactNode;
}

/**
 * An element that animates between its children's content height, and zero.
 * Useful for building accordion-style UIs.
 */
export function AnimatingHeightSizer({ isShown, children, ...props }: Props) {
    const ref = useRef<HTMLDivElement>(null);
    const isFirstRender = useRef<boolean>(true);
    const firstRenderHeight = useRef(isShown ? 'auto' : 0);

    useLayoutEffect(() => {
        const div = ref.current;
        if (!div) {
            return;
        }
        const currentHeight = div.clientHeight;
        const desiredHeight = isShown ? div.scrollHeight : 0;

        // On first render, simply set the size explicitly
        if (isFirstRender.current) {
            div.style.height = isShown ? 'auto' : '0';
            isFirstRender.current = false;
        }

        // All other renders, we animate to the new size
        else if (currentHeight !== desiredHeight) {
            // If we are animating from auto-height, we need to first set an absolute current height (calculated from the current
            // size), and then animate to the new height on the next render
            if (div.style.height === 'auto') {
                div.style.height = `${currentHeight}px`;
            }

            // Animate to the new value
            requestAnimationFrame(() => {
                div.style.height = `${desiredHeight}px`;
            });
        }
    }, [isShown]);

    const onTransitionEnd = useCallback(
        (e: TransitionEvent) => {
            // If we are done animating, and we are displaying content, reset
            // height to 'auto' so that regular content height resizing occurs
            if (e.propertyName === 'height' && ref.current && isShown) {
                ref.current.style.height = 'auto';
            }
        },
        [isShown]
    );

    const defaultStyle: CSSProperties = {
        transition: 'height 0.3s ease-in-out, opacity 0.3s ease-in-out',
        overflow: 'hidden',
        height: firstRenderHeight.current,
    };

    return (
        <div style={defaultStyle} ref={ref} onTransitionEnd={onTransitionEnd} {...props}>
            {children}
        </div>
    );
}
