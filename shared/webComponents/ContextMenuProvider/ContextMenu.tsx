import classNames from 'classnames';
import { MouseEvent, useCallback, useRef } from 'react';

import { ContextMenuRequest, ContextMenuResponse } from '@shared/api/generatedExtraApi';

import { logger } from '@shared/webUtils';

import { Button } from '../Button/Button';
import { ContextMenuItem } from './ContextMenuItem';

import './ContextMenu.scss';
const log = logger('ContextMenu');
export interface Props {
    header: React.ReactNode;
    items: ContextMenuItem[];
    triggerMenu: (request: ContextMenuRequest) => Promise<ContextMenuResponse>;
    className?: string;
}
const CONTEXT_MENU_PADDING = 10;
export const ContextMenu = ({ header, items, triggerMenu, className }: Props) => {
    const ref = useRef<HTMLButtonElement>(null);

    const triggerContextMenu = useCallback(
        async (e: MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation();
            e.preventDefault();

            const rect = ref.current?.getBoundingClientRect();
            const xPosition = rect?.left;
            const yPosition = rect?.bottom ? rect.bottom + CONTEXT_MENU_PADDING : undefined;
            const requestItems = items.map((v) => v.item);
            const response = await triggerMenu({
                items: requestItems,
                xPosition,
                yPosition,
            });

            const matchingItem = items.find((v) => v.item.id === response?.responseId);

            if (matchingItem) {
                matchingItem.callback?.();
            } else {
                log.debug('Missing context item', response?.responseId);
            }
        },
        [triggerMenu, items]
    );

    const classes = classNames({
        context_menu: true,
        [`${className}`]: !!className,
    });

    return (
        <Button as="icon" className={classes} ref={ref} onClick={triggerContextMenu}>
            {header}
        </Button>
    );
};
