@use 'layout' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'layout-mixin' as *;

.message_feedback_descriptions {
    border-radius: $spacer-8;

    position: relative;
}

.message_feedback_descriptions__background {
    border-radius: $spacer-8;
    position: absolute;
    inset: 0;
    z-index: -100;
}

.message_feedback_description {
    display: grid;
    grid:
        'avatar title' auto
        'avatar content' auto
        / auto 1fr;

    padding: $spacer-8;
    column-gap: $spacer-8;
    margin-top: $spacer-4;

    .message_feedback_description__avatar {
        grid-area: avatar;
    }

    .message_feedback_description__title {
        grid-area: title;
    }

    .message_feedback_description__name {
        font-weight: $font-weight-bold;
    }

    .message_feedback_description__type {
        font-weight: $font-weight-bold;
    }

    .message_feedback_description__content {
        grid-area: content;
    }
}
