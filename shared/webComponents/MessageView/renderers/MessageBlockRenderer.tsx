import { Block } from '../../../api/models';
import { BlockRenderer } from './BlockRenderer';
import { MessageBlockRendererContext, MessageBlockRendererTraits } from './MessageBlockRendererContext';

export const MessageBlockRenderer = ({
    block,
    teamId,
    repoId,
    onNavigateLink,
    textOnly,
    stripParagraphs,
}: {
    block: Block;
    teamId: string | undefined;
    repoId: string | undefined;
    onNavigateLink?: (url: string) => void;
    textOnly?: boolean;
    stripParagraphs?: boolean;
}) => {
    const traits: MessageBlockRendererTraits = {
        teamId,
        repoId,
        onNavigateLink,
        textOnly,
        stripParagraphs,
    };

    return (
        <MessageBlockRendererContext.Provider value={traits}>
            <BlockRenderer block={block} />
        </MessageBlockRendererContext.Provider>
    );
};
