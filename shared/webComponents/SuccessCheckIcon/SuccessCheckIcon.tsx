// import { Props as IconProps } from './Icon';
import { Props as IconProps } from '@shared/webComponents/Icon/Icon';
import { ThemeIcon } from '@shared/webComponents/Icon/ThemeIcon';

import checkIcon from '@clientAssets/success-checkmark.png';
import checkIconDark from '@clientAssets/success-checkmark-dark.png';

type Props = Omit<IconProps, 'icon'>;

export const SuccessCheckIcon = (props: Props) => {
    return <ThemeIcon lightIcon={checkIcon} darkIcon={checkIconDark} {...props} />;
};
