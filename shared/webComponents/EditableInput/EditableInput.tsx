import { useCallback, useEffect, useRef, useState } from 'react';

import './EditableInput.scss';

export interface Props {
    value: string;
    onValueChanged: (value: string) => void;
    placeholder?: string;
}

const DEFAULT_PLACEHOLDER = 'Enter a value';

export const EditableInput = ({ value, onValueChanged, placeholder }: Props) => {
    const inputRef = useRef<null | HTMLInputElement>(null);
    const [currentValue, setCurrentValue] = useState(value);

    const onBlurHandler = useCallback(() => {
        if (currentValue && currentValue !== '') {
            onValueChanged(currentValue);
            setCurrentValue(currentValue);
        }
    }, [onValueChanged, currentValue]);

    useEffect(() => {
        if (currentValue !== value) {
            onValueChanged(currentValue);
        }
    }, [value, currentValue, onValueChanged]);

    // There's a trick in the JSX here to get the input to size to its content: We add a span (title__sizer_template)
    // that contains the same content as the input, as the span will size to its content.  The input is then held
    // in the same location as the span, via its parent (title__sizer_parent).
    return (
        <div className="editable_input">
            <span className="editable_input__sizer_template">{currentValue ?? placeholder ?? DEFAULT_PLACEHOLDER}</span>
            <input
                ref={inputRef}
                className="editable_input__input"
                placeholder={placeholder ?? DEFAULT_PLACEHOLDER}
                value={currentValue}
                onChange={(event) => setCurrentValue(event.target.value)}
                onBlur={onBlurHandler}
            />
        </div>
    );
};
