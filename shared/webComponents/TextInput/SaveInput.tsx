import classNames from 'classnames';
import { useState } from 'react';

import { Button, ButtonVariant } from '../Button/Button';
import { Props as TextInputProps, TextInput } from './TextInput';

interface Props {
    value: string;
    onCancel?: () => void;
    onSubmit?: (value: string) => void;
    onSubmitPromise?: (value: string) => Promise<void>;
    disabled?: (value: string) => boolean;
    primaryButtonVariant?: ButtonVariant;
    secondaryButtonVariant?: ButtonVariant;
}

export const SaveInput = ({
    className,
    value: propsValue,
    onSubmit,
    onSubmitPromise,
    onCancel,
    disabled,
    primaryButtonVariant = 'primary',
    secondaryButtonVariant = 'secondary',
    ...props
}: Props & Omit<TextInputProps, 'disabled'>) => {
    const [value, setValue] = useState<string>(propsValue);

    const classes = classNames('save_input', className);
    return (
        <form>
            <TextInput
                {...props}
                className={classes}
                value={value}
                onValueChange={setValue}
                trailingAction={
                    <span className="save_input__actions">
                        <Button size="tight" variant={secondaryButtonVariant} onClick={onCancel}>
                            Cancel
                        </Button>
                        <Button
                            size="tight"
                            variant={primaryButtonVariant}
                            onClick={onSubmit ? () => onSubmit(value) : undefined}
                            onClickPromise={onSubmitPromise ? () => onSubmitPromise(value) : undefined}
                            disabled={!value || disabled?.(value)}
                            type="submit"
                        >
                            Save
                        </Button>
                    </span>
                }
            />
        </form>
    );
};
