import { PullRequest, ThreadInfoAggregate } from '@shared/api/generatedExtraApi';

import {
    CreateStoreProxyTraits,
    StoreProxyAction1Args,
    StoreProxyStream,
} from '@shared/proxy/StoreProxy/StoreProxyTypes';
import { ThreadViewSource } from '@shared/webUtils/ThreadUtils';

import { Insight } from '../../../webUtils';
import { SidebarThreadProps } from '../SidebarTypes';

export type CurrentFileInsightFailures =
    | { $case: 'loading'; fileName?: string }
    | { $case: 'uninitialized' }
    | { $case: 'missingFile' /* No file selected yet */ }
    | { $case: 'requiresConnection' }
    | { $case: 'processing' /* team data is being processed */ };

export type FileInsightsState =
    | CurrentFileInsightFailures
    | {
          $case: 'ready';
          fileName: string;
          filePath: string;
          insights: Insight[];
          teamId: string;
          hasFileInsights?: boolean | undefined; // True if the file has insights, but the current view area potentially doesn't
      };

export type CurrentFileInsightsSuccess = {
    $case: 'ready';
    fileName: string;
    filePath: string;
    docInsights: Insight[];
    prInsights: Insight[];
    teamId: string;
    hasFileInsights?: boolean | undefined; // True if the file has insights, but the current view area potentially doesn't
};

export type CurrentFileInsightsState = CurrentFileInsightFailures | CurrentFileInsightsSuccess;

export type CurrentFileInsights = CurrentFileInsightsState & {
    needsLongFormDocInstallation?: boolean;
};
export type ExplorerInsightsTab = 'docs' | 'pullRequests' | 'all';

export type ExplorerInsightsViewState = {
    currentFileState: CurrentFileInsights;

    // If this is set, UI will display the tab in question
    // If undefined, a tab UI will be displayed, allowing selecting between the tabs
    selectedTab: ExplorerInsightsTab;

    workspaceName: string | undefined;
};

export type ExplorerInsightsExtensionProps = SidebarThreadProps;

export const ExplorerInsightsStoreTraits = CreateStoreProxyTraits({
    category: 'ExplorerInsights',
    keyToString: (key: { tab: ExplorerInsightsTab }) => key.tab,
    actions: {
        openPullRequest: StoreProxyAction1Args<
            {
                source: ThreadViewSource;
                teamId: string;
                currentFilePath: string;
                pullRequest: PullRequest;
                threadInfo?: ThreadInfoAggregate;
            },
            void
        >(),
        selectThread: StoreProxyAction1Args<
            { source: ThreadViewSource; threadInfo: ThreadInfoAggregate; currentFilePath?: string },
            void
        >(),
        updateThreadUnread: StoreProxyAction1Args<{ threadInfo: ThreadInfoAggregate }, void>(),
    },
    streams: {
        stateStream: StoreProxyStream<ExplorerInsightsViewState>(),
    },
});
