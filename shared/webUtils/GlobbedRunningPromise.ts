/**
 * Globs concurrent promises together.
 * This class can be used if we want to prevent async requests from overlapping.
 */
export class GlobbedRunningPromise<T> {
    private runningPromise?: Promise<T>;

    /**
     * Run an async operation.
     * If there is already an operation in progress, it will be used instead.
     */
    run(fn: () => Promise<T>): Promise<T> {
        if (this.runningPromise) {
            return this.runningPromise;
        }

        const newPromise = new Promise<T>(async (resolve, reject) => {
            try {
                const result = await fn();
                resolve(result);
            } catch (e) {
                reject(e);
            } finally {
                this.runningPromise = undefined;
            }
        });

        this.runningPromise = newPromise;
        return newPromise;
    }
}
