import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class AwsProviderTraits extends ProviderTraits {
    readonly displayName: string = 'Aws';
    readonly shortenedDisplayName: string = 'Aws';

    projectLabelCounts(): StringPluralCounts {
        return { singular: 'content', plural: 'content' };
    }

    workspaceLabelCounts(): StringPluralCounts {
        return { singular: 'team', plural: 'teams' };
    }

    iconSrc(): IconSrc {
        return BrandIcons.awsIdentityCenter;
    }
}
