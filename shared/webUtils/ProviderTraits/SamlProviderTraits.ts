import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class SamlProviderTraits extends ProviderTraits {
    readonly displayName: string = 'SAML SSO';
    readonly shortenedDisplayName: string = 'SAML';

    projectLabelCounts(): StringPluralCounts {
        return { singular: 'content', plural: 'content' }; // Default value from ProviderUtils for Saml
    }

    workspaceLabelCounts(): StringPluralCounts {
        return { singular: 'team', plural: 'teams' }; // Default value from ProviderUtils for Saml
    }

    iconSrc(): IconSrc {
        return BrandIcons.saml;
    }
}
