import { RefObject, useEffect } from 'react';

/**
 * Hook that calls callback when there are clicks outside of the passed ref
 */
export function useClickOutside(ref: RefObject<Element>, callback: () => void, isActive = true) {
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (ref.current && !ref.current.contains(event.target as Node)) {
                event.stopPropagation();
                callback();
            }
        };

        if (isActive) {
            // Bind the event listener
            document.addEventListener('mousedown', handleClickOutside, true);
        }

        return () => {
            // Unbind the event listener on clean up
            document.removeEventListener('mousedown', handleClickOutside, true);
        };
    }, [ref, callback, isActive]);
}
