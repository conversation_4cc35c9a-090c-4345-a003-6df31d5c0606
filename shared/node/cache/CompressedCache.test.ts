import { CompressedCache } from './CompressedCache';

describe('CompressedCache', () => {
    test('cache', async () => {
        const cache = new CompressedCache(1);

        const key = 'foo';
        let value = await cache.getOrInsert(key, () => 'bar');
        expect(value).toEqual('bar');
        expect(cache.getStats()).toEqual({ hitCount: 0, missCount: 1, effectiveness: 0 });

        for (let i = 0; i < 4; i++) {
            value = await cache.getOrInsert(key, () => 'bar');
        }
        expect(value).toEqual('bar');
        expect(cache.getStats()).toEqual({ hitCount: 4, missCount: 1, effectiveness: 80 });
    });

    test('cache large values', async () => {
        const cache = new CompressedCache(1);

        const key = 'foo';
        const largeValue = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'.repeat(1000);
        let value = await cache.getOrInsert(key, () => largeValue);
        expect(value).toEqual(largeValue);
        expect(cache.getStats()).toEqual({ hitCount: 0, missCount: 1, effectiveness: 0 });

        for (let i = 0; i < 4; i++) {
            value = await cache.getOrInsert(key, () => largeValue);
        }
        expect(value).toEqual(largeValue);
        expect(cache.getStats()).toEqual({ hitCount: 4, missCount: 1, effectiveness: 80 });
    });

    test('propagates exceptions', async () => {
        const cache = new CompressedCache(1);

        const key = 'foo';

        const failingFunction = () => {
            throw new Error('oops');
        };

        await expect(cache.getOrInsert(key, failingFunction)).rejects.toThrow('oops');
    });
});
