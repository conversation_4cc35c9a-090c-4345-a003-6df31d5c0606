import dayjs from 'dayjs';
import xstream, { Listener, Stream } from 'xstream';

import { API, ResponseError } from '../api';
import { LoginOption } from './LoginTypes';

export type AuthPollingState =
    | { $case: 'notPolling' }
    | { $case: 'loading' }
    | { $case: 'error'; loginOption?: LoginOption }
    | { $case: 'authenticated'; token: string; refreshToken: string };

export const createPreAuthPollingStream = (loginOption: LoginOption, expiration = 60): Stream<AuthPollingState> => {
    const pollingIntervalMS = 2 * 1000;

    let isPolling = false;
    let shouldContinuePolling = false;

    const pollingExpiration = dayjs().add(expiration, 's');

    const doPollExchange = async (listener: Listener<AuthPollingState>) => {
        if (!shouldContinuePolling) {
            isPolling = false;
            return;
        }

        if (dayjs().isAfter(pollingExpiration)) {
            shouldContinuePolling = false;
            isPolling = false;
            listener.next({ $case: 'error', loginOption });
            listener.complete();
            return;
        }

        try {
            const { token, refreshToken } = await API.auth.preAuthTokenExchange();
            listener.next({ $case: 'authenticated', token, refreshToken });
            shouldContinuePolling = false;
            listener.complete();
        } catch (error) {
            if (error instanceof ResponseError) {
                if (error.response.status === 404) {
                    //  Token not ready for exchange. Continue polling
                    listener.next({ $case: 'loading' });
                    return;
                }
                if (error.response.status === 403) {
                    // Exchange window has elapsed. Client must stop polling
                    listener.next({ $case: 'error', loginOption });
                    shouldContinuePolling = false;
                    return;
                }
            }
            // Return false if unexpected issue
            listener.next({ $case: 'error', loginOption });
            shouldContinuePolling = false;
        } finally {
            setTimeout(() => {
                doPollExchange(listener);
            }, pollingIntervalMS);
        }
    };

    const startPollingExchange = (listener: Listener<AuthPollingState>) => {
        shouldContinuePolling = true;
        if (!isPolling) {
            isPolling = true;
            setTimeout(() => {
                doPollExchange(listener);
            }, 1);
        }
    };

    const stopPollingExchange = () => {
        shouldContinuePolling = false;
    };

    const startStream = async (listener: Listener<AuthPollingState>): Promise<void> => {
        startPollingExchange(listener);
    };

    const stopStream = () => {
        stopPollingExchange();
    };

    return xstream.create({ start: startStream, stop: stopStream });
};
