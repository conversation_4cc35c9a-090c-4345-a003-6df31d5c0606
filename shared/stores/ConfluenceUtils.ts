import { API } from '@shared/api';
import { ConfluenceConfiguration, ConfluenceSite, GetConfluenceSpacesResponse } from '@shared/api/models';

import { ClientServerCrypto } from '@shared/webUtils/Crypto/ClientServerCrypto';

export const getConfluenceSites = async (teamId: string): Promise<ConfluenceSite[]> => {
    return API.confluence.getConfluenceSites({ teamId });
};

export const getConfluenceConfiguration = async (
    teamId: string,
    confluenceId: string
): Promise<ConfluenceConfiguration> => {
    return API.confluence.getConfluenceConfiguration({ teamId, confluenceId });
};

export const updateConfluenceConfiguration = async (
    teamId: string,
    confluenceId: string,
    confluenceConfiguration: ConfluenceConfiguration
): Promise<void> => {
    return API.confluence.postConfluenceConfiguration({ teamId, confluenceId, confluenceConfiguration });
};

export const getConfluenceSpaces = async (
    teamId: string,
    confluenceId: string
): Promise<GetConfluenceSpacesResponse> => {
    return API.confluence.getConfluenceSpaces({ teamId, confluenceId });
};

export const patchConfluenceDataCenterToken = async (
    teamId: string,
    confluenceDataCenterId: string,
    hostUrl: string,
    pat: string
) => {
    const publicKey = await API.config.getPublicKey({
        teamId,
    });
    const encryptedPat = await ClientServerCrypto.encrypt(publicKey, pat);
    return API.confluenceDataCenter.patchConfluenceDataCenter({
        teamId,
        confluenceDataCenterId,
        patchConfluenceDataCenterRequest: { hostUrl, pat: encryptedPat },
    });
};
