package com.nextchaptersoftware.slackextractor

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.slack.extractor.utils.SlackChannelDocumentLoader
import kotlinx.coroutines.runBlocking

private class ChannelNotFoundException(channelName: String) : Exception("Channel not found: $channelName")

fun main(args: Array<String>) {
    val applicationConfiguration = ArgumentsParser.parse(args)

    runSuspendCatching {
        run(applicationConfiguration)
    }.onFailure {
        println(it)
    }.getOrThrow()
}

fun run(applicationConfiguration: ApplicationConfiguration) = runBlocking {
    val document = SlackChannelDocumentLoader(
        token = applicationConfiguration.token,
    ).loadChannelDocument(
        channelId = applicationConfiguration.channelId,
        threadTs = null,
        limit = 150,
    ) { true } ?: throw ChannelNotFoundException(applicationConfiguration.channelId)

    document.encode(applicationConfiguration.output)
}
