package com.nextchaptersoftware.ci.circleci.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CircleCiUserTest {

    @Test
    fun `decode from V2 API json`() {
        val jsonV2ApiUser = """
        {"name":null,"login":"lsale","id":"9b1b0810-0215-427a-a258-2a555abe1358"}
        """.trimIndent()

        val user = jsonV2ApiUser.decode<CircleCiUser>()
        assertThat(user).isEqualTo(
            CircleCiUser(
                name = null,
                login = "lsale",
                id = "9b1b0810-0215-427a-a258-2a555abe1358",
            ),
        )
    }
}
