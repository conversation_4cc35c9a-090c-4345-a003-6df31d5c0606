package com.nextchaptersoftware.stackoverflowteams.api

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.StackOverflowTeamsInstallation
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.user.secret.UserSecretServiceInterface
import com.sksamuel.hoplite.Secret

class StackOverflowTeamsAuthProvider(
    private val userSecretService: UserSecretServiceInterface,
    private val installationStore: InstallationStore = Stores.installationStore,
) {
    suspend fun getAccessToken(
        installationId: InstallationId,
    ): Pair<StackOverflowTeamsInstallation, Secret> {
        val installation = installationStore.findById(
            installationId = installationId,
        ) as StackOverflowTeamsInstallation

        return Pair(installation, getAccessToken(installation))
    }

    suspend fun getAccessToken(
        installation: StackOverflowTeamsInstallation,
    ): Secret {
        return installation.accessToken?.let { userSecretService.decrypt(it) }
            ?: error("Missing access token")
    }
}
