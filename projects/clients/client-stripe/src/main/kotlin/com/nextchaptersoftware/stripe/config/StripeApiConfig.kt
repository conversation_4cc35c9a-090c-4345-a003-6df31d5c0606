package com.nextchaptersoftware.stripe.config

import com.nextchaptersoftware.config.ConfigLoader
import com.sksamuel.hoplite.Secret
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
data class StripeApiSecretsConfig(
    @Contextual val key: Secret,
)

@Serializable
data class StripeApiConfig(
    val stripeApi: StripeApiSecretsConfig,
) {
    companion object {
        val INSTANCE = ConfigLoader.loadConfig<StripeApiConfig>(scope = "stripe-api-config")
    }
}
