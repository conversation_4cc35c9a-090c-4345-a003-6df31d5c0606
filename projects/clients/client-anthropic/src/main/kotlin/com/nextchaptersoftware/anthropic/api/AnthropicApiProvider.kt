package com.nextchaptersoftware.anthropic.api

import com.nextchaptersoftware.anthropic.AnthropicApiClient

class AnthropicApiProvider(
    val config: AnthropicApiConfiguration,
) {
    private val client by lazy {
        AnthropicApiClient(
            baseApiUri = config.baseApiUri,
            timeout = config.timeout,
            token = config.token,
        )
    }

    val anthropicCompletionsApi by lazy {
        AnthropicCompletionsApi(
            client = client,
        )
    }
}
