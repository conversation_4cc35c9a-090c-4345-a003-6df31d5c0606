package com.nextchaptersoftware.notion.api

import com.nextchaptersoftware.notion.models.Parent
import io.ktor.http.HttpStatusCode
import java.util.UUID
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock

@Disabled
class NotionBlocksApiTest {
    private val api = NotionBlocksApi(
        client = NotionHttpClient.makeClient(
            baseUrl = Utils.NOTION_BASE_URL,
            version = Utils.NOTION_VERSION,
        ),
        oauthTokenProvider = mock(),
    )

    @Test
    fun getBlock() = runTest {
        val result = api.getBlock(
            blockId = "562bd390-2cef-457d-b4df-f1b60863f964",
            accessToken = Utils.NOTION_API_TOKEN,
        )
        assertThat(result.childPage?.title).isEqualTo("Document for Testing Notion")
        assertThat(result.parent).isEqualTo(
            Parent(
                type = "database_id",
                databaseId = UUID.fromString("60aa1097-93ce-4859-b006-41f7a7cbe683"),
            ),
        )
    }

    @Test
    fun getBlockChildren() = runTest {
        val result = api.getBlockChildren(
            blockId = "04b2114c-218b-49b4-b400-8db03adfbf3a",
            accessToken = Utils.NOTION_API_TOKEN,
        )
        assertThat(result.results).hasSize(12)
    }

    @Test
    fun headBlockChildren() = runTest {
        assertThat(
            api.headBlockChildren(
                blockId = "04b2114c-218b-49b4-b400-8db03adfbf3a",
                accessToken = Utils.NOTION_API_TOKEN,
            ),
        ).isEqualTo(HttpStatusCode.OK)

        assertThat(
            api.headBlockChildren(
                blockId = "c17a9e05d8e5430fbe5df2291b8f2adf",
                accessToken = Utils.NOTION_API_TOKEN,
            ),
        ).isEqualTo(HttpStatusCode.NotFound)
    }
}
