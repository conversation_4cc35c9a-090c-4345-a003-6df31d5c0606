package com.nextchaptersoftware.cohere.api.utils

import com.nextchaptersoftware.cohere.api.CohereApiConfiguration
import com.nextchaptersoftware.cohere.api.CohereApiProvider
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl

object CohereTestUtils {
    private val COHERE_API_CONFIGURATION = CohereApiConfiguration(
        baseApiUri = GlobalConfig.INSTANCE.cohere.baseApiUri.asUrl,
        completionTimeout = GlobalConfig.INSTANCE.cohere.completionTimeout,
        rerankTimeout = GlobalConfig.INSTANCE.cohere.rerankTimeout,
        token = GlobalConfig.INSTANCE.cohere.apiKey,
    )

    val COHERE_API_PROVIDER by lazy {
        CohereApiProvider(config = COHERE_API_CONFIGURATION)
    }
}
