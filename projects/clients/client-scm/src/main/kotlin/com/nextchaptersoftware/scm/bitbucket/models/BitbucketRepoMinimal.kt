package com.nextchaptersoftware.scm.bitbucket.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BitbucketRepoMinimal(
    val uuid: String,

    @SerialName("full_name")
    val fullName: String,

    @SerialName("is_private")
    val isPrivate: Boolean,

    val workspace: BitbucketWorkspace,
    val owner: BitbucketRepoOwner,

    @SerialName("links")
    val links: BitbucketLinks,
)
