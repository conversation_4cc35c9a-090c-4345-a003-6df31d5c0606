package com.nextchaptersoftware.scm.apps

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.cache.RedisObjectCache
import io.lettuce.core.ExperimentalLettuceCoroutinesApi
import java.util.UUID

@OptIn(ExperimentalLettuceCoroutinesApi::class)
class AppManifestNegotiationStore(
    private val cache: RedisObjectCache = RedisObjectCache(namespace = "AppManifestNegotiation"),
) {
    suspend fun saveState(negotiation: AppManifestNegotiation): UUID {
        return cache.set(negotiation.encode())
    }

    suspend fun getState(state: UUID): AppManifestNegotiation? {
        return cache.get(state)?.decode()
    }
}
