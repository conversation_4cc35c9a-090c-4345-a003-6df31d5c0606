package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData

object AzureDevOpsRepositories {

    private val decoder = { text: String -> text.decode<AzureDevOpsRepository>() }

    val Unblocked = testData(
        file = "/scm/azure-devops/repo-unblocked.json",
        decoder = decoder,
    )
}
