package com.nextchaptersoftware.scm.bitbucketdatacenter.models

import com.nextchaptersoftware.test.utils.TestDataFactory
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ArgumentsSource

internal class BitbucketDataCenterUserTest {

    class Users : TestDataFactory<BitbucketDataCenterUser>(
        BitbucketDataCenterUsers::Admin,
        BitbucketDataCenterUsers::Reader,
        BitbucketDataCenterUsers::Writer,
        BitbucketDataCenterUsers::State_Active,
        BitbucketDataCenterUsers::State_Deleted,
        BitbucketDataCenterUsers::State_Anonymized,
    )

    @ParameterizedTest
    @ArgumentsSource(Users::class)
    fun `parse user`(make: () -> BitbucketDataCenterUser) {
        val user = make()
        assertThat(user).isNotNull()
    }
}
