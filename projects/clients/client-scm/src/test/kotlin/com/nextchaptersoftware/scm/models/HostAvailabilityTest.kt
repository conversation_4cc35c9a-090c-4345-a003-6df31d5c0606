@file:Suppress("ktlint:nextchaptersoftware:no-test-delay-expression-rule")

package com.nextchaptersoftware.scm.models

import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.network.sockets.ConnectTimeoutException
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.ktor.client.request.get
import io.ktor.http.HttpStatusCode
import io.ktor.network.sockets.SocketTimeoutException
import io.ktor.utils.io.ByteReadChannel
import java.security.cert.CertPathBuilderException
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class HostAvailabilityTest {

    private class ApiClient(engine: HttpClientEngine) {
        private val httpClient = HttpClient(engine) {
            expectSuccess = true
        }

        suspend fun apiCall(): String = httpClient.get("https://some.mock.endpoint/").body()
    }

    @Test
    fun `is UnknownIssue when isEnterpriseHost is false`() = runTest {
        HostAvailability.checkAvailability("google.com") { false }.also { status ->
            assertThat(status).isInstanceOf(HostAvailabilityStatus.UnknownIssue::class.java)
        }
    }

    @Test
    fun `is Ok when isEnterpriseHost is true`() = runTest {
        HostAvailability.checkAvailability("google.com") { true }.also { status ->
            assertThat(status).isEqualTo(HostAvailabilityStatus.Ok)
        }
    }

    @Test
    fun `is HostnameNotFound when hostname does not exist`() = runTest {
        HostAvailability.checkAvailability("DoesNotExist0987654321.com") { true }.also { status ->
            assertThat(status).isEqualTo(HostAvailabilityStatus.HostnameNotFound)
        }
    }

    @Test
    fun `is HostnameNotFound when loopback`() = runTest {
        HostAvailability.checkAvailability("localhost") { true }.also { status ->
            assertThat(status).isEqualTo(HostAvailabilityStatus.HostnameNotFound)
        }
    }

    @Test
    fun `is HostTemporarilyUnavailable when 5xx`() = runTest {
        listOf(
            HttpStatusCode.BadGateway,
            HttpStatusCode.GatewayTimeout,
            HttpStatusCode.InsufficientStorage,
            HttpStatusCode.NotImplemented,
            HttpStatusCode.ServiceUnavailable,
        ).forEach { statusCode ->
            checkMockResponse(
                withStatusCode = statusCode,
                expectHostStatus = HostAvailabilityStatus.HostTemporarilyUnavailable(statusCode),
            )
        }
    }

    @Test
    fun `is HostTemporarilyUnavailable when 500`() = runTest {
        checkMockResponse(
            withStatusCode = HttpStatusCode.InternalServerError,
            expectHostStatus = HostAvailabilityStatus.UnknownIssue("Unexpected enterprise host"),
        )
    }

    @Test
    fun `is HostNotReachable when SocketTimeoutException`() = runTest {
        checkFailedRequest(
            withException = SocketTimeoutException(""),
            expectHostStatus = HostAvailabilityStatus.HostNotReachable,
        )
    }

    @Test
    fun `is HostNotReachable when ConnectTimeoutException`() = runTest {
        checkFailedRequest(
            withException = ConnectTimeoutException(""),
            expectHostStatus = HostAvailabilityStatus.HostNotReachable,
        )
    }

    @Test
    fun `is HostNotReachable when HttpRequestTimeoutException`() = runTest {
        checkFailedRequest(
            withException = HttpRequestTimeoutException("", 3L),
            expectHostStatus = HostAvailabilityStatus.HostNotReachable,
        )
    }

    @Test
    fun `is InvalidSslCertificate when CertPathBuilderException`() = runTest {
        checkFailedRequest(
            withException = CertPathBuilderException("self signed certificate"),
            expectHostStatus = HostAvailabilityStatus.InvalidSslCertificate,
        )
    }

    @Test
    fun `is HostNotReachable when TimeoutCancellationException`() = runTest {
        suspend fun isEnterpriseHost(): Boolean {
            withTimeout(1.milliseconds) {
                delay(1000.milliseconds)
            }
            return true
        }

        HostAvailability.checkAvailability("google.com") { isEnterpriseHost() }.also { status ->
            assertThat(status).isEqualTo(HostAvailabilityStatus.HostNotReachable)
        }
    }

    private suspend fun checkMockResponse(
        withStatusCode: HttpStatusCode,
        expectHostStatus: HostAvailabilityStatus,
        dispatcher: CoroutineDispatcher = Dispatchers.Default,
    ) {
        suspend fun isEnterpriseHost(): Boolean {
            ApiClient(
                MockEngine {
                    respond(
                        content = ByteReadChannel(""),
                        status = withStatusCode,
                    )
                },
            ).apiCall()
            return true
        }

        withContext(dispatcher) {
            HostAvailability.checkAvailability("google.com") { isEnterpriseHost() }.also { status ->
                assertThat(status).isEqualTo(expectHostStatus)
            }
        }
    }

    private suspend fun checkFailedRequest(withException: Exception, expectHostStatus: HostAvailabilityStatus) {
        fun isEnterpriseHost(): Boolean {
            throw withException
        }

        HostAvailability.checkAvailability("google.com") { isEnterpriseHost() }.also { status ->
            assertThat(status).isEqualTo(expectHostStatus)
        }
    }
}
