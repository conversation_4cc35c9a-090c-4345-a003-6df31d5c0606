package com.nextchaptersoftware.activemq

import com.nextchaptersoftware.activemq.extensions.MessageExtensions.body
import com.nextchaptersoftware.activemq.models.messageProperties
import java.util.UUID
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.toJavaDuration
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ActiveMQGroupTest {

    @Test
    fun `test message group affinity`() {
        val queueName = "test-queue-${UUID.randomUUID()}"
        val producer = ActiveMQProducer.producer(queueName)
        val consumers = (1..7).map {
            ActiveMQConsumer.consumer(queueName, transacted = false)
        }

        val numTrials = 23

        repeat(numTrials) {
            producer.sendMessage("Message for group 1", properties = messageProperties { withJMSXGroupId(groupId = "1") })
            producer.sendMessage("Message for group 2", properties = messageProperties { withJMSXGroupId(groupId = "2") })
            producer.sendMessage("Message not grouped")
        }

        val consumersForGroup = mutableMapOf<String, MutableList<Int>>()
        while (consumersForGroup.values.sumOf { it.size } < numTrials * 3) {
            consumers.forEachIndexed { consumerId, consumer ->
                consumer.receive(10.milliseconds.toJavaDuration())?.also { message ->

                    val body = message.body()
                    val groupId = message.getStringProperty("JMSXGroupID") ?: "null"
                    when (groupId) {
                        "1" -> assertThat(body).isEqualTo("Message for group 1")
                        "2" -> assertThat(body).isEqualTo("Message for group 2")
                        else -> assertThat(body).isEqualTo("Message not grouped")
                    }
                    consumersForGroup[groupId] = (consumersForGroup[groupId] ?: mutableListOf())
                    consumersForGroup[groupId]?.add(consumerId)
                }
            }
        }

        assertThat(consumersForGroup["1"]).hasSize(numTrials)
        assertThat(consumersForGroup["2"]).hasSize(numTrials)
        assertThat(consumersForGroup["null"]).hasSize(numTrials)

        assertThat(consumersForGroup["1"]?.distinct()).hasSize(1)
        assertThat(consumersForGroup["2"]?.distinct()).hasSize(1)
        assertThat(consumersForGroup["null"]?.distinct()).hasSizeGreaterThan(1)
    }
}
