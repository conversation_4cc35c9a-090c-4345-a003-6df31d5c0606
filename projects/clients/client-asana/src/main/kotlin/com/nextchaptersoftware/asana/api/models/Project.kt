package com.nextchaptersoftware.asana.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("project")
data class Project(
    override val gid: String,
    override val name: String? = null,
    @SerialName("resource_type")
    override val resourceType: String = "project",
    val archived: Boolean? = null,
    val color: String? = null,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("current_status")
    val currentStatus: ProjectStatus? = null,
    @SerialName("current_status_update")
    val currentStatusUpdate: StatusUpdate? = null,
    @SerialName("custom_field_settings")
    val customFieldSettings: List<CustomFieldSetting>? = null,
    @SerialName("default_view")
    val defaultView: String? = null,
    @SerialName("due_date")
    val dueDate: String? = null,
    @SerialName("due_on")
    val dueOn: String? = null,
    @SerialName("html_notes")
    val htmlNotes: String? = null,
    val members: List<User>? = null,
    @SerialName("modified_at")
    val modifiedAt: String? = null,
    val notes: String? = null,
    @SerialName("privacy_setting")
    val privacySetting: String? = null,
    @SerialName("start_on")
    val startOn: String? = null,
    @SerialName("default_access_level")
    val defaultAccessLevel: String? = null,
    @SerialName("minimum_access_level_for_customization")
    val minimumAccessLevelForCustomization: String? = null,
    @SerialName("minimum_access_level_for_sharing")
    val minimumAccessLevelForSharing: String? = null,
    @SerialName("custom_fields")
    val customFields: List<CustomField>? = null,
    val completed: Boolean? = null,
    @SerialName("completed_at")
    val completedAt: String? = null,
    @SerialName("completed_by")
    val completedBy: User? = null,
    val followers: List<User>? = null,
    val owner: User? = null,
    val team: Team? = null,
    val icon: String? = null,
    @SerialName("permalink_url")
    val permalinkUrl: String? = null,
    @SerialName("project_brief")
    val projectBrief: ProjectBrief? = null,
    @SerialName("created_from_template")
    val createdFromTemplate: ProjectTemplate? = null,
    val workspace: Reference? = null,
) : NamedResource()

@Serializable
data class ProjectStatus(
    override val gid: String,
    @SerialName("resource_type")
    override val resourceType: String,
    val title: String? = null,
    val text: String? = null,
    @SerialName("html_text")
    val htmlText: String? = null,
    val color: String? = null,
    val author: User? = null,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("created_by")
    val createdBy: User? = null,
    @SerialName("modified_at")
    val modifiedAt: String? = null,
) : Resource()

@Serializable
data class StatusUpdate(
    override val gid: String,
    @SerialName("resource_type")
    override val resourceType: String,
    val title: String? = null,
    @SerialName("resource_subtype")
    val resourceSubtype: String? = null,
) : Resource()

@Serializable
data class CustomFieldSetting(
    override val gid: String,
    @SerialName("resource_type")
    override val resourceType: String,
    val project: Reference? = null,
    @SerialName("is_important")
    val isImportant: Boolean? = null,
    val parent: Reference? = null,
    @SerialName("custom_field")
    val customField: CustomField? = null,
) : Resource()

@Serializable
data class ProjectBrief(
    override val gid: String,
    @SerialName("resource_type")
    override val resourceType: String,
) : Resource()

@Serializable
data class ProjectTemplate(
    override val gid: String,
    @SerialName("resource_type")
    override val resourceType: String,
     val name: String,
) : Resource()
