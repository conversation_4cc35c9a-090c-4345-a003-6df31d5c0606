package com.nextchaptersoftware.asana.api.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
@Suppress("CyclomaticComplexMethod", "NestedBlockDepth")
data class HumanReadableAsanaTask(
    val gid: String,
    val name: String,
    val notes: String = "",
    val fields: Map<String, String>,
    val stories: List<HumanReadableAsanaStory>,
    val customFields: Map<String, String>,
) {
    val title: String
        get() = name

    companion object {
        /**
         * Creates a HumanReadableAsanaTask from a JsonObject representation of an Asana task
         */
        fun fromJsonTask(jsonObject: JsonObject): HumanReadableAsanaTask {
            // Extract the data object which contains the actual task
            val dataJson = jsonObject["data"]

            // Convert to Task model
            val task = when (dataJson != null) {
                true -> dataJson.encode().decode<Task>()
                else -> jsonObject.encode().decode<Task>()
            }

            // Extract the basic fields
            val gid = task.gid
            val name = requireNotNull(task.name) {
                "Task with gid $gid has no name"
            }
            val notes = task.notes ?: ""

            val fields = mutableMapOf<String, String>()
            val customFields = mutableMapOf<String, String>()

            // Process regular fields
            addBasicFields(task, fields)

            // Process special relations
            addSpecialFields(task, fields)

            addCustomFields(task, customFields)

            val stories = task.stories
                ?.mapNotNull { story -> HumanReadableAsanaStory.fromStory(story) }
                ?: emptyList()

            return HumanReadableAsanaTask(
                gid = gid,
                name = name,
                notes = notes,
                fields = fields.toSortedMap(),
                stories = stories,
                customFields = customFields,
            )
        }

        private fun addBasicFields(task: Task, fields: MutableMap<String, String>) {
            // Add simple fields
            addIfNotEmpty(fields, "Assignment Status", task.assigneeStatus)
            addIfNotEmpty(fields, "Created", task.createdAt)
            addIfNotEmpty(fields, "Status", task.completed?.toString())
            addIfNotEmpty(fields, "Completed Date", task.completedAt)
            addIfNotEmpty(fields, "Due Date", task.dueOn)
            addIfNotEmpty(fields, "Due At", task.dueAt)
            addIfNotEmpty(fields, "Modified", task.modifiedAt)
            addIfNotEmpty(fields, "Start At", task.startAt)
            addIfNotEmpty(fields, "Start Date", task.startOn)

            // Add permalink
            addIfNotEmpty(fields, "URL", task.permalinkURL)
        }

        private fun addCustomFields(task: Task, customFields: MutableMap<String, String>) {
            task.customFields?.forEach { customField ->
                val name = customField.name ?: return@forEach

                when (customField.type) {
                    "text" -> {
                        addIfNotEmpty(customFields, name, customField.textValue)
                    }

                    "number" -> {
                        customField.numberValue?.let { value ->
                            val formattedValue = customField.displayValue ?: when (value == value.toInt().toDouble()) {
                                 true -> value.toInt().toString()
                                 else -> value.toString()
                            }
                            addIfNotEmpty(customFields, name, formattedValue)
                        }
                    }

                    "enum" -> {
                        addIfNotEmpty(customFields, name, customField.enumValue?.name)
                    }

                    "multi_enum" -> {
                        val optionNames = customField.multiEnumValues?.mapNotNull { it.name } ?: emptyList()
                        addIfNotEmpty(customFields, name, optionNames.takeIf { it.isNotEmpty() }?.joinToString(", "))
                    }

                    "date" -> {
                        customField.dateValue?.let { dateValue ->
                            addIfNotEmpty(customFields, name, dateValue.dateTime ?: dateValue.date)
                        }
                    }

                    "people" -> {
                        val peopleNames = customField.peopleValue?.mapNotNull { it.name } ?: emptyList()
                        addIfNotEmpty(customFields, name, peopleNames.takeIf { it.isNotEmpty() }?.joinToString(", "))
                    }

                    else -> {
                        addIfNotEmpty(customFields, name, customField.displayValue)
                    }
                }
            }
        }

        private fun addSpecialFields(task: Task, fields: MutableMap<String, String>) {
            // Handle projects
            task.projects?.let { projects ->
                projects.mapNotNull { it.name }
                    .takeIf { it.isNotEmpty() }
                    ?.joinToString(", ")
                    ?.let { projectNames ->
                        val fieldName = "Projects"
                        fields[fieldName] = projectNames
                    }
            }

            // Handle sections from memberships
            task.memberships?.let { memberships ->
                memberships.mapNotNull { it.section?.name }
                    .distinct()
                    .takeIf { it.isNotEmpty() }
                    ?.joinToString(", ")
                    ?.let { sectionNames ->
                        val fieldName = "Sections"
                        fields[fieldName] = sectionNames
                    }
            }

            // Handle assignee
            task.assignee?.let { assignee ->
                assignee.name?.let { name ->
                    val fieldName = "Assigned To"
                    fields[fieldName] = name
                }
            }

            // Handle workspace
            task.workspace?.let { workspace ->
                workspace.name?.let { name ->
                    val fieldName = "Workspace"
                    fields[fieldName] = name
                }
            }

            // Handle tags
            // task.tags?.let { tags ->
            //     tags.mapNotNull { it.name }
            //         .takeIf { it.isNotEmpty() }
            //         ?.joinToString(", ")
            //         ?.let { tagNames ->
            //             val fieldName = "Tags"
            //             fields[fieldName] = tagNames
            //         }
            // }

            // Handle followers
            task.followers?.let { followers ->
                followers.mapNotNull { it.name }
                    .takeIf { it.isNotEmpty() }
                    ?.joinToString(", ")
                    ?.let { followerNames ->
                        val fieldName = "Followers"
                        fields[fieldName] = followerNames
                    }
            }
        }

        private fun addIfNotEmpty(map: MutableMap<String, String>, key: String, value: String?) {
            if (!value.isNullOrEmpty()) {
                map[key] = value
            }
        }
    }

    /**
     * Converts the task to a Markdown representation
     */
    fun asMarkdown(
        showDescription: Boolean = true,
        showCustomFields: Boolean = true,
        showStories: Boolean = true,
    ): String {
        val sb = StringBuilder()

        // Task title (gid + name)
        sb.appendLine("# [$gid] $name")
        sb.appendLine()

        // Description
        if (showDescription && notes.isNotEmpty()) {
            sb.appendLine(notes)
            sb.appendLine()
        }

        // Standard fields
        if (fields.isNotEmpty()) {
            sb.appendLine("## Fields")
            sb.appendLine()
            fields.forEach { (key, value) ->
                if (value.isNotEmpty()) {
                    sb.appendLine(" - $key: $value")
                }
            }
            sb.appendLine()
        }

        // Custom fields
        if (showCustomFields && customFields.isNotEmpty()) {
            sb.appendLine("## Custom Fields")
            sb.appendLine()
            customFields.forEach { (key, value) ->
                if (value.isNotEmpty()) {
                    sb.appendLine(" - $key: $value")
                }
            }
            sb.appendLine()
        }

        // Stories
        if (showStories && stories.isNotEmpty()) {
            sb.appendLine()
            sb.appendLine("## Stories")
            sb.appendLine()
            sb.appendLine(stories.joinToString("\n") { it.asMarkdown() })
        }
        return sb.toString().trimEnd()
    }
}
