package com.nextchaptersoftware.user.secret

import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.sksamuel.hoplite.Secret
import java.util.UUID
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock

class UserSecretServiceTest {
    private val keyPair = RSACryptoSystem.generateRSAKeyPair()

    private val encryption = RSACryptoSystem.RSAEncryption(
        publicKey = keyPair.publicKey,
        modulusBitLength = 4096,
    )

    private val decryption = RSACryptoSystem.RSADecryption(
        privateKey = keyPair.privateKey,
        modulusBitLength = 4096,
    )

    private val tokens = OAuthTokens(
        accessToken = Secret(UUID.randomUUID().toString()),
        refreshToken = Secret(UUID.randomUUID().toString()),
        accessTokenExpiresAt = Instant.nowWithMicrosecondPrecision().plus(72.hours),
        refreshTokenExpiresAt = Instant.nowWithMicrosecondPrecision().plus(180.days),
    )

    @Test
    fun `encrypt and decrypt`() {
        val userSecretService = UserSecretService(
            identityStore = mock(),
            encryption = encryption,
            decryption = decryption,
        )

        val encrypted = userSecretService.encrypt(tokens.accessToken)
        val decrypted = userSecretService.decrypt(encrypted)

        assertThat(decrypted).isEqualTo(tokens.accessToken)
    }

    @Test
    fun `encryptTokens and decryptTokens`() {
        val userSecretService = UserSecretService(
            identityStore = mock(),
            encryption = encryption,
            decryption = decryption,
        )

        val encrypted = userSecretService.encryptTokens(tokens)
        val decrypted = userSecretService.decryptTokens(encrypted)

        assertThat(decrypted).isEqualTo(tokens)
    }
}
