package com.nextchaptersoftware.anthropic.api.utils

import com.nextchaptersoftware.anthropic.api.models.AnthropicChatMessage
import com.nextchaptersoftware.anthropic.api.models.AnthropicChatRole
import com.nextchaptersoftware.anthropic.api.models.AnthropicMessageBundle
import com.nextchaptersoftware.utils.nullIfBlank

object AnthropicChatCompletionPromptParser {
    private const val JSON_OUTPUT_PRIMER = "I will now output the JSON and then stop. Here is the JSON: {"

    fun parse(prompt: String): List<AnthropicChatMessage> {
        val tokens = AnthropicChatRole.tokens
        var tempPrompt = prompt
        val chatMessages = mutableListOf<AnthropicChatMessage>()

        @Suppress("LoopWithTooManyJumpStatements")
        while (tempPrompt.isNotEmpty()) {
            val tokensInPrompt = tokens.filter { tempPrompt.contains(it) }
            if (tokensInPrompt.isEmpty()) {
                break
            }

            val leastToken = tokensInPrompt.minByOrNull { tempPrompt.indexOf(it) } ?: break
            val leastTokenIndex = tempPrompt.indexOf(leastToken)
            val nextTokenIndex = tokensInPrompt.minOf {
                tempPrompt.indexOf(it, leastTokenIndex + leastToken.length).takeUnless { index ->
                    index == -1
                } ?: tempPrompt.length
            }

            leastToken.asRole?.let { role ->
                val message = tempPrompt.substring(leastTokenIndex + leastToken.length, nextTokenIndex).trim()
                chatMessages.add(
                    AnthropicChatMessage(
                        role = role,
                        content = message,
                    ),
                )
            }

            tempPrompt = tempPrompt.substring(nextTokenIndex)
        }

        return chatMessages
    }

    fun messageBundleFromPrompt(
        prompt: String,
        respondWithJson: Boolean,
    ): AnthropicMessageBundle {
        val messages = parse(prompt)
        return messageBundleForMessages(messages, respondWithJson)
    }

    fun messageBundleForMessages(
        messages: List<AnthropicChatMessage>,
        respondWithJson: Boolean,
    ): AnthropicMessageBundle {
        val (systemMessages, otherMessages) = messages.filter {
            it.content.isNotBlank()
        }.partition {
            it.role == AnthropicChatRole.SYSTEM
        }
        val systemPrompt = systemMessages.joinToString("\n") { it.content }.nullIfBlank()
        val messagesStartingWithUser = otherMessages.dropWhile { it.role != AnthropicChatRole.USER }
        val mergedMessages = mergeConsecutiveRoleMessages(messagesStartingWithUser)
        val chatMessages = if (respondWithJson) {
            val lastMessage = mergedMessages.lastOrNull()
            if (lastMessage?.role == AnthropicChatRole.ASSISTANT) {
                mergedMessages.dropLast(1) + lastMessage.copy(
                    content = "${lastMessage.content}\n\n$JSON_OUTPUT_PRIMER",
                )
            } else {
                mergedMessages + AnthropicChatMessage(
                    role = AnthropicChatRole.ASSISTANT,
                    content = JSON_OUTPUT_PRIMER,
                )
            }
        } else {
            mergedMessages
        }
        return AnthropicMessageBundle(
            systemPrompt = systemPrompt,
            chatMessages = chatMessages,
        )
    }

    private fun mergeConsecutiveRoleMessages(
        messages: List<AnthropicChatMessage>,
    ): List<AnthropicChatMessage> {
        val mergedMessages = mutableListOf<AnthropicChatMessage>()
        var previousMessage: AnthropicChatMessage? = null
        for (message in messages) {
            if (previousMessage != null && previousMessage.role == message.role) {
                val mergedContent = "${previousMessage.content}\n\n${message.content}"
                previousMessage = previousMessage.copy(content = mergedContent)
            } else {
                previousMessage?.let { mergedMessages.add(it) }
                previousMessage = message
            }
        }
        previousMessage?.let { mergedMessages.add(it) }
        return mergedMessages
    }
}

val AnthropicChatRole.asToken: String
    get() = "[[${this.role.uppercase()}]]"

val AnthropicChatRole.Companion.roles: List<AnthropicChatRole>
    get() = listOf(
        SYSTEM,
        USER,
        ASSISTANT,
    )

val AnthropicChatRole.Companion.tokens: List<String>
    get() = AnthropicChatRole.roles.map { it.asToken }

val String.asRole: AnthropicChatRole?
    get() = AnthropicChatRole.roles.firstOrNull { this == it.asToken }
