package com.nextchaptersoftware.kotlinx.coroutines

import com.nextchaptersoftware.config.GlobalConfig
import kotlinx.coroutines.Dispatchers

private val DB_DISPATCHER = Dispatchers.IO.limitedParallelism(GlobalConfig.INSTANCE.coroutine.dbParallelism)

private val ASYNC_STEP_DISPATCHER = Dispatchers.IO.limitedParallelism(GlobalConfig.INSTANCE.coroutine.asyncStepParallelism)

private val DOCUMENT_RETRIEVAL_DISPATCHER = Dispatchers.IO.limitedParallelism(GlobalConfig.INSTANCE.coroutine.documentRetrievalParallelism)

val Dispatchers.DB get() = DB_DISPATCHER
val Dispatchers.ASYNC_STEP get() = ASYNC_STEP_DISPATCHER
val Dispatchers.DOCUMENT_RETRIEVAL get() = DOCUMENT_RETRIEVAL_DISPATCHER
