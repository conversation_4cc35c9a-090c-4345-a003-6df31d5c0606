package com.nextchaptersoftware.kotlinx.coroutines.flow

import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat

@OptIn(ExperimentalCoroutinesApi::class)
inline fun <reified T> Flow<Flow<T>>.asFlatFlow(): Flow<T> = flatMapConcat { it }

@OptIn(ExperimentalCoroutinesApi::class)
inline fun <reified T> Flow<Iterable<T>>.asFlatItemsFlow(): Flow<T> = flatMapConcat { it.asFlow() }
