package com.nextchaptersoftware.dsac.filter

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Thread
import kotlin.collections.contains

class CompositeDsacFilter(
    private val dsacFilters: List<DsacFilter>,
    private val validInstallationIds: Set<InstallationId>,
) : DsacFilter {

    override suspend fun filterDocuments(
        orgId: OrgId,
        documents: List<MLTypedDocument>,
    ): List<MLTypedDocument> {
        val initial = documents.filter { it.installationId in validInstallationIds }

        return dsacFilters.fold(initial) { acc, dsacFilter ->
            dsacFilter.filterDocuments(orgId = orgId, documents = acc)
        }
    }

    override suspend fun filterThreads(
        orgId: OrgId,
        threads: List<Thread>,
    ): List<Thread> {
        val initial = threads.filter { it.installationId in validInstallationIds } // Note: threads with null installationIds are filtered out

        return dsacFilters.fold(initial) { acc, dsacFilter ->
            dsacFilter.filterThreads(orgId = orgId, threads = acc)
        }
    }
}
