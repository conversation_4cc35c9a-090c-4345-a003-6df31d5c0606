package com.nextchaptersoftware.dsac.filter

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.dsac.DsacContext

class AsanaDsacFilter(
    override val dsacContext: DsacContext,
    override val installationId: InstallationId,
) : InstallationDsacFilter() {

    override suspend fun filterInstallationDocuments(
        orgId: OrgId,
        installationId: InstallationId,
        documents: List<MLTypedDocument>,
    ): List<MLTypedDocument> {
        return documents // TODO ASANA implement document filtering
    }
}
