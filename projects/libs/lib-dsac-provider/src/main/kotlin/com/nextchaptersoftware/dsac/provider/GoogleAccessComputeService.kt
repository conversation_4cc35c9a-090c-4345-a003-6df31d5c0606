package com.nextchaptersoftware.dsac.provider

import com.nextchaptersoftware.db.models.GoogleDriveFile
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.GoogleDriveFileStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.MemberIdentityPair
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.google.api.GoogleApiProvider
import com.nextchaptersoftware.google.services.GoogleCredentialProvider
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import mu.KotlinLogging
import org.jetbrains.exposed.sql.AndOp
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList

private val LOGGER = KotlinLogging.logger {}

class GoogleAccessComputeService(
    private val googleCredentialProvider: GoogleCredentialProvider,
    private val googleApiProvider: GoogleApiProvider,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val googleDriveFileStore: GoogleDriveFileStore = Stores.googleDriveFileStore,
    private val memberStore: MemberStore = Stores.memberStore,
    private val accessComputeService: AccessComputeService = AccessComputeService(namespace = NAMESPACE),
) {
    companion object {
        private val NAMESPACE = this::class.java.simpleName

        private val providers = listOf(Provider.GoogleDrive, Provider.GoogleDriveWorkspace)
    }

    suspend fun invalidateForMembers(orgId: OrgId) {
        memberStore.findOrgAndMemberAndIdentities(
            where = AndOp(
                listOf(
                    InstallationModel.org eq orgId,
                    InstallationModel.provider inList providers,
                ),
            ),
        ).forEach { invalidateForMember(memberId = it.member.id) }
    }

    suspend fun invalidateForMember(memberId: MemberId) {
        runSuspendCatching {
            accessComputeService.invalidate(memberId)
        }.getOrElse {
            LOGGER.warnAsync(it) { "Error invalidating access for member" }
        }
    }

    suspend fun get(
        orgId: OrgId,
        memberIdentityPair: MemberIdentityPair,
    ): AccessResult.Google {
        return accessComputeService.getOrComputeAccess(
            orgId = orgId,
            memberIdentityPair = memberIdentityPair,
            computeAccess = ::computeAccess,
        ) as AccessResult.Google
    }

    suspend fun computeAccess(
        orgId: OrgId,
        memberIdentityPair: MemberIdentityPair,
    ): AccessResult.Google {
        val installation = installationStore.getIntegrationInstallations(
            orgId = orgId,
            providers = providers,
        ).firstOrNull {
            it.id == memberIdentityPair.member.installationId
        } ?: return AccessResult.Google(emptySet())

        val googleDriveFiles = googleDriveFileStore.list(installationId = installation.id)

        val allowedFiles = getAllowedFiles(
            installation = installation,
            identity = memberIdentityPair.identity,
            googleDriveFiles = googleDriveFiles,
        )

        return AccessResult.Google(googleDriveFileIds = allowedFiles.map { it.id }.toSet())
    }

    private suspend fun getAllowedFiles(
        installation: Installation,
        identity: Identity,
        googleDriveFiles: List<GoogleDriveFile>,
    ): List<GoogleDriveFile> {
        return runSuspendCatching {
            val credential = googleCredentialProvider.getCredential(installation = installation, identity = identity)

            val allowed = googleApiProvider.driveApi.filesByIds(
                client = googleApiProvider.driveApi.client(httpRequestInitializer = credential),
                fileIds = googleDriveFiles.map { it.googleDriveId },
            ).let { (files, errors) ->
                errors.forEach { error -> LOGGER.debugAsync("error" to error) { "Error fetching file" } }
                files.map { file -> file.id }.toSet()
            }

            googleDriveFiles.filter { it.googleDriveId in allowed }
        }.getOrElse {
            LOGGER.errorAsync(it) { "Error fetching folders for identity" }
            emptyList()
        }
    }
}
