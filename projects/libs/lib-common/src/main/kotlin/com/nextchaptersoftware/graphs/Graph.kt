package com.nextchaptersoftware.graphs

open class Graph<N : Node> {
    private val _nodes = mutableSetOf<N>()
    private val _edges = mutableSetOf<Edge<N>>()

    // [node] --edge-->
    private val sources = mutableMapOf<Edge<N>, N>()

    // --edge--> [node]
    private val targets = mutableMapOf<Edge<N>, N>()

    val size: Int
        get() = _nodes.size

    val nodes: Set<N>
        get() = _nodes.toSet()

    fun isEmpty(): Boolean = _nodes.isEmpty()

    fun hasNode(predicate: (N) -> Boolean): Boolean {
        return _nodes.any(predicate)
    }

    fun getNode(predicate: (N) -> Boolean): N? {
        return _nodes.find(predicate)
    }

    fun addNode(node: N): N {
        _nodes.add(node)
        return node
    }

    fun getSourcesOf(node: N): Set<N> {
        return targets.filter { it.value == node }.keys.map { it.source }.toSet()
    }

    fun getTargetsOf(node: N): Set<N> {
        return sources.filter { it.value == node }.keys.map { it.target }.toSet()
    }

    fun getAdjacentNodes(node: N): Set<N> {
        val sources = getSourcesOf(node)
        val targets = getTargetsOf(node)
        return sources + targets
    }

    val edges: Set<Edge<N>>
        get() = _edges.toSet()

    fun hasEdge(predicate: (Edge<N>) -> Boolean): Boolean {
        return _edges.any(predicate)
    }

    fun getEdge(predicate: (Edge<N>) -> Boolean): Edge<N>? {
        return _edges.find(predicate)
    }

    fun addEdge(source: N, target: N, label: Any? = null): Edge<N> {
        val edge = Edge(source, target, label = label)
        return addEdge(edge)
    }

    fun addEdge(edge: Edge<N>): Edge<N> {
        _edges.add(edge)
        _nodes.add(edge.source)
        _nodes.add(edge.target)
        sources[edge] = edge.source
        targets[edge] = edge.target
        return edge
    }

    fun getSourceEdges(node: N): Set<Edge<N>> {
        return sources.filter { it.value == node }.keys
    }

    fun getTargetEdges(node: N): Set<Edge<N>> {
        return targets.filter { it.value == node }.keys
    }
}

interface Node

data class Edge<N>(
    val source: N,
    val target: N,
    val label: Any? = null,
)
