package com.nextchaptersoftware.ml.completion.prompts.filter

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.ml.api.MachineLearningApiConfiguration
import com.nextchaptersoftware.ml.api.MachineLearningApiProvider
import com.nextchaptersoftware.ml.completion.MachineLearningCompletionService
import io.ktor.http.Url
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class SlackSummaryLLMContentFilterTest {

    private val filter = SlackSummaryLLMContentFilter(
        completionService = MachineLearningCompletionService(
            machineLearningApiProvider = MachineLearningApiProvider(
                config = MachineLearningApiConfiguration(
                    baseApiUri = Url(GlobalConfig.INSTANCE.machineLearning.defaultDeployment.baseApiUri),
                ),
            ),
        ),
    )

    private val somewhatTechnicalSummary = """
        On September 25th, 2024, at 2:19 PM, <PERSON> asked <PERSON><PERSON> if they were okay. <PERSON><PERSON> replied that the issue of repeating messages had been resolved and mentioned that changes were deploying soon. <PERSON> expressed his approval. Later, <PERSON>shin inquired about <PERSON>'s identity, to which Unblocked provided information about <PERSON>'s involvement with Next Chapter Software Org and his contributions to various projects and discussions. Unblocked also mentioned that Peter had been with the organization since at least April 2022. Finally, Peter requested a summary of the thread, and Unblocked began to compile it.
    """.trimIndent()

    private val helpSummary = """
        On September 17, 2024, Ben Ng reported issues with logging in and signing up, which were not working for him. He noted that the issue was specific to Safari, as it worked fine in Chrome. Kelly Watkins mentioned that it worked for her in Safari. Ben later clarified that Matt helped sort out the issue, which was caused by something going haywire with the Mac app.
    """.trimIndent()

    private val tacos = """
        On Tuesday at 10:19 PM, a conversation about lunch started. The main topic was a love for tacos, especially on Tuesdays. There was a suggestion to go to the park and have some tacos.
    """.trimIndent()

    private val offsite = """
        Dennis Pilarinos initiated a thread on August 27, 2024, to organize transportation for a team event the next day. He asked who could drive and how many people each could accommodate.
        - Dennis can drive 4 people.
        - Rashin Arab will drive himself due to a prior commitment but will wait for the team.
        - Adam Smythe offered discounted tickets and lunch.
        - Ben Ng, Peter Werry, Claire Gong, and Richard Bresnan each confirmed they could drive 4 people.
        - Jeff Ng mentioned he might not attend due to his dog's health.
        - Dennis calculated that they need rides for 12 people, requiring 3 cars. He confirmed that he, Ben Ng, and Peter Werry would drive.
        - Richard Bresnan later mentioned he would go directly to the start line instead of meeting at the office.
    """.trimIndent()

    private val weekendPlansConversation = """
        On October 5th, 2024, at 3:00 PM, Alice Smith and Bob Johnson discussed their weekend plans. Alice mentioned she was going hiking, while Bob said he was attending a music festival. They agreed to catch up on Monday to share stories.
    """.trimIndent()

    private val officePartyConversation = """
        On September 30th, 2024, the team chatted about the upcoming office party. Sarah Lee suggested a 1980s theme, and Mike Brown offered to handle the music playlist. Everyone was excited about dressing up and having fun.
    """.trimIndent()

    private val sportsEventConversation = """
        On October 2nd, 2024, at 11:00 AM, James Wilson and Emily Davis talked about the recent football match. James was thrilled about his team's victory, while Emily expressed disappointment over her team's loss. They debated the key moments of the game.
    """.trimIndent()

    private val lunchOptionsConversation = """
        On September 28th, 2024, the team discussed lunch options. They considered trying a new sushi place, but ultimately decided to order pizza. John Doe volunteered to place the order.
    """.trimIndent()

    private val holidaySchedulesConversation = """
        On October 1st, 2024, the staff coordinated their holiday schedules. Many planned to take time off during the winter holidays. The manager requested everyone to update their availability in the shared calendar.
    """.trimIndent()

    private val movieDiscussionConversation = """
        On September 29th, 2024, Karen Miller and Tom Anderson talked about the latest blockbuster movie. They shared their favorite scenes and speculated about a potential sequel.
    """.trimIndent()

    private val weatherConversation = """
        On October 3rd, 2024, the team chatted about the weather. It had been raining heavily, and some experienced flooding in their areas. They hoped for better weather in the coming days.
    """.trimIndent()

    private val hobbiesConversation = """
        On September 27th, 2024, at 4:00 PM, Laura Garcia and Mark Robinson discussed their hobbies. Laura talked about her painting classes, and Mark shared his interest in photography. They considered collaborating on a project.
    """.trimIndent()

    private val officeGossipConversation = """
        On October 4th, 2024, several team members engaged in office gossip. They speculated about a rumored merger and shared opinions about the new manager's style.
    """.trimIndent()

    private val travelPlansConversation = """
        On September 26th, 2024, at 2:00 PM, the team discussed travel plans. Jessica Adams was excited about her upcoming trip to Japan, and Michael Thompson shared tips from his previous visit.
    """.trimIndent()

    // Conversations that should not be skipped
    private val codingIssueConversation = """
        On October 5th, 2024, at 10:00 AM, Daniel Lee asked for assistance with a coding issue in the new application. He was encountering a NullPointerException when trying to save data. Maria Gonzalez suggested checking if the object was properly initialized. After some discussion, they identified that the database connection was not being established correctly. Daniel thanked Maria for her help.
    """.trimIndent()

    private val criticalBugConversation = """
        On September 30th, 2024, the development team discussed a critical bug affecting the login functionality. Alex Kim reported that users were unable to log in after the recent update. The team analyzed the issue and discovered that a recent change to the authentication service caused the problem. They decided to roll back the change and implement a fix.
    """.trimIndent()

    private val featureDeploymentConversation = """
        On October 2nd, 2024, at 3:00 PM, the team planned the deployment of a new feature. Samantha Clark outlined the deployment steps, and Jacob Harris raised concerns about potential downtime. They agreed to perform the deployment during off-peak hours and set up monitoring to quickly detect any issues.
    """.trimIndent()

    private val codeReviewConversation = """
        On September 28th, 2024, at 1:00 PM, Michael Brown requested a code review for his latest pull request. He had implemented a new caching mechanism to improve performance. Olivia White reviewed the code and suggested some optimizations. Michael updated the code accordingly, and the pull request was approved.
    """.trimIndent()

    private val apiIntegrationConversation = """
        On October 1st, 2024, the team organized a technical meeting to discuss the upcoming API integration. They outlined the requirements, set deadlines, and assigned tasks. Peter Johnson was tasked with designing the API endpoints, while Lisa Martinez would handle the documentation.
    """.trimIndent()

    private val machineLearningArticleConversation = """
        On September 29th, 2024, at 11:00 AM, Kevin Wilson shared an interesting article about new advancements in machine learning. The team discussed the implications of the technology and how it could be applied to their projects. They considered scheduling a workshop to explore the topic further.
    """.trimIndent()

    private val performanceOptimizationConversation = """
        On October 3rd, 2024, at 2:00 PM, the engineering team brainstormed solutions for optimizing the application's performance. They identified that database queries were causing slowdowns. Suggestions included indexing certain tables and refactoring complex queries. Action items were assigned to team members to implement these changes.
    """.trimIndent()

    private val serverIssuesConversation = """
        On September 27th, 2024, at 9:00 AM, Rachel Green reported issues with the server's response time. Joey Tribbiani suggested checking the server logs for errors. They discovered that a recent update caused memory leaks. The team worked on patching the issue and monitored the server's performance.
    """.trimIndent()

    private val softwareArchitectureConversation = """
        On October 4th, 2024, at 4:00 PM, the team discussed the software architecture for the new project. Monica Geller proposed using a microservices approach, while Chandler Bing suggested a monolithic architecture due to the project's scope. After weighing the pros and cons, they decided to proceed with microservices and scheduled further planning sessions.
    """.trimIndent()

    private val databaseQueryOptimizationConversation = """
        On September 26th, 2024, at 5:00 PM, Ross Geller asked for help with optimizing a database query that was taking too long to execute. Phoebe Buffay recommended using an EXPLAIN plan to analyze the query. Together, they identified unnecessary joins and optimized the query, significantly improving performance.
    """.trimIndent()

    private val mixedContentConversation = """
        On October 6th, 2024, at 10:00 AM, the team started by discussing the weekend football match. After a brief chat, John Doe brought up an issue with the latest software build failing on the CI server. Jane Smith suggested that it might be due to a misconfiguration in the build pipeline. They agreed to investigate further after lunch.
    """.trimIndent()

    private val technicalButVagueConversation = """
        On October 7th, 2024, at 2:00 PM, Alex Turner mentioned that the thing they were working on wasn't performing as expected. Sam Cooke responded that perhaps adjusting the parameters might help. They decided to run some tests to see if it would improve the results.
    """.trimIndent()

    private val nonTechnicalWithTechnicalJargonConversation = """
        On October 8th, 2024, at 3:00 PM, the team discussed the 'architecture' of their upcoming camping trip. They joked about 'deploying tents' and 'iterating' on their marshmallow roasting techniques. Everyone was excited about the 'project.'
    """.trimIndent()

    private val technicalConversationWithSecurityIssue = """
        On October 9th, 2024, at 4:00 PM, Emily Brown reported a security vulnerability that allowed unauthorized access to user data. David Wilson advised that this was a serious issue and that they should inform the security team immediately. They planned to patch the vulnerability and audit the system logs.
    """.trimIndent()

    private val longNonTechnicalConversationWithTechnicalSnippet = """
        On October 10th, 2024, at 5:00 PM, the team had a lengthy discussion about the upcoming company retreat. They planned activities, meals, and transportation. At the end of the conversation, Lisa Simpson mentioned, "By the way, the server is down." Bart Simpson replied, "I'll reboot it after the meeting."
    """.trimIndent()

    private val metaphoricalTechnicalConversation = """
        On October 11th, 2024, at 9:00 AM, the team discussed the 'bugs' in their garden. Sarah mentioned that the 'architecture' of the new flower beds wasn't allowing for proper water flow. Michael suggested 'debugging' the system by checking the soil composition. They planned to 'optimize' the garden layout over the weekend.
    """.trimIndent()

    private val foreignLanguageTechnicalConversation = """
        El 12 de octubre de 2024, a las 10:00 AM, Juan Pérez solicitó ayuda con un problema de código en su aplicación. María López sugirió revisar las conexiones a la base de datos. Después de algunos intentos, resolvieron el problema y Juan agradeció a María por su ayuda.
    """.trimIndent()

    private val encryptedTechnicalConversation = """
        On October 13th, 2024, at 11:00 AM, Alice and Bob exchanged messages using code words. Alice said, "The eagle flies at midnight." Bob replied, "But the serpent sleeps at dawn." They agreed that the 'package' would be delivered on time. Later, it was revealed they were coordinating a software release codenamed 'Eagle.'
    """.trimIndent()

    private val lengthyNonTechnicalWithEmbeddedCodeConversation = """
        On October 14th, 2024, at 2:00 PM, the team engaged in a lengthy discussion about the annual picnic. Amid the plans, David inserted a code snippet:

        ```
        def calculate_budget(expenses):
            total = sum(expenses)
            return total
        ```

        He joked that they'd need this to manage the picnic expenses. The team laughed and continued planning.
    """.trimIndent()

    private val technicalConversationWithPersonalContent = """
        On October 15th, 2024, at 3:00 PM, Emily and John discussed the new encryption algorithm for securing data transmissions. Midway, Emily mentioned she was getting married next month. John congratulated her, and they briefly talked about wedding plans before returning to finalize the encryption module details.
    """.trimIndent()

    @Disabled
    @Test
    fun `should skip when conversation is technical but doesn't have any meat to it`() = runTest {
        val skip = filter.shouldSkip(tacos)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should not skip when someone asks for help with a technical issue`() = runTest {
        val skip = filter.shouldSkip(helpSummary)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip when summary is somewhat technical`() = runTest {
        val skip = filter.shouldSkip(somewhatTechnicalSummary)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should skip conversation about an off site trip`() = runTest {
        val skip = filter.shouldSkip(offsite)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about weekend plans`() = runTest {
        val skip = filter.shouldSkip(weekendPlansConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about office party`() = runTest {
        val skip = filter.shouldSkip(officePartyConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about sports events`() = runTest {
        val skip = filter.shouldSkip(sportsEventConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about lunch options`() = runTest {
        val skip = filter.shouldSkip(lunchOptionsConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about holiday schedules`() = runTest {
        val skip = filter.shouldSkip(holidaySchedulesConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about movies`() = runTest {
        val skip = filter.shouldSkip(movieDiscussionConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about weather`() = runTest {
        val skip = filter.shouldSkip(weatherConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about hobbies`() = runTest {
        val skip = filter.shouldSkip(hobbiesConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip office gossip conversation`() = runTest {
        val skip = filter.shouldSkip(officeGossipConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip conversation about travel plans`() = runTest {
        val skip = filter.shouldSkip(travelPlansConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should not skip conversation about coding issue`() = runTest {
        val skip = filter.shouldSkip(codingIssueConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about critical bug`() = runTest {
        val skip = filter.shouldSkip(criticalBugConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about feature deployment`() = runTest {
        val skip = filter.shouldSkip(featureDeploymentConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about code review`() = runTest {
        val skip = filter.shouldSkip(codeReviewConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about API integration`() = runTest {
        val skip = filter.shouldSkip(apiIntegrationConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about machine learning article`() = runTest {
        val skip = filter.shouldSkip(machineLearningArticleConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about performance optimization`() = runTest {
        val skip = filter.shouldSkip(performanceOptimizationConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about server issues`() = runTest {
        val skip = filter.shouldSkip(serverIssuesConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about software architecture`() = runTest {
        val skip = filter.shouldSkip(softwareArchitectureConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation about database query optimization`() = runTest {
        val skip = filter.shouldSkip(databaseQueryOptimizationConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip conversation with mixed content`() = runTest {
        val skip = filter.shouldSkip(mixedContentConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should not skip technical but vague conversation`() = runTest {
        val skip = filter.shouldSkip(technicalButVagueConversation)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should skip non-technical conversation with technical jargon`() = runTest {
        val skip = filter.shouldSkip(nonTechnicalWithTechnicalJargonConversation)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should not skip conversation about security issue`() = runTest {
        val skip = filter.shouldSkip(technicalConversationWithSecurityIssue)
        assertThat(skip).isFalse
    }

    @Disabled
    @Test
    fun `should skip long non-technical conversation with technical snippet`() = runTest {
        val skip = filter.shouldSkip(longNonTechnicalConversationWithTechnicalSnippet)
        assertThat(skip).isTrue
    }

    @Disabled
    @Test
    fun `should skip metaphorical technical conversation`() = runTest {
        val skip = filter.shouldSkip(metaphoricalTechnicalConversation)
        assertThat(skip).isTrue // Expected to skip, but might not due to technical terms
    }

    @Disabled
    @Test
    fun `should not skip foreign language technical conversation`() = runTest {
        val skip = filter.shouldSkip(foreignLanguageTechnicalConversation)
        assertThat(skip).isFalse // May skip due to being in another language
    }

    @Disabled
    @Test
    fun `should not skip encrypted technical conversation`() = runTest {
        val skip = filter.shouldSkip(encryptedTechnicalConversation)
        assertThat(skip).isFalse // Might not recognize technical content
    }

    @Disabled
    @Test
    fun `should skip lengthy non-technical with embedded code conversation`() = runTest {
        val skip = filter.shouldSkip(lengthyNonTechnicalWithEmbeddedCodeConversation)
        assertThat(skip).isTrue // May skip due to predominant non-technical content
    }

    @Disabled
    @Test
    fun `should not skip technical conversation with personal content`() = runTest {
        val skip = filter.shouldSkip(technicalConversationWithPersonalContent)
        assertThat(skip).isFalse // Might skip due to personal content
    }
}
