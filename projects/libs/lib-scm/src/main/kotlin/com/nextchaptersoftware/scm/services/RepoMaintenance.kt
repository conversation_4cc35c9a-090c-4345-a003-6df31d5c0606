@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.scm.services

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequestIngestionModel
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.RepoModel
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.TeamSettingsStore
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.utils.RepoUrl
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.security.Hashing
import com.nextchaptersoftware.types.Hostname
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import mu.KotlinLogging
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.batchInsert
import org.jetbrains.exposed.sql.statements.BatchInsertStatement
import org.jetbrains.exposed.sql.update

private val LOGGER = KotlinLogging.logger {}

class RepoMaintenance(
    private val repoStore: RepoStore = Stores.repoStore,
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    private val teamSettingsStore: TeamSettingsStore = Stores.teamSettingsStore,
) {

    private fun sanitizeRepos(scmRepos: List<ScmRepository>): List<ScmRepository> {
        return scmRepos.filter {
            runSuspendCatching {
                RepoUrl.parseOrThrow(it.htmlUrl).canonicalHttpUrl
                true
            }.getOrElse { error ->
                LOGGER.errorSync(error, "externalId" to it.externalId) { "repo validation failed" }
                false
            }
        }
    }

    @Suppress("CyclomaticComplexMethod")
    private suspend fun upsertRepos(
        autoSelectNewSourceRepos: Boolean,
        scmTeam: ScmTeam,
        scmRepoBatch: List<ScmRepository>,
    ) {
        suspendedTransaction {
            val existingRepoExternalIds = RepoModel
                .select(RepoModel.externalId)
                .where {
                    RepoModel.scmTeam.eq(scmTeam.id) and RepoModel.externalId.inList(scmRepoBatch.map { it.externalId })
                }
                .map { it[RepoModel.externalId] }
                .toSet()

            val (existingScmRepos, newScmRepos) = sanitizeRepos(scmRepoBatch).partition { existingRepoExternalIds.contains(it.externalId) }

            if (newScmRepos.isNotEmpty()) {
                LOGGER.infoAsync(
                    "teamId" to scmTeam.id,
                    "autoSelectNewSourceRepos" to autoSelectNewSourceRepos,
                    "newRepos" to newScmRepos.joinToString(",") { it.externalId },
                ) { "Inserting new repos" }
            }
            val newRepos = insertNewRepos(this, scmTeam, newScmRepos, autoSelectNewSourceRepos)

            newPullRequestIngestion(trx = this, newRepos = newRepos, scmTeam = scmTeam)

            // Update existing repos
            if (existingScmRepos.isNotEmpty()) {
                LOGGER.infoAsync(
                    "teamId" to scmTeam.id,
                    "existingScmRepos" to existingScmRepos.joinToString(",") { it.externalId },
                ) { "Updating existing repos" }
            }

            existingScmRepos.forEach { scmRepository ->
                RepoDAO.find { (RepoModel.scmTeam eq scmTeam.id) and (RepoModel.externalId eq scmRepository.externalId) }.firstOrNull()
                    ?.let { dbRepo ->
                        withLoggingContextAsync("repoId" to scmRepository.externalId) {
                            runSuspendCatching {
                                extractRepoUrls(scmRepository).also { gitRepoUrls ->
                                    dbRepo.httpUrl = gitRepoUrls.httpUrl
                                    dbRepo.altHttpUrl = gitRepoUrls.altHttpUrl
                                    dbRepo.altSshUrl = gitRepoUrls.altSshUrl
                                }

                                dbRepo.createdAt = scmRepository.createdAt
                                dbRepo.lastActiveAt = scmRepository.lastActiveAt
                                dbRepo.externalOwner = scmRepository.repoOwner
                                dbRepo.externalName = scmRepository.repoName
                                dbRepo.externalAltName = scmRepository.repoAltName
                                dbRepo.isPublic = scmRepository.isPublic
                                dbRepo.isFork = scmRepository.isFork
                                dbRepo.parentExternalOwner = scmRepository.parentRepoOwner.takeIf { scmRepository.isFork }
                                dbRepo.parentExternalName = scmRepository.parentRepoName.takeIf { scmRepository.isFork }
                                scmRepository.isEmpty?.also { dbRepo.isEmpty = it }
                                dbRepo.hasWiki = scmRepository.hasWiki
                                dbRepo.isScmConnected = true
                            }.getOrElse { error ->
                                LOGGER.errorAsync(
                                    error,
                                    "externalId" to scmRepository.externalId,
                                ) { "Failed to update repo with externalId" }
                            }
                        }
                    }
            }
        }
    }

    private fun newPullRequestIngestion(trx: Transaction, newRepos: List<RepoDAO>, scmTeam: ScmTeam) {
        trx.run {
            PullRequestIngestionModel.batchInsert(
                data = newRepos,
                shouldReturnGeneratedValues = false,
            ) { repo ->
                this[PullRequestIngestionModel.repo] = repo.id

                // Lightweight ingestion only runs for GitHub and GitHub Enterprise.
                // For other providers, we assume that the initial ingestion has already completed.
                this[PullRequestIngestionModel.initialIngestionComplete] = when (scmTeam.provider) {
                    Provider.GitHub,
                    Provider.GitHubEnterprise,
                        -> false

                    Provider.AzureDevOps,
                    Provider.Bitbucket,
                    Provider.BitbucketDataCenter,
                    Provider.GitLab,
                    Provider.GitLabSelfHosted,
                        -> true

                    Provider.Asana,
                    Provider.Aws,
                    Provider.AwsIdentityCenter,
                    Provider.BitbucketPipelines,
                    Provider.Buildkite,
                    Provider.CircleCI,
                    Provider.Coda,
                    Provider.Confluence,
                    Provider.ConfluenceDataCenter,
                    Provider.CustomIntegration,
                    Provider.GenericSaml,
                    Provider.GitHubActions,
                    Provider.GoogleDrive,
                    Provider.GoogleDriveWorkspace,
                    Provider.GoogleWorkspace,
                    Provider.Jira,
                    Provider.JiraDataCenter,
                    Provider.Linear,
                    Provider.MicrosoftEntra,
                    Provider.Notion,
                    Provider.Okta,
                    Provider.PingOne,
                    Provider.Slack,
                    Provider.StackOverflowTeams,
                    Provider.Unblocked,
                    Provider.Web,
                        -> error("${scmTeam.provider.displayName} is not a valid provider for PullRequestIngestionModel")
                }
            }
        }
    }

    /**
     * Disconnects repos that are no longer in the list of current repos
     */
    private suspend fun disconnectRepos(
        teamId: ScmTeamId,
        currentRepoExternalIds: Set<String>,
    ) {
        suspendedTransaction {
            RepoModel.update(
                {
                    AllOp(
                        RepoModel.scmTeam eq teamId,
                        RepoModel.externalId notInList currentRepoExternalIds,
                        RepoModel.isScmConnected eq true,
                    )
                },
            ) {
                it[isScmConnected] = false
                it[isUserSelected] = false
                it[codeIngestLastStartAt] = null
            }
        }
    }

    suspend fun refreshRepositories(
        scmTeam: ScmTeam,
        allScmRepos: Flow<List<ScmRepository>>,
    ) {
        val autoSelectNewSourceRepos = teamSettingsStore.getAutoSelectNewSourceRepos(scmTeam.id)

        val currentRepoExternalIds = buildSet {
            allScmRepos
                .onEach { scmRepoBatch ->
                    upsertRepos(autoSelectNewSourceRepos, scmTeam, scmRepoBatch)
                    addAll(scmRepoBatch.map { it.externalId })
                }
                .collect()
        }

        disconnectRepos(teamId = scmTeam.id, currentRepoExternalIds = currentRepoExternalIds)
    }

    suspend fun insertNewRepos(
        trx: Transaction? = null,
        scmTeam: ScmTeam,
        scmRepositories: List<ScmRepository>,
        autoSelectNewSourceRepos: Boolean,
    ): List<RepoDAO> {
        val hasAnySelectedRepos = repoStore.hasAnySelectedRepos(teamId = scmTeam.id)

        return suspendedTransaction(trx) {
            RepoModel.batchInsert(scmRepositories) { scmRepository ->
                insertNewRepo(
                    scmTeam = scmTeam,
                    scmRepository = scmRepository,
                    autoSelectNewSourceRepos = autoSelectNewSourceRepos,
                    hasAnySelectedRepos = hasAnySelectedRepos,
                )
            }.map { RepoDAO.wrapRow(it) }
        }
    }

    /**
     * Chooses a repo to clean up, and then cleans it up.
     * Returns true if a repo was cleaned up, false otherwise.
     */
    suspend fun autoCleanupRepo(): Boolean {
        val repoToCleanup = repoStore.takeSomeDisconnectedRepo() ?: repoStore.takeSomeDirtyRepo()

        if (repoToCleanup == null) {
            return false
        }

        withLoggingContextAsync(
            "teamId" to repoToCleanup.teamId,
            "repoId" to repoToCleanup.id,
        ) {
            LOGGER.debugAsync { "Cleaning up repo" }
            cleanupRepo(teamId = repoToCleanup.teamId, repoId = repoToCleanup.id)
        }

        return true
    }

    /**
     * Removes the repo from Postgres if applicable.
     * Safely validates the repo state before cleanup.
     */
    private suspend fun cleanupRepo(teamId: ScmTeamId, repoId: RepoId) {
        val teamDisconnected = scmTeamStore.findById(teamId = teamId, includeDeleted = true)
            ?.let { it.isDeleted || !it.isScmInstalled }
            ?: true

        repoStore.findById(teamId = teamId, repoId = repoId, includeDeselected = true, includeDisconnected = true)?.also { repo ->
            when {
                (!repo.isScmConnected && !repo.isUserSelected) || teamDisconnected
                    -> {
                    deleteRepoResources(teamId = teamId, repoId = repoId)
                    LOGGER.infoAsync { "Deleting repo from Postgres" }
                    repoStore.deleteRepo(teamId = teamId, repoId = repoId)
                }

                !repo.isUserSelected -> {
                    deleteRepoResources(teamId = teamId, repoId = repoId)
                    repoStore.setCleanupNeeded(repoIds = listOf(repoId), cleanupNeeded = false)
                }

                else -> {
                    assert(!teamDisconnected)
                    assert(repo.isScmConnected)
                    assert(repo.isUserSelected)
                    repoStore.setCleanupNeeded(repoIds = listOf(repoId), cleanupNeeded = false)
                    LOGGER.infoAsync { "Repo is still in use, so clearing cleanupNeeded" }
                }
            }
        }
    }

    private suspend fun deleteRepoResources(teamId: ScmTeamId, repoId: RepoId) {
        LOGGER.infoAsync { "Deleting repo dependencies from Postgres" }
        Stores.threadPullRequestStore.deleteForRepo(repoId = repoId)
        Stores.gitHubIngestionStore.deleteForRepo(teamId = teamId, repoId = repoId)
        Stores.gitHubIssuesIngestionStore.deleteForRepo(teamId = teamId, repoId = repoId)
        Stores.pullRequestIngestionStore.deleteForRepo(repoId = repoId)
        Stores.topicStore.deleteForRepo(repoId = repoId)
        Stores.searchInsightStore.deleteForRepo(repoId = repoId)
        Stores.sourceMarkStore.deleteForRepo(repoId = repoId)
        Stores.pullRequestStore.deleteForRepo(repoId = repoId)
        Stores.threadStore.destructiveDeleteForRepo(repoId = repoId)
    }

    suspend fun deselectRepos(teamId: ScmTeamId, repoIds: List<RepoId>) {
        // mark isUserSelected = false
        repoStore.deselectRepos(teamId = teamId, repoIds = repoIds)

        // mark for cleanup (async)
        repoStore.setCleanupNeeded(repoIds, true)
    }

    private data class GitRepoUrls(
        val httpUrl: String,
        val altHttpUrl: String?,
        val altSshUrl: String?,
    )

    private fun extractRepoUrls(scmRepository: ScmRepository): GitRepoUrls {
        val preserveGitSuffix = scmRepository.repoName.endsWith(".git")
        val repoUrl = RepoUrl.parseOrThrow(scmRepository.htmlUrl, preserveGitSuffix)
        val altSshUrl = scmRepository.sshUrl?.let { RepoUrl.parseOrThrow(it, preserveGitSuffix) }
        val altHttpUrl = scmRepository.httpUrl?.let { RepoUrl.parseOrThrow(it, preserveGitSuffix) }
            ?.let {
                // Special case for Plaid GitHub Enterprise
                when (it.authority) {
                    "github.plaid.com" -> it.copy(host = Hostname.parse("github-ext.plaid.com"))
                    else -> it
                }
            }

        return GitRepoUrls(
            httpUrl = repoUrl.canonicalHttpUrl,
            altHttpUrl = when {
                altHttpUrl == null -> null
                repoUrl == altHttpUrl -> null
                else -> altHttpUrl.canonicalHttpUrl
            },
            altSshUrl = when {
                altSshUrl == null -> null
                repoUrl == altSshUrl -> null
                else -> altSshUrl.canonicalSshUrl
            },
        )
    }

    @Suppress("CyclomaticComplexMethod")
    private fun BatchInsertStatement.insertNewRepo(
        scmTeam: ScmTeam,
        scmRepository: ScmRepository,
        autoSelectNewSourceRepos: Boolean,
        hasAnySelectedRepos: Boolean,
    ) {
        val gitRepoUrls = extractRepoUrls(scmRepository)

        // TODO richie replace with real root hash
        val rootSha = Hashing.randomSha1().value

        // After an initial repos selection has been made,
        // auto-select new source repos (excluding archived, disabled, and forked repos).
        val selectRepo = when {
            scmRepository.isArchived -> false
            scmRepository.isDisabled -> false
            scmRepository.isFork -> false
            !autoSelectNewSourceRepos -> false
            scmTeam.providerLastRefreshedAt == null -> false
            !hasAnySelectedRepos -> false
            else -> true
        }

        this[RepoModel.createdAt] = scmRepository.createdAt
        this[RepoModel.externalId] = scmRepository.externalId
        this[RepoModel.externalName] = scmRepository.repoName
        this[RepoModel.externalAltName] = scmRepository.repoAltName
        this[RepoModel.externalOwner] = scmRepository.repoOwner
        this[RepoModel.httpUrl] = gitRepoUrls.httpUrl
        this[RepoModel.altHttpUrl] = gitRepoUrls.altHttpUrl
        this[RepoModel.altSshUrl] = gitRepoUrls.altSshUrl
        this[RepoModel.isPublic] = scmRepository.isPublic
        this[RepoModel.isFork] = scmRepository.isFork
        this[RepoModel.isEmpty] = scmRepository.isEmpty
        this[RepoModel.hasWiki] = scmRepository.hasWiki
        this[RepoModel.isScmConnected] = scmTeam.providerInstallationValid
        this[RepoModel.isUserSelected] = selectRepo
        this[RepoModel.languages] = scmRepository.languages.encode()
        this[RepoModel.lastActiveAt] = scmRepository.lastActiveAt
        this[RepoModel.provider] = scmTeam.provider
        this[RepoModel.rootHash] = rootSha
        this[RepoModel.scmTeam] = scmTeam.id
        this[RepoModel.parentExternalOwner] = if (scmRepository.isFork) scmRepository.parentRepoOwner else null
        this[RepoModel.parentExternalName] = if (scmRepository.isFork) scmRepository.parentRepoName else null
    }
}
