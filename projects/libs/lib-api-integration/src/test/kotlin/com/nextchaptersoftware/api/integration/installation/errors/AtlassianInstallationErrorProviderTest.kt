package com.nextchaptersoftware.api.integration.installation.errors

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.ConfluenceSpaceIngestionType
import com.nextchaptersoftware.db.models.JiraProjectIngestionType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.ConfluenceSiteStore
import com.nextchaptersoftware.db.stores.ConfluenceSpaceStore
import com.nextchaptersoftware.db.stores.JiraProjectStore
import com.nextchaptersoftware.db.stores.JiraSiteStore
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

class AtlassianInstallationErrorProviderTest {
    private val confluenceSiteStore = mock<ConfluenceSiteStore>()
    private val confluenceSpaceStore = mock<ConfluenceSpaceStore>()
    private val jiraSiteStore = mock<JiraSiteStore>()
    private val jiraProjectStore = mock<JiraProjectStore>()

    private val provider = AtlassianInstallationErrorProvider(
        confluenceSiteStore = confluenceSiteStore,
        confluenceSpaceStore = confluenceSpaceStore,
        jiraSiteStore = jiraSiteStore,
        jiraProjectStore = jiraProjectStore,
    )

    @Nested
    inner class Confluence {
        private val confluenceInstallation = MockDataClasses.installation(provider = Provider.Confluence)
        private val confluenceSite = MockDataClasses.confluenceSite(installationId = confluenceInstallation.id)

        @Test
        fun `isMissingConfiguration is true`() = runTest {
            `when`(confluenceSiteStore.findForInstallation(installationId = confluenceInstallation.id))
                .thenReturn(confluenceSite.copy(confluenceSpaceIngestionType = null))

            `when`(confluenceSpaceStore.countShouldIngest(confluenceSiteId = confluenceSite.id)).thenReturn(0)

            assertThat(provider.isMissingConfiguration(confluenceInstallation)).isTrue
        }

        @Test
        fun `isMissingConfiguration is false`() = runTest {
            `when`(confluenceSiteStore.findForInstallation(installationId = confluenceInstallation.id))
                .thenReturn(confluenceSite.copy(confluenceSpaceIngestionType = ConfluenceSpaceIngestionType.AllSpaces))

            `when`(confluenceSpaceStore.countShouldIngest(confluenceSiteId = confluenceSite.id)).thenReturn(0)

            assertThat(provider.isMissingConfiguration(confluenceInstallation)).isFalse
        }

        @Test
        fun `isMissingConfiguration is true for no teams selected`() = runTest {
            `when`(confluenceSiteStore.findForInstallation(installationId = confluenceInstallation.id))
                .thenReturn(confluenceSite.copy(confluenceSpaceIngestionType = ConfluenceSpaceIngestionType.SelectedSpacesOnly))

            `when`(confluenceSpaceStore.countShouldIngest(confluenceSiteId = confluenceSite.id)).thenReturn(0)

            assertThat(provider.isMissingConfiguration(confluenceInstallation)).isTrue
        }

        @Test
        fun `isMissingConfiguration is false when teams selected`() = runTest {
            `when`(confluenceSiteStore.findForInstallation(installationId = confluenceInstallation.id))
                .thenReturn(confluenceSite.copy(confluenceSpaceIngestionType = ConfluenceSpaceIngestionType.SelectedSpacesOnly))

            `when`(confluenceSpaceStore.countShouldIngest(confluenceSiteId = confluenceSite.id)).thenReturn(1)

            assertThat(provider.isMissingConfiguration(confluenceInstallation)).isFalse
        }
    }

    @Nested
    inner class Jira {
        private val jiraInstallation = MockDataClasses.installation(provider = Provider.Jira)
        private val jiraSite = MockDataClasses.jiraSite(installationId = jiraInstallation.id)

        @Test
        fun `isMissingConfiguration is true`() = runTest {
            `when`(jiraSiteStore.findForInstallation(installationId = jiraInstallation.id))
                .thenReturn(jiraSite.copy(jiraProjectIngestionType = null))

            `when`(jiraProjectStore.countShouldIngest(jiraSiteId = jiraSite.id)).thenReturn(0)

            assertThat(provider.isMissingConfiguration(jiraInstallation)).isTrue
        }

        @Test
        fun `isMissingConfiguration is false`() = runTest {
            `when`(jiraSiteStore.findForInstallation(installationId = jiraInstallation.id))
                .thenReturn(jiraSite.copy(jiraProjectIngestionType = JiraProjectIngestionType.AllProjects))

            `when`(jiraProjectStore.countShouldIngest(jiraSiteId = jiraSite.id)).thenReturn(0)

            assertThat(provider.isMissingConfiguration(jiraInstallation)).isFalse
        }

        @Test
        fun `isMissingConfiguration is true for no teams selected`() = runTest {
            `when`(jiraSiteStore.findForInstallation(installationId = jiraInstallation.id))
                .thenReturn(jiraSite.copy(jiraProjectIngestionType = JiraProjectIngestionType.SelectedProjectsOnly))

            `when`(jiraProjectStore.countShouldIngest(jiraSiteId = jiraSite.id)).thenReturn(0)

            assertThat(provider.isMissingConfiguration(jiraInstallation)).isTrue
        }

        @Test
        fun `isMissingConfiguration is false when teams selected`() = runTest {
            `when`(jiraSiteStore.findForInstallation(installationId = jiraInstallation.id))
                .thenReturn(jiraSite.copy(jiraProjectIngestionType = JiraProjectIngestionType.SelectedProjectsOnly))

            `when`(jiraProjectStore.countShouldIngest(jiraSiteId = jiraSite.id)).thenReturn(1)

            assertThat(provider.isMissingConfiguration(jiraInstallation)).isFalse
        }
    }
}
