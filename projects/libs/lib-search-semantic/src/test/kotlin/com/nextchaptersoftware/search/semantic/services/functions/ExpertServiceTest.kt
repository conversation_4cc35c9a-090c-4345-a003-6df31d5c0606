package com.nextchaptersoftware.search.semantic.services.functions

import com.nextchaptersoftware.ci.logging.LogSummaryService
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.models.TopicId
import com.nextchaptersoftware.db.stores.MemberAssociationStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgAndMemberAndIdentity
import com.nextchaptersoftware.db.stores.PullRequestStore
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.SlackTeamDaos
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.ml.api.MachineLearningApiProvider
import com.nextchaptersoftware.ml.api.MachineLearningModelsApi
import com.nextchaptersoftware.ml.api.models.topicmapping.TopicMapping
import com.nextchaptersoftware.ml.api.models.topicmapping.TopicMappingResponse
import com.nextchaptersoftware.ml.completion.CompletionService
import com.nextchaptersoftware.ml.doc.rerank.services.DocumentRerankService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.repo.RepoFocusService
import com.nextchaptersoftware.scm.CiApiLegacyFactory
import com.nextchaptersoftware.scm.ScmRepoApi
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmCommitAuthor
import com.nextchaptersoftware.search.semantic.services.retrieval.SemanticDocumentRetriever
import com.nextchaptersoftware.topic.insight.services.TopicExpertInfo
import com.nextchaptersoftware.topic.insight.services.TopicExpertMappingService
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.coroutines.flow.asFlow
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock

class ExpertServiceTest : DatabaseTestsBase() {

    private lateinit var semanticDocumentRetriever: SemanticDocumentRetriever
    private lateinit var templateService: MLInferenceTemplateService
    private lateinit var rerankService: DocumentRerankService
    private lateinit var scmCommitService: ScmCommitService
    private lateinit var scmRepoApiFactory: ScmRepoApiFactory
    private lateinit var topicExpertMappingService: TopicExpertMappingService
    private lateinit var scmRepoApi: ScmRepoApi
    private lateinit var scmTeamStore: ScmTeamStore
    private lateinit var repoStore: RepoStore
    private lateinit var memberStore: MemberStore
    private lateinit var memberAssociationStore: MemberAssociationStore
    private lateinit var repoFocusService: RepoFocusService
    private lateinit var repoAccessService: RepoAccessService
    private lateinit var slackTeamStore: SlackTeamStore
    private lateinit var slackTeam: SlackTeamDAO
    private lateinit var expertService: ScmExpertService
    private lateinit var completionService: CompletionService
    private lateinit var pullRequestStore: PullRequestStore
    private var ciApiLegacyFactory = mock<CiApiLegacyFactory>()
    private val machineLearningModelsApi = mock<MachineLearningModelsApi>()
    private val machineLearningApiProvider = mock<MachineLearningApiProvider>()
    private val logSummaryService = mock<LogSummaryService>()

    @Suppress("LongMethod")
    private suspend fun setup() {
        semanticDocumentRetriever = mock()
        templateService = mock()
        rerankService = mock()
        scmRepoApi = mock()
        scmRepoApiFactory = mock()
        topicExpertMappingService = mock()
        scmTeamStore = mock()
        repoStore = mock()
        memberAssociationStore = mock()
        slackTeamStore = mock()
        repoFocusService = mock()
        repoAccessService = mock()
        completionService = mock()
        memberStore = mock()
        pullRequestStore = mock()

        val org = ModelBuilders.makeOrg()
        val installation = ModelBuilders.makeInstallation()
        val scmTeam = ModelBuilders.makeScmTeam(installation = installation)
        val repo = ModelBuilders.makeRepo(scmTeam = scmTeam)
        slackTeam = ModelBuilders.makeSlackTeam(org = org, installation = installation)

        val documents = listOf(
            MockDataClasses.typedDocument(
                source = "acme/repo/README.md",
                content = "This is a README file",
                groupId = repo.id.value.value,
                score = 0.8f,
            ),
            MockDataClasses.typedDocument(
                source = "acme/repo/README.md",
                content = "Other README content",
                groupId = repo.id.value.value,
                score = 0.7f,
            ),
            MockDataClasses.typedDocument(
                source = "acme/repo/DEAD.md",
                content = "Should be filtered because groupId is null",
                groupId = null,
                score = 0.6f,
            ),
            MockDataClasses.typedDocument(
                source = "acme/repo/NOT_INCLUDED.md",
                content = "Should be filtered because outside max doc range",
                groupId = repo.id.value.value,
                score = 0.5f,
            ),
        )

        val fileCommits = listOf(
            ScmCommit(
                commitDate = Instant.nowWithMicrosecondPrecision(),
                sha = Hash.parse("1234567890"),
                author = ScmCommitAuthor(
                    externalUserId = "1234",
                    name = "Rashin",
                    email = null,
                ),
            ),
            ScmCommit(
                commitDate = Instant.nowWithMicrosecondPrecision(),
                sha = Hash.parse("**********"),
                author = ScmCommitAuthor(
                    externalUserId = "5678",
                    name = "David",
                    email = null,
                ),
            ),
        )

        val rashinSlackIdentity = MockDataClasses.identity(
            externalId = "1234",
            displayName = "Rashin",
            provider = Provider.Slack,
        )

        val davidSlackIdentity = MockDataClasses.identity(
            externalId = "5678",
            displayName = "David",
            provider = Provider.Slack,
        )

        `when`(scmTeamStore.findById(trx = anyOrNull(), teamId = any(), includeDeleted = anyOrNull())).thenReturn(
            scmTeam.asDataModel(),
        )

        `when`(scmTeamStore.getOrgId(teamId = any())).thenReturn(org.idValue)

        `when`(slackTeamStore.findBySlackExternalTeamId(trx = anyOrNull(), slackExternalTeamId = any())).thenReturn(
            SlackTeamDaos(
                org = org,
                slackTeam = slackTeam,
                installation = installation,
            ),
        )

        val rashinTeamMember = MockDataClasses.member(
            installationId = installation.idValue,
            identityId = rashinSlackIdentity.id,
        )

        val davidTeamMember = MockDataClasses.member(
            installationId = installation.idValue,
            identityId = davidSlackIdentity.id,
            isCurrentMember = false,
        )

        val rashinOrgAndMemberAndIdentity = OrgAndMemberAndIdentity(
            member = rashinTeamMember,
            identity = rashinSlackIdentity,
            org = org.asDataModel(),
        )

        val davidOrgAndMemberAndIdentity = OrgAndMemberAndIdentity(
            member = davidTeamMember,
            identity = davidSlackIdentity,
            org = org.asDataModel(),
        )

        `when`(templateService.orgTemplate(any(), any())).thenReturn(
            MockDataClasses.template(),
        )
        `when`(templateService.getGlobal(any())).thenReturn(
            MockDataClasses.template(),
        )
        `when`(
            semanticDocumentRetriever.doRetrieval(
                orgId = any(),
                orgMemberId = any(),
                documentQuery = any(),
                rerankQuery = any(),
                template = any(),
                maxDocuments = any(),
                maxRerankDocs = any(),
                baseDocumentSet = any(),
                useRRF = any(),
                useCERR = any(),
                sparseVectorWeight = any(),
                enableSparseVectorCodeSearch = any(),
                documentTimeout = any(),
                rerankModel = any(),
                exclusiveRepoSet = anyOrNull(),
                nonExclusiveRepoSet = anyOrNull(),
                dsacContext = any(),
                useShallowRetrievalWhenDsacOff = any(),
                dataSourcePresetConfiguration = any(),
                wideSlackEmbeddingsRetrievalStyle = any(),
                postRetrievalFilters = any(),
                documentPromotionConditions = any(),
            ),
        ).thenReturn(
            documents,
        )

        `when`(
            repoStore.findByIds(
                repoIds = any(),
                includeDeselected = any(),
                includeDisconnected = any(),
            ),
        ).thenReturn(
            listOf(repo.asDataModel()),
        )

        `when`(
            scmRepoApi.fileCommits(
                path = any(),
                maxItems = any(),
            ),
        ).thenReturn(
            fileCommits.asFlow(),
        )

        `when`(
            pullRequestStore.countByRepos(orgId = any<OrgId>()),
        ).thenReturn(emptyMap())

        `when`(
            scmRepoApiFactory.getApiFromRepo(
                scmTeam = any(),
                repo = any(),
                scm = any(),
                clientEngine = any(),
            ),
        ).thenReturn(
            scmRepoApi,
        )

        `when`(
            memberAssociationStore.legacyGetAssociatedMembersByExternalIdsGroupedByPrimaryMember(
                orgId = any(),
                externalIds = any(),
            ),
        ).thenReturn(
            mapOf(
                rashinOrgAndMemberAndIdentity to emptySet(),
                davidOrgAndMemberAndIdentity to emptySet(),
            ),
        )

        `when`(
            repoFocusService.findRepoFocus(
                orgId = any(),
                orgMemberIds = any(),
                limit = any(),
            ),
        ).thenReturn(
            listOf(
                MockDataClasses.repoFocus(
                    repoId = repo.idValue,
                    repoName = repo.externalName,
                ),
            ),
        )

        `when`(
            completionService.query(
                prompt = any(),
                engine = any(),
                jsonOutputFormatInstructions = anyOrNull(),
                temperature = any(),
            ),
        ).thenReturn(
            repo.id.toString(),
        )

        `when`(
            machineLearningApiProvider.machineLearningModelsApi,
        ).thenReturn(machineLearningModelsApi)

        val topicMappings = listOf(
            listOf(
                TopicMapping(
                    topic = "sourcemark",
                    confidenceScore = 1.0f,
                ),
            ),
        )

        `when`(
            topicExpertMappingService.fetchTopicExpertInfo(
                orgId = anyOrNull(),
                contentByRepoId = anyOrNull(),
            ),
        ).thenReturn(
            listOf(
                TopicExpertInfo(TopicId.random(), UUID.randomUUID(), "Rashin", 1.0f),
            ),
        )

        `when`(
            machineLearningModelsApi.topicMapping(
                orgId = anyOrNull(),
                repoId = anyOrNull(),
                pineconeHybridIndex = anyOrNull(),
                documents = anyOrNull(),
                topK = anyOrNull(),
                alpha = anyOrNull(),
            ),
        ).thenReturn(TopicMappingResponse(topicMappings = topicMappings))

        scmCommitService = ScmCommitService(
            scmTeamStore = scmTeamStore,
            repoStore = repoStore,
            memberStore = memberStore,
            scmRepoApiFactory = scmRepoApiFactory,
            completionService = completionService,
            pullRequestStore = pullRequestStore,
            repoAccessService = repoAccessService,
            repoFocusService = repoFocusService,
            ciApiLegacyFactory = ciApiLegacyFactory,
            logSummaryService = logSummaryService,
        )

        expertService = ScmExpertService(
            semanticDocumentRetriever = semanticDocumentRetriever,
            templateService = templateService,
            scmCommitService = scmCommitService,
            scmTeamStore = scmTeamStore,
            repoStore = repoStore,
            expertFunctionTimeout = GlobalConfig.INSTANCE.search.functions.expertsFunctionTimeout,
            useExpertsUsingTopics = GlobalConfig.INSTANCE.search.useExpertsUsingTopics,
            memberAssociationStore = memberAssociationStore,
            slackTeamStore = slackTeamStore,
            topicExpertMappingService = topicExpertMappingService,
            config = GlobalConfig.INSTANCE,
        )
    }

    @Test
    fun `pulls experts`() = suspendingDatabaseTest {
        setup()

        val orgId = OrgId.random()
        val experts = expertService.expertsForNamedFileEntity(
            orgId = orgId,
            orgMemberId = OrgMemberId.random(),
            entity = "fuzzyBunnies",
            dsacContext = DsacContext.disabled(orgId),
        )

        assertThat(experts).isEqualTo(
            """
            |## Most knowledgeable team members for fuzzyBunnies
            |The most knowledgeable team members for fuzzyBunnies is:
            |- Rashin
            |
            |Refer the user to these team members if the user explicitly seeks help understanding fuzzyBunnies.
        """.trimMargin(),
        )
    }
}
