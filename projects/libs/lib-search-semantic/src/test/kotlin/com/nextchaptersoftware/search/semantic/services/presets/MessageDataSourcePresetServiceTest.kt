package com.nextchaptersoftware.search.semantic.services.presets

import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.DataSourcePresetArtifact
import com.nextchaptersoftware.db.models.DataSourcePresetId
import com.nextchaptersoftware.db.models.DataSourcePresetInstallationArtifact
import com.nextchaptersoftware.db.models.MessageDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class MessageDataSourcePresetServiceTest : DatabaseTestsBase() {
    private val service = MessageDataSourcePresetService()

    @Test
    fun updateDataSourcePreset() = suspendingDatabaseTest {
        val messageId = makeMessage().idValue

        val dataSourcePreset = DataSourcePresetArtifact(
            presetId = DataSourcePresetId.random(),
            displayName = "Test",
            avatarUrl = "https://www.google.com",
            installations = listOf(
                DataSourcePresetInstallationArtifact(
                    provider = Provider.GitHub,
                    displayName = "installation",
                    groupCount = 1,
                ),
            ),
        )

        service.updateDataSourcePreset(
            messageId = messageId,
            dataSourcePreset = dataSourcePreset,
        )

        val updated = suspendedTransaction { MessageDAO[messageId].asDataModel() }

        assertThat(updated.dataSourcePreset).isEqualTo(dataSourcePreset)
    }
}
