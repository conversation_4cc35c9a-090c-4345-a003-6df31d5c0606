package com.nextchaptersoftware.scm.data.store.keys

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.integration.data.store.keys.IntegrationDataKeyProvider
import com.nextchaptersoftware.scm.models.ScmPullRequest

class ScmPullRequestDataKeyProvider : IntegrationDataKeyProvider.ScmDataKeyProvider() {
    fun provide(
        orgId: OrgId,
        repoId: RepoId,
        pullRequest: ScmPullRequest,
    ): String {
        val pathList = listOfNotNull(
            orgId,
            repoId,
            "pullRequests",
            pullRequest.number,
            pullRequest.number,
        )
        return provide(*pathList.toTypedArray())
    }
}
