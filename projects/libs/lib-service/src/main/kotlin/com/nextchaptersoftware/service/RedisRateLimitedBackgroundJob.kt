package com.nextchaptersoftware.service

import com.nextchaptersoftware.redis.ratelimit.RateLimiter
import kotlin.time.Duration

/**
 * A background job that enforces a rate limit on the underlying job
 *
 * The rate limit state is managed via Redis, so it's persistent to deployments.
 *
 * @param cooldown duration between consecutive executions
 * @param job background job to be rate-limited
 */
private class RedisRateLimitedBackgroundJob(
    private val cooldown: Duration,
    private val job: BackgroundJob,
) : BackgroundJob {

    override val name: String = "RateLimited $cooldown: ${job.name}"

    private val rateLimiter = RateLimiter(
        keyPrefix = "${javaClass.simpleName}:${job.name}:$cooldown",
    )

    override suspend fun run() {
        rateLimiter.withRateLimit(
            cooldown = cooldown,
            compute = job::run,
        )
    }
}

/**
 * @see RedisRateLimitedBackgroundJob
 */
fun BackgroundJob.redisRateLimit(
    cooldown: Duration,
): BackgroundJob = RedisRateLimitedBackgroundJob(
    cooldown = cooldown,
    job = this,
)
