package com.nextchaptersoftware.asset.key

import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.ProductAgentType.IntelliJ
import com.nextchaptersoftware.db.models.ProductAgentType.VSCode
import com.nextchaptersoftware.db.models.fromProductAgentHeaderOrNull
import io.ktor.http.ContentType

class AssetKeyExtensionProvider {
    fun provide(contentType: String, productAgentTypeHeader: String?): String {
        return provide(
            ContentType.parse(contentType),
            ProductAgentType.fromProductAgentHeaderOrNull(productAgentTypeHeader),
        )
    }

    private fun provide(contentType: ContentType, productAgentType: ProductAgentType?): String {
        return if (contentType.match(ContentType.Video.Any)) {
            when (productAgentType) {
                VSCode -> "h264-mp3.mp4"
                IntelliJ -> "vp9-ogg.webm"
                else -> "h265-aac.mp4"
            }
        } else {
            ""
        }
    }
}
