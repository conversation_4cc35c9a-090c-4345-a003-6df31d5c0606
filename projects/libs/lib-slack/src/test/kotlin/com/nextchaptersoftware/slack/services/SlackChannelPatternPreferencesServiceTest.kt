package com.nextchaptersoftware.slack.services

import com.nextchaptersoftware.db.ModelBuilders.makeDataSourcePreset
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeSlackChannel
import com.nextchaptersoftware.db.ModelBuilders.makeSlackTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.AnswerConcisenessPreference
import com.nextchaptersoftware.db.models.AnswerDepthPreference
import com.nextchaptersoftware.db.models.AnswerPreferences
import com.nextchaptersoftware.db.models.AnswerTonePreference
import com.nextchaptersoftware.db.models.DataSourcePresetDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.SlackAutoAnswerMode
import com.nextchaptersoftware.db.models.SlackChannelDAO
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.stores.SlackChannelPatternPreferencesStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SlackChannelPatternPreferencesServiceTest : DatabaseTestsBase() {
    private val slackChannelPatternPreferencesStore: SlackChannelPatternPreferencesStore = Stores.slackChannelPatternPreferencesStore
    private val service = SlackChannelPatternPreferencesService(
        slackChannelPatternPreferencesStore = slackChannelPatternPreferencesStore,
    )

    private lateinit var org: OrgDAO
    private lateinit var slackTeam: SlackTeamDAO
    private lateinit var slackChannel: SlackChannelDAO
    private lateinit var dataSourcePreset: DataSourcePresetDAO

    private suspend fun setup() {
        org = makeOrg()
        slackTeam = makeSlackTeam(org = org)
        slackChannel = makeSlackChannel(slackTeam = slackTeam)
        dataSourcePreset = makeDataSourcePreset(org = org)
    }

    @Test
    fun `test upsert with autoAnswerMode for patterns`() = suspendingDatabaseTest {
        setup()
        val initialPattern = "kool*"

        suspendedTransaction {
            service.upsert(
                trx = this,
                slackTeamId = slackTeam.idValue,
                slackChannelPatternPreferenceEntries = listOf(
                    SlackChannelPatternPreferenceEntry(
                        slackChannelPattern = initialPattern,
                        autoAnswerMode = SlackAutoAnswerMode.LowFrequencyHighQuality,
                    ),
                ),
            )
        }

        val preferences = suspendedTransaction {
            slackChannelPatternPreferencesStore.findSlackChannelPatternsBySlackTeamId(trx = this, slackTeamId = slackTeam.idValue)
        }

        assertThat(preferences).containsExactly(initialPattern)
    }

    @Test
    fun `test pattern update and deletion with autoAnswerMode`() = suspendingDatabaseTest {
        setup()
        val pattern1 = "kool*"
        val pattern2 = "aid*"

        // Insert initial patterns
        suspendedTransaction {
            service.upsert(
                trx = this,
                slackTeamId = slackTeam.idValue,
                slackChannelPatternPreferenceEntries = listOf(
                    SlackChannelPatternPreferenceEntry(
                        slackChannelPattern = pattern1,
                        autoAnswerMode = SlackAutoAnswerMode.LowFrequencyHighQuality,
                    ),
                    SlackChannelPatternPreferenceEntry(
                        slackChannelPattern = pattern2,
                        autoAnswerMode = SlackAutoAnswerMode.Off,
                    ),
                ),
            )
        }

        val initialPreferences = suspendedTransaction {
            slackChannelPatternPreferencesStore.findSlackChannelPatternsBySlackTeamId(trx = this, slackTeamId = slackTeam.idValue)
        }
        assertThat(initialPreferences).containsExactlyInAnyOrder(pattern1, pattern2)

        // Update with new patterns, removing pattern1
        val pattern3 = "juice*"
        suspendedTransaction {
            service.upsert(
                trx = this,
                slackTeamId = slackTeam.idValue,
                slackChannelPatternPreferenceEntries = listOf(
                    SlackChannelPatternPreferenceEntry(
                        slackChannelPattern = pattern2,
                        autoAnswerMode = SlackAutoAnswerMode.LowFrequencyHighQuality,
                    ),
                    SlackChannelPatternPreferenceEntry(
                        slackChannelPattern = pattern3,
                        autoAnswerMode = SlackAutoAnswerMode.Off,
                    ),
                ),
            )
        }

        val updatedPreferences = suspendedTransaction {
            slackChannelPatternPreferencesStore.findSlackChannelPatternsBySlackTeamId(trx = this, slackTeamId = slackTeam.idValue)
        }
        assertThat(updatedPreferences).containsExactlyInAnyOrder(pattern2, pattern3)
        assertThat(updatedPreferences).doesNotContain(pattern1)
    }

    @Test
    fun `test multiple patterns with autoAnswerMode`() = suspendingDatabaseTest {
        setup()

        val patterns = listOf("foo*", "bar*", "baz*")
        val entries = patterns.map { pattern ->
            SlackChannelPatternPreferenceEntry(
                slackChannelPattern = pattern,
                autoAnswerMode = when (pattern.startsWith("b")) {
                    true -> SlackAutoAnswerMode.LowFrequencyHighQuality
                    false -> SlackAutoAnswerMode.Off
                },
            )
        }

        suspendedTransaction {
            service.upsert(
                trx = this,
                slackTeamId = slackTeam.idValue,
                slackChannelPatternPreferenceEntries = entries,
            )
        }

        val preferences = suspendedTransaction {
            slackChannelPatternPreferencesStore.findSlackChannelPatternsBySlackTeamId(trx = this, slackTeamId = slackTeam.idValue)
        }

        assertThat(preferences).containsExactlyInAnyOrderElementsOf(patterns)
    }

    @Test
    fun `test upsert with answer preferences`() = suspendingDatabaseTest {
        setup()
        val initialPattern = "kool*"

        suspendedTransaction {
            service.upsert(
                trx = this,
                slackTeamId = slackTeam.idValue,
                slackChannelPatternPreferenceEntries = listOf(
                    SlackChannelPatternPreferenceEntry(
                        slackChannelPattern = initialPattern,
                        answerPreferences = AnswerPreferences(
                            conciseness = AnswerConcisenessPreference.LessConcise,
                            tone = AnswerTonePreference.LessTechnicalTone,
                            depth = AnswerDepthPreference.LessDepth,
                        ),
                        dataSourcePresetId = dataSourcePreset.idValue,
                    ),
                ),
            )
        }

        val preferences = suspendedTransaction {
            slackChannelPatternPreferencesStore.findSlackChannelPatternsBySlackTeamId(trx = this, slackTeamId = slackTeam.idValue)
        }

        assertThat(preferences).containsExactly(initialPattern)
    }
}
