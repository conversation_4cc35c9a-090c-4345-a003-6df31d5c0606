package com.nextchaptersoftware.slack.services

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.SlackConfig
import io.ktor.http.URLBuilder
import io.ktor.http.Url

class SlackDeepLinkService(
    private val slackConfig: SlackConfig = GlobalConfig.INSTANCE.providers.slack,
) {
    private fun buildSlackUrl(path: String, parameters: Map<String, String>): Url {
        return URLBuilder("slack://").apply {
            this.pathSegments = listOf(path)
            parameters.forEach { (key, value) ->
                this.parameters.append(key, value)
            }
        }.build()
    }

    fun getAppHome(slackExternalTeamId: String): Url {
        return buildSlackUrl(
            "app",
            mapOf(
            "team" to slackExternalTeamId,
            "id" to slackConfig.appId,
        ),
        )
    }

    fun getDirectMessage(slackExternalTeamId: String, userId: String): Url {
        return buildSlackUrl(
            "user",
            mapOf(
            "team" to slackExternalTeamId,
            "user" to userId,
        ),
        )
    }

    fun getFile(slackExternalTeamId: String, fileId: String): Url {
        return buildSlackUrl(
            "file",
            mapOf(
            "team" to slackExternalTeamId,
            "id" to fileId,
        ),
        )
    }

    fun getWorkspace(slackExternalTeamId: String): Url {
        return buildSlackUrl(
            "workspace",
            mapOf(
            "team" to slackExternalTeamId,
        ),
        )
    }
}
