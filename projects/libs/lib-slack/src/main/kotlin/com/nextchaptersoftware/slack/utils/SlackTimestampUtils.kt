package com.nextchaptersoftware.slack.utils

import kotlinx.datetime.Instant

object SlackTimestampUtils {
    @Suppress("MagicNumber")
    fun String.toInstantFromSlackTs(): Instant? {
        return this.toDoubleOrNull()?.let {
            Instant.fromEpochMilliseconds((it * 1000).toLong())
        }
    }

    @Suppress("MagicNumber")
    fun String.toTsWithMicroseconds(): String {
        return "${dropLast(6)}.${takeLast(6)}"
    }

    @Suppress("MagicNumber")
    fun String.fromTsWithMicroseconds(): String {
        return replace(".", "")
    }
}
