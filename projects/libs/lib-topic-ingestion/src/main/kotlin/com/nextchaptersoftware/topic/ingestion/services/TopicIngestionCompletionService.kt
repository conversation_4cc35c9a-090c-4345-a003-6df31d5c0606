package com.nextchaptersoftware.topic.ingestion.services

import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.s3.S3ProviderFactory
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.Topic
import com.nextchaptersoftware.db.models.TopicSourceType
import com.nextchaptersoftware.db.models.eq
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.TopicExpertStore
import com.nextchaptersoftware.db.stores.TopicStore
import com.nextchaptersoftware.ingestion.pipeline.config.IngestionPipelineConfig
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.topic.ingestion.pipeline.RepoTopicIngestionService
import com.nextchaptersoftware.topic.ingestion.pipeline.completion.RepoTopicIngestionCompletion
import com.nextchaptersoftware.topic.ingestion.services.internal.TopicIngestionMemberResolutionService
import com.nextchaptersoftware.topic.ingestion.services.payload.TopicIngestionOutputPayloadService
import com.nextchaptersoftware.types.Hash
import mu.KotlinLogging
import software.amazon.awssdk.services.s3.paginators.ListObjectsV2Iterable

private val LOGGER = KotlinLogging.logger { }

class TopicIngestionCompletionService(
    private val s3ProviderFactory: S3ProviderFactory,
    private val awsClientProvider: AWSClientProvider,
    private val ingestionPipelineConfig: IngestionPipelineConfig,
    private val topicIngestionOutputPayloadService: TopicIngestionOutputPayloadService,
    private val topicIngestionMemberResolutionService: TopicIngestionMemberResolutionService,
    private val topicStore: TopicStore = Stores.topicStore,
    private val topicExpertStore: TopicExpertStore = Stores.topicExpertStore,
    private val repoTopicIngestionService: RepoTopicIngestionService,
    private val topicSourceType: TopicSourceType = TopicSourceType.Cluster,
) {
    private val s3Client by lazy {
        s3ProviderFactory.generate(awsClientProvider, ingestionPipelineConfig.ingestionPipeline.s3.bucket)
    }

    suspend fun ingest(
        teamId: ScmTeamId,
        repoId: RepoId,
        s3OutputPrefix: String,
    ) = withLoggingContextAsync(
        "s3OutputPrefix" to s3OutputPrefix,
    ) {
        val s3Url = s3OutputPrefix.asUrl
        val listOfObjects = s3Client.listObjects(prefix = s3Url.encodedPath.removePrefix("/"))

        val topics = ingestTopics(
            repoId = repoId,
            listOfObjects = listOfObjects,
        )

        ingestTopicsExperts(
            teamId = teamId,
            topics = topics,
            listOfObjects = listOfObjects,
        )

        ingestTopicMetadata(
            repoId = repoId,
            listOfObjects = listOfObjects,
        )
    }

    private suspend fun ingestTopics(
        repoId: RepoId,
        listOfObjects: ListObjectsV2Iterable,
    ): List<Topic> {
        return listOfObjects.contents().filter { s3Object ->
            LOGGER.debugAsync("s3ObjectName" to s3Object.key()) {
                "Filtering s3Object"
            }
            s3Object.key().contains(".json") &&
                s3Object.key().contains("cluster_topics") &&
                s3Object.key().contains("metadata.json").not() &&
                s3Object.key().contains("experts.json").not()
        }.flatMap { s3Object ->
            LOGGER.debugAsync("s3ObjectName" to s3Object.key()) {
                "Processing s3Object"
            }
            val topicsJsonStr = s3Client.getObjectAsBytes(
                objectKey = s3Object.key(),
            ).asString(Charsets.UTF_8)
            topicIngestionOutputPayloadService.processTopicPayload(topicsJsonStr)
        }.sortedByDescending { it.score }.distinctBy { it.name }.let { topics ->
            LOGGER.debugAsync("topicInfosSize" to topics.size) {
                "Processing topicInfos"
            }
            topicStore.upsertTopics(
                repoId = repoId,
                topics = topics.map { it.toTopicInfo() },
                source = topicSourceType,
            )
        }
    }

    private suspend fun ingestTopicsExperts(
        teamId: ScmTeamId,
        topics: List<Topic>,
        listOfObjects: ListObjectsV2Iterable,
    ) {
        listOfObjects.contents().filter { s3Object ->
            LOGGER.debugAsync("s3ObjectName" to s3Object.key()) {
                "Filtering s3Object"
            }
            s3Object.key().contains("experts.json")
        }.flatMap { s3Object ->
            LOGGER.debugAsync("s3ObjectName" to s3Object.key()) {
                "Processing s3Object"
            }
            val topicsJsonStr = s3Client.getObjectAsBytes(
                objectKey = s3Object.key(),
            ).asString(Charsets.UTF_8)
            topicIngestionOutputPayloadService.processTopicExpertsPayload(topicsJsonStr)
        }.distinctBy { it.name }.forEach { topicExperts ->
            LOGGER.debugAsync("topic" to topicExperts.name) {
                "Processing topic experts"
            }
            topics.firstOrNull { it.eq(topicExperts.name) }?.also { topic ->
                val teamMembers = topicExperts.experts.mapNotNull { expert ->
                    topicIngestionMemberResolutionService.resolveMember(
                        teamId = teamId,
                        name = expert.name,
                        email = expert.email,
                    )
                }
                teamMembers.forEach {
                    topicExpertStore.upsertTopicExpert(
                        topicId = topic.id,
                        orgMemberId = it.orgMemberId,
                        memberId = it.id,
                        activityCount = null,
                    )
                }
            }
        }
    }

    private suspend fun ingestTopicMetadata(
        repoId: RepoId,
        listOfObjects: ListObjectsV2Iterable,
    ) {
        listOfObjects.contents().filter { s3Object ->
            LOGGER.debugAsync("s3ObjectName" to s3Object.key()) {
                "Filtering s3Object"
            }
            s3Object.key().contains("metadata.json")
        }.map { s3Object ->
            LOGGER.debugAsync("s3ObjectName" to s3Object.key()) {
                "Processing s3Object"
            }
            val topicsJsonStr = s3Client.getObjectAsBytes(
                objectKey = s3Object.key(),
            ).asString(Charsets.UTF_8)
            topicIngestionOutputPayloadService.processMetadataPayload(topicsJsonStr)
        }.forEach {
            LOGGER.debugAsync("completionCommitSha" to it.completedCommitSha) {
                "Processing completion metadata"
            }
            repoTopicIngestionService.updateLatestContinuation(
                repoId = repoId,
                continuation = RepoTopicIngestionCompletion(
                    completedCommitSha = Hash.parse(it.completedCommitSha),
                    completedCommitTimestamp = it.completedCommitTimestamp,
                ),
            )
        }
    }
}
