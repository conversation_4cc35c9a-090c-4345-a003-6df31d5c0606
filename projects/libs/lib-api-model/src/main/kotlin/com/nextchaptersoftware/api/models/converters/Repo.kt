package com.nextchaptersoftware.api.models.converters

import com.nextchaptersoftware.api.models.Repo as ApiRepo
import com.nextchaptersoftware.api.models.ScmRepo as ApiScmRepo
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.utils.asApiDateTime

val Repo.asApiModel: ApiRepo
    get() {
        return ApiRepo(
            id = id.value,
            provider = provider.asApiModel(),
            rootCommitSha = rootHash.asString(),
            fullName = fullName,
            ownerName = externalOwner,
            repoName = externalName,
            isConnected = isScmConnected,
            webUrl = repoUrl.webHtmlUrl,
            httpUrl = httpRepoUrl.canonicalHttpUrl,
            sshUrl = sshRepoUrl.canonicalSshUrl,
            scpUrl = sshRepoUrl.canonicalScpUrl,
            createdAt = createdAt.asApiDateTime(),
            lastActiveAt = lastActiveAt.asApiDateTime(),
            hasCompletedProcessing = hasCompletedProcessing,
            isEmpty = isEmpty ?: false,
            isFork = isFork,
            isUserSelected = isUserSelected,
            externalId = externalId,
            isCiEnabled = isCiEnabled,
        )
    }

val Repo.asApiScmRepo: ApiScmRepo
    get() {
        return ApiScmRepo(
            externalId = externalId,
            fullName = fullName,
            ownerName = externalOwner,
            repoName = externalName,
            webUrl = repoUrl.webHtmlUrl,
            httpUrl = repoUrl.canonicalHttpUrl,
            sshUrl = sshRepoUrl.canonicalSshUrl,
            scpUrl = sshRepoUrl.canonicalScpUrl,
            createdAt = createdAt.asApiDateTime(),
            lastActiveAt = lastActiveAt.asApiDateTime(),
            isFork = isFork,
            isEmpty = isEmpty,
        )
    }
