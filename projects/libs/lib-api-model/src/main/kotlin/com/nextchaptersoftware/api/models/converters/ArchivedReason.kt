package com.nextchaptersoftware.api.models.converters

import com.nextchaptersoftware.api.models.ArchiveReasonType
import com.nextchaptersoftware.db.models.ArchivedReason

@Suppress("CyclomaticComplexMethod")
fun ArchivedReason.asApiModel(): ArchiveReasonType = when (this) {
    ArchivedReason.User,
    -> ArchiveReasonType.custom

    ArchivedReason.Incorrect,
    -> ArchiveReasonType.incorrect

    ArchivedReason.Outdated,
    ArchivedReason.LowRelevance,
    ArchivedReason.IgnoredAuthor,
    ArchivedReason.PrClosed,
    ArchivedReason.AutogeneratedComment,
    -> ArchiveReasonType.outdated
}

fun ArchiveReasonType.asArchivedReason(): ArchivedReason = when (this) {
    ArchiveReasonType.custom -> ArchivedReason.User
    ArchiveReasonType.incorrect -> ArchivedReason.Incorrect
    ArchiveReasonType.outdated -> ArchivedReason.Outdated
}
