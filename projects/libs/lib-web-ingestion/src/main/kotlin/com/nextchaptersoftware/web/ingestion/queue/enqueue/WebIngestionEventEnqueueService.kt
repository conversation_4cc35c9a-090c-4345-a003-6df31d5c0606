package com.nextchaptersoftware.web.ingestion.queue.enqueue

import com.nextchaptersoftware.web.events.queue.enqueue.WebEventEnqueueService
import com.nextchaptersoftware.web.events.queue.payloads.WebIngestionEvent
import com.nextchaptersoftware.web.ingestion.redis.cache.RedisWebIngestionCache
import com.nextchaptersoftware.web.ingestion.redis.cache.WebIngestionCache
import kotlin.time.Duration
import kotlin.time.Duration.Companion.days

class WebIngestionEventEnqueueService(
    private val webEventEnqueueService: WebEventEnqueueService,
    private val cache: WebIngestionCache = RedisWebIngestionCache(),
) {
    suspend fun enqueueEvent(
        event: WebIngestionEvent.WebPageIngestionEvent,
        reingestAllPages: Boolean = false,
    ) {
        val orgId = event.orgId
        val root = event.root
        val path = event.path
        val expiry = event.reingestIntervalDays.days

        if (reingestAllPages) { // Clear the cache if we want to do a full ingest
            cache.clear(orgId = orgId, root = root)
        }

        if (cache.set(orgId = orgId, root = root, url = path, expiry = expiry)) {
            enqueue(event = event, withDelay = null)
        }
    }

    suspend fun enqueueEventForRetry(
        event: WebIngestionEvent.WebPageIngestionEvent,
        withDelay: Duration,
    ) {
        enqueue(event = event, withDelay = withDelay)
    }

    private suspend fun enqueue(
        event: WebIngestionEvent.WebPageIngestionEvent,
        withDelay: Duration?,
    ) {
        webEventEnqueueService.enqueueIngestionEvent(
            event = event,
            withDelay = withDelay,
        )
    }
}
