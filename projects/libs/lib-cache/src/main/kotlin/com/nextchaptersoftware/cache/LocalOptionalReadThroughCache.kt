package com.nextchaptersoftware.cache

import io.github.reactivecircus.cache4k.Cache
import kotlin.time.Duration

/**
 * In-memory local read-through cache that takes optional values.
 */
class LocalOptionalReadThroughCache<K : Any, V : Any?>(
    private val writeTimeout: Duration? = null,
) : ReadThroughCache<K, V?> {
    private val cache = Cache.Builder<K, Optional<V>>()
        .apply {
            writeTimeout?.let {
                expireAfterWrite(it)
            }
        }.build()

    override suspend fun getOrCompute(key: K, compute: suspend (K) -> V?): V? {
        return cache.get(key) {
            Optional(compute(key))
        }.data
    }

    override suspend fun get(key: K): V? {
        return cache.get(key)?.data
    }

    override suspend fun put(key: K, value: V?) {
        cache.put(key, Optional(value))
    }

    override fun clear() {
        cache.invalidateAll()
    }
}
