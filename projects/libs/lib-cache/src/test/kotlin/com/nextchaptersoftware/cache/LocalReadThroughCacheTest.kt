package com.nextchaptersoftware.cache

import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

class LocalReadThroughCacheTest {

    @Test
    fun `fetches from the store but only once`() {
        val cache = LocalReadThroughCache<String, String>()
        val store = mock<Store<String, String>>()

        `when`(store.fetch(any())).thenReturn("value")

        runBlocking {
            repeat(10) {
                assertThat(cache.getOrCompute("key", store::fetch)).isEqualTo("value")
            }
            verify(store, times(1)).fetch(eq("key"))
        }
    }

    @Test
    fun `fetches multiple values from the store but only once`() {
        val cache = LocalReadThroughCache<String, String>()
        val store = mock<Store<String, String>>()

        `when`(store.fetch(eq("key1"))).thenReturn("value1")
        `when`(store.fetch(eq("key2"))).thenReturn("value2")

        runBlocking {
            repeat(10) {
                assertThat(cache.getOrCompute("key1", store::fetch)).isEqualTo("value1")
                assertThat(cache.getOrCompute("key2", store::fetch)).isEqualTo("value2")
            }
            verify(store, times(1)).fetch(eq("key1"))
            verify(store, times(1)).fetch(eq("key2"))
            verify(store, times(2)).fetch(any())
        }
    }

    @Test
    fun `gets from cache`() {
        val cache = LocalReadThroughCache<String, String>()

        runBlocking {
            assertThat(cache.get("key")).isNull()

            cache.getOrCompute("key") { "value" }
            assertThat(cache.get("key")).isEqualTo("value")
        }
    }
}
