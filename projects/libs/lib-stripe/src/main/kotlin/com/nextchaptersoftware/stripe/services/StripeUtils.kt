package com.nextchaptersoftware.stripe.services

import com.nextchaptersoftware.db.models.CardType
import com.stripe.model.Charge.PaymentMethodDetails
import com.stripe.model.PaymentMethod

object StripeUtils {
    fun cardType(card: PaymentMethod.Card): CardType {
        return card.brand.cardType()
    }

    fun cardType(card: PaymentMethodDetails.Card): CardType {
        return card.brand.cardType()
    }

    private fun String.cardType(): CardType {
        return when (this.lowercase()) {
            "visa" -> CardType.Visa
            "mastercard" -> CardType.Mastercard
            "amex" -> CardType.Amex
            else -> CardType.Other
        }
    }
}
