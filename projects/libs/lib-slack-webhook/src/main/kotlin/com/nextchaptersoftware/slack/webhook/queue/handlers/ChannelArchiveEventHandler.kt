package com.nextchaptersoftware.slack.webhook.queue.handlers

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.slack.event.SlackEventType
import com.nextchaptersoftware.slack.event.SlackPayloadExtractor
import com.nextchaptersoftware.slack.event.SlackPayloadParser
import com.nextchaptersoftware.slack.webhook.queue.payloads.SlackWebhookEvent
import com.nextchaptersoftware.slack.webhook.services.SlackChannelArchiveEventService
import com.slack.api.app_backend.events.payload.ChannelArchivePayload
import com.slack.api.model.event.ChannelArchiveEvent

class ChannelArchiveEventHandler(
    private val slackChannelArchiveEventService: SlackChannelArchiveEventService,
) : TypedEventHandler<SlackWebhookEvent.SlackWebhookApiEvent> {
    override suspend fun handle(event: SlackWebhookEvent.SlackWebhookApiEvent): Boolean {
        val webhookEventPayload = SlackPayloadExtractor.extract(event.body) ?: return false
        if (!SlackEventType.isEventType(
                webhookEventPayload,
                ChannelArchiveEvent.TYPE_NAME,
            )
        ) {
            return false
        }

        val payload = SlackPayloadParser.parsePayload<ChannelArchivePayload>(webhookEventPayload) ?: return false
        slackChannelArchiveEventService.process(payload = payload)
        return true
    }
}
