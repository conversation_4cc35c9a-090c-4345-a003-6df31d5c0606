package com.nextchaptersoftware.slack.webhook.queue.handlers

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.view.SlackBotViewSubmissionEventHandler
import com.nextchaptersoftware.slack.event.SlackEventType
import com.nextchaptersoftware.slack.event.SlackPayloadExtractor
import com.nextchaptersoftware.slack.event.SlackPayloadParser
import com.nextchaptersoftware.slack.webhook.queue.payloads.SlackWebhookEvent
import com.slack.api.app_backend.views.payload.ViewSubmissionPayload

class ViewSubmissionEventHandler(
    private val slackBotViewSubmissionEventHandler: SlackBotViewSubmissionEventHandler,
) : TypedEventHandler<SlackWebhookEvent.ViewSubmissionEvent> {
    override suspend fun handle(event: SlackWebhookEvent.ViewSubmissionEvent): Boolean {
        // You MUST extract this event body as it is not serialized in json format but some weird intermediate slack format
        val webhookEventPayload = SlackPayloadExtractor.extract(event.body) ?: return false
        if (!SlackEventType.isEventType(
                webhookEventPayload,
                ViewSubmissionPayload.TYPE,
            )
        ) {
            return false
        }

        val viewSubmissionPayload = SlackPayloadParser.parsePayload<ViewSubmissionPayload>(webhookEventPayload) ?: return false
        slackBotViewSubmissionEventHandler.handle(event = viewSubmissionPayload)
        return true
    }
}
