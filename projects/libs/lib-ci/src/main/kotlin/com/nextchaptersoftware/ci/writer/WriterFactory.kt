package com.nextchaptersoftware.ci.writer

import com.nextchaptersoftware.db.models.Provider

object WriterFactory {

    fun create(
        provider: Provider,
    ): Writer = when (provider) {
        Provider.GitHub,
        Provider.GitHubEnterprise,
            -> GitHubWriter()

        Provider.Bitbucket,
            -> BitbucketWriter

        else -> error("Not supported: $provider")
    }
}
