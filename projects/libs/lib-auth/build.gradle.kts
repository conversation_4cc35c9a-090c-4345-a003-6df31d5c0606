plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

configurations {
    create("test")
}

dependencies {
    implementation(libs.bundles.ktor.client)
    implementation(libs.exposed.kotlin.datetime)

    implementation(project(":projects:clients:client-redis", "default"))
    implementation(project(":projects:libs:lib-crypto", "default"))
    implementation(project(":projects:libs:lib-ktor", "default"))
    implementation(project(":projects:libs:lib-security", "default"))

    testImplementation(testLibs.bundles.test.core)
}

// Output test resources to a jar that we can add a dependency to from other projects
tasks {
    register<Jar>("testArchive") {
        archiveBaseName.set("${project.name}-test")
        from(project.the<SourceSetContainer>()["test"].output)
    }
}

artifacts {
    add("test", tasks["testArchive"])
}
