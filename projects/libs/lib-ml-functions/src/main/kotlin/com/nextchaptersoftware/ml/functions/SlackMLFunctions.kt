package com.nextchaptersoftware.ml.functions

import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.ml.functions.core.MLFunction
import com.nextchaptersoftware.ml.functions.core.MLFunctionDescription
import com.nextchaptersoftware.ml.functions.core.MLFunctionExecutionContext
import com.nextchaptersoftware.slack.api.models.SlackPermission
import com.nextchaptersoftware.slack.extractor.utils.SlackChannelDocumentLoader
import com.nextchaptersoftware.slack.extractor.utils.SlackChannelNotFoundException
import com.nextchaptersoftware.slack.extractor.utils.SlackConversationHistoryNotInChannelError
import com.nextchaptersoftware.slack.extractor.utils.SlackConversationSummaryPromptExtractor
import com.nextchaptersoftware.slack.services.AuthorizedSlackChannelResolver
import com.nextchaptersoftware.slack.services.SlackTokenService
import com.nextchaptersoftware.slack.utils.SlackUrl
import com.nextchaptersoftware.slack.utils.SlackUrlBuilder
import com.slack.api.model.event.MessageBotEvent
import kotlin.time.Duration
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class SlackMLFunctions(
    private val slackTokenService: SlackTokenService,
    private val authorizedSlackChannelResolver: AuthorizedSlackChannelResolver,
    private val slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
    private val urlBuilderProvider: UrlBuilderProvider,
    private val slackSummaryFunctionTimeout: Duration,
    private val slackConversationSummaryPromptExtractor: SlackConversationSummaryPromptExtractor,
) : MLFunction {
    companion object {
        const val MAX_MESSAGES = 100
        const val CHANNEL_CONTEXT = "CHANNEL_CONTEXT"
        const val THREAD_CONTEXT = "THREAD_CONTEXT"
    }

    override fun isIntegrationEnabled(enabledProviders: Set<Provider>): Boolean {
        return enabledProviders.any { it in setOf(Provider.Slack) }
    }

    override val timeoutOverride: Duration
        get() = slackSummaryFunctionTimeout

    @Suppress("LongMethod", "CyclomaticComplexMethod")
    @MLFunctionDescription(
        description = """
            Call this function if the user asks for a summary of or data contained within a slack channel or slack thread.
            The user will provide you either with a name, url, or id of the slack channel or thread,
            which you will pass to the channelNameOrUrlOrId parameter.

            The user might be asking for a summary of or data within a Slack channel from the Slack channel itself. In that case they might
            say "Summarize this channel", or just "Summarize channel", or "what are the most frequent reported issues in this channel?".
            In that case, you should pass "$CHANNEL_CONTEXT" in the channelNameOrUrlOrId field.

            Similarly, the user might be asking for a summary of or data contained within a Slack thread from the Slack thread itself. In that case they might
            say "Summarize this thread" or "what issues related to deployment were discussed in this thread?". In that case, you should pass "$THREAD_CONTEXT" in the channelNameOrUrlOrId field,

            Slack urls are in the form:
            - Channel URL: https://<workspace>.slack.com/archives/<channel>/<root_thread_ts>/<thread_message_ts>

            Example user phrases in the user query that should result in a call to this function include:
            - "Summarize channel"
            - "Summarize this channel"
            - "What are the key issues being discussed in this channel?"
            - "What are the key issues being discussed in this thread?"
            - "What are the key issues discussed in the engineering channel?"
            - "What are the recent problems we've discussed in the #devops channel?"
            - "What recent deployment failures were mentioned in this channel?"
            - "Summarize #general"
            - "Summarize https://<workspace>.slack.com/archives/C01UJ9Z3Z2Z"
            - "Summarize https://<workspace>.slack.com/archives/C01UJ9Z3Z2Z/p1612345678900000"
            - "Summarize https://<workspace>.slack.com/archives/C01UJ9Z3Z2Z/p1612345678900000?thread_ts=1612345678.000000&cid=C01UJ9Z3Z2Z"
            - "Summarize <#C01UJ9Z3Z2Z|general>"
            - "Summarize this thread"
            - "Summarize the thread found here: https://<workspace>.slack.com/archives/C01UJ9Z3Z2Z/p1612345678900000"

            Parameters:
                - channelNameOrUrlOrId: The name, url, or id of the slack channel or thread. For format for each is as follows:
                    - when channelNameOrUrlOrId is a Name: The name of the channel, e.g. #general, #random, etc., threadOnly is always false in this case.
                    - when channelNameOrUrlOrId is a Url: The URL of the channel or thread ->
                        e.g. https://nextchapter.slack.com/archives/C01UJ9Z3Z2Z, <-- channel url with no ts or thread_ts, always set threadOnly to false in this case
                        https://nextchapter.slack.com/archives/C01UJ9Z3Z2Z/p1612345678900000, <-- thread url containing a ts. The default value for threadOnly when a ts is specified in the url is "true". If the user explicitly asks for channel summary or data contained within a channel, set threadOnly to false.
                        https://nextchapter.slack.com/archives/C01UJ9Z3Z2Z/p1612345678900000?thread_ts=1612345678.000000&cid=C01UJ9Z3Z2Z, <-- thread url with a thread_ts. The default value for threadOnly when a thread_ts is specified is "true". If user explicitly asks for channel summary or data contained within a channel, set threadOnly to false.
                        etc.
                    - when channelNameOrUrlOrId is an ID: The ID of the channel, e.g. C01UJ9Z3Z2Z. threadOnly is always false in this case
                    - SPECIAL CASE: If the user provides a Slack formatted channel ID/Name pair that looks like this: <#C01UJ9Z3Z2Z|general>,
                        you will need to extract the ID from the formatted string and pass it in the channelNameOrUrlOrId field.
                        For example, in this case, the ID is C01UJ9Z3Z2Z. threadOnly is always false in this case
                    - SPECIAL CASE: If the user requests a summary of or data contained within "this channel", but does not specify a url, pass "$CHANNEL_CONTEXT" in the channelNameOrUrlOrId field.
                    - SPECIAL CASE: If the user requests a summary of or data contained within "this thread", but does not specify a url, pass "$THREAD_CONTEXT" in the channelNameOrUrlOrId field,
                        and set the threadOnly parameter to true
                - threadOnly: A boolean flag to indicate if the user is asking for a summary of or data contained within a thread only.
                    IMPORTANT: If the user explicitly asks for a thread summary or data contained within a thread, threadOnly MUST be true. A user can ask for a thread summary or data contained within a thread specifying a thread url or not.
                    If true, the function will only return the messages of the thread.
                    If false, the function will return the messages of the channel.

        """,
    )
    suspend fun getSlackChannelOrThreadSummaryOrData(
        context: MLFunctionExecutionContext,
        channelNameOrUrlOrId: String,
        threadOnly: Boolean,
    ): String {
        val orgId = context.orgId
        val orgMemberId = context.orgMemberId

        LOGGER.debugAsync(
            "orgId" to orgId,
            "orgMemberId" to orgMemberId,
            "channelNameOrUrlOrId" to channelNameOrUrlOrId,
            "threadOnly" to threadOnly,
        ) {
            "Getting slack channel or thread summary"
        }

        val slackTeam = requireNotNull(slackTeamStore.findByOrgs(orgIds = listOf(orgId)).firstOrNull())

        val slackUrl = SlackUrl.parseAll(channelNameOrUrlOrId).firstOrNull()

        // If this condition is true, it means that the user provided a slack url to a thread message
        val threadOnlyOverride = slackUrl?.let {
            threadOnly || (it.threadTs != null && it.ts != null && it.threadTs != it.ts)
        } ?: threadOnly

        val token = slackTokenService.getSlackToken(
            slackTeamId = slackTeam.idValue,
            permissions = listOf(SlackPermission.CHANNELS_HISTORY, SlackPermission.CHANNELS_READ),
        ).value

        val channelDocumentLoader = SlackChannelDocumentLoader(
            token = token,
        )

        val sanitizedChannelNameOrUrlOrId = channelNameOrUrlOrId.let {
            when (it) {
                CHANNEL_CONTEXT, THREAD_CONTEXT -> {
                    context.callingSlackChannelId
                }

                else -> {
                    null
                }
            } ?: it
        }.removePrefix("#")

        val resultingChannelName = when (channelNameOrUrlOrId) {
            CHANNEL_CONTEXT -> "this channel"

            THREAD_CONTEXT -> if (threadOnlyOverride) {
                "this thread"
            } else {
                "this channel"
            }

            else -> channelNameOrUrlOrId
        }

        val threadTs = channelNameOrUrlOrId.let {
            when (it) {
                THREAD_CONTEXT -> {
                    context.callingSlackThreadTs
                }

                else -> {
                    null
                }
            }
        } ?: slackUrl?.threadTs

        val slackChannel = authorizedSlackChannelResolver.resolveSlackChannel(
            orgId = orgId,
            orgMemberId = orgMemberId,
            slackTeamId = slackTeam.idValue,
            channelNameOrUrlOrId = sanitizedChannelNameOrUrlOrId,
        ) ?: throw SlackChannelNotFoundException(
            channelName = when (channelNameOrUrlOrId) {
                CHANNEL_CONTEXT -> "CHANNEL_CONTEXT : ${context.callingSlackChannelId}"
                THREAD_CONTEXT -> "THREAD_CONTEXT : ${context.callingSlackChannelId}"
                else -> channelNameOrUrlOrId
            },
        )

        val slackChannelDocument = runSuspendCatching {
            channelDocumentLoader.loadChannelDocument(
                channelId = slackChannel.slackExternalChannelId,
                threadTs = threadTs,
                threadOnly = threadOnlyOverride,
                limit = MAX_MESSAGES,
            ) { message ->
                message.subtype == null || message.subtype == MessageBotEvent.SUBTYPE_NAME
            }
        }.getOrElse {
            LOGGER.errorAsync(
                t = it,
                "orgId" to orgId,
                "channelNameOrUrlOrId" to channelNameOrUrlOrId,
                "threadOnly" to threadOnly,
            ) { "Error loading channel document" }

            when (it) {
                is SlackConversationHistoryNotInChannelError -> {
                    return """
                    I'm not a member of $resultingChannelName, so I can't provide a summary.

                    Try adding me to the channel here: [Slack Settings](${
                        urlBuilderProvider.dashboard().withOrg(orgId.value).withIntegrations().withSlack().build().asString
                    })
                """.trimIndent()
                }

                is SlackChannelNotFoundException -> {
                    return """
                    I couldn't find the messages for $resultingChannelName.

                    It's possible I don't have access to the channel. If you think I should, try adding me to the channel here: [Slack Settings](${
                        urlBuilderProvider.dashboard().withOrg(orgId.value).withIntegrations().withSlack().build().asString
                    })
                """.trimIndent()
                }
            }
            throw it
        }

        return if (slackChannelDocument != null && slackChannelDocument.messages.isNotEmpty()) {
            val slackUrlBuilder = SlackUrlBuilder(
                baseHost = "${slackTeam.domain}.slack.com",
            )
            val userQuery = context.query ?: "What is the summary of $resultingChannelName?"

            slackConversationSummaryPromptExtractor.generatePrompt(
                slackChannelDocument = slackChannelDocument,
                slackUrlBuilder = slackUrlBuilder,
                userQuery = userQuery,
            )
        } else {
            LOGGER.debugAsync(
                "orgId" to orgId,
                "channelNameOrUrlOrId" to channelNameOrUrlOrId,
                "threadOnly" to threadOnly,
            ) {
                "No messages found"
            }

            """
            No messages were found for $resultingChannelName
            """.trimIndent()
        }
    }
}
