package com.nextchaptersoftware.slack.extractor.utils

import arrow.fx.coroutines.parMap
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.slack.api.model.Message
import kotlin.system.measureTimeMillis
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.toList
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class SlackConversationHistoryNotInChannelError(val error: String) : Exception(error)

class SlackConversationHistoryWalker(
    private val token: String,
    private val slackApiProvider: SlackApiProvider = SlackExtractorApiProvider.INSTANCE,
    private val slackConversationRepliesWalker: SlackConversationRepliesWalker = SlackConversationRepliesWalker(
        token = token,
    ),
) {
    @Suppress("CyclomaticComplexMethod", "LongMethod")
    fun walk(
        channelId: String,
        includeReplies: Boolean = true,
        oldest: String? = null,
        latest: String? = null,
        inclusive: Boolean = false,
        limit: Int? = null,
        messageFilter: (Message) -> Boolean,
    ): Flow<Message> = flow {
        var cursor: String? = null
        var totalCount = 0

        do {
            LOGGER.debugAsync(
                "channelId" to channelId,
                "cursor" to cursor,
                "oldest" to oldest,
                "latest" to latest,
                "inclusive" to inclusive,
                "limit" to limit,
            ) { "Getting conversation history" }

            val history = slackApiProvider.slackConversationApi.getConversationsHistory(
                token = token,
                channelId = channelId,
                cursor = cursor,
                oldest = oldest,
                latest = latest,
                inclusive = inclusive,
                limit = limit,
            )

            LOGGER.debugAsync(
                "channelId" to channelId,
                "cursor" to cursor,
                "oldest" to oldest,
                "latest" to latest,
                "inclusive" to inclusive,
                "limit" to limit,
            ) { "Got conversation history" }

            if (history.error != null && history.error == "not_in_channel") {
                throw SlackConversationHistoryNotInChannelError(history.error)
            }

            var messages = if (history.messages != null) {
                history.messages.filter(messageFilter)
            } else {
                return@flow
            }

            if (includeReplies) {
                if (limit != null) {
                    var currentCount = 0
                    messages = messages.takeWhile {
                        if (currentCount + totalCount > limit) {
                            false
                        } else {
                            val replyCount = if (it.replyCount != null) {
                                it.replyCount
                            } else {
                                0
                            }
                            currentCount += 1 + replyCount
                            true
                        }
                    }
                }

                measureTimeMillis {
                    messages.parMap(context = Dispatchers.IO, concurrency = 30) { message ->
                        if (message.replyCount != null && message.replyCount > 0) {
                            LOGGER.debugAsync(
                                "channelId" to channelId,
                                "ts" to message.ts,
                            ) { "Getting replies for message" }
                            val replies = slackConversationRepliesWalker.walk(
                                channelId = channelId,
                                parentTs = message.ts,
                                oldest = oldest,
                                latest = latest,
                            ).toList()
                            LOGGER.debugAsync(
                                "channelId" to channelId,
                                "ts" to message.ts,
                            ) { "Got replies for message" }
                            Pair(message, replies)
                        } else {
                            Pair(message, null)
                        }
                    }.forEach {
                        val parent = it.first
                        val replies = it.second

                        totalCount++

                        emit(parent)
                        replies?.filter { reply -> reply.ts != parent.ts && reply.subtype == null }?.forEach { reply ->
                            totalCount++
                            emit(reply)
                        }
                    }
                }.also {
                    LOGGER.infoAsync("time" to it) {
                        "Finished processing conversation history"
                    }
                }
            } else {
                totalCount += messages.size
                messages.forEach {
                    emit(it)
                }
            }

            cursor = if ((limit == null || totalCount < limit) && history.isHasMore) {
                history.responseMetadata.nextCursor
            } else {
                null
            }
        } while (cursor != null)
    }
}
