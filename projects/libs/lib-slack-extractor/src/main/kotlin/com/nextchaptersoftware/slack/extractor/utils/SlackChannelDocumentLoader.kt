package com.nextchaptersoftware.slack.extractor.utils

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.slack.extractor.models.SlackChannelDocumentModel
import com.nextchaptersoftware.slack.utils.resolvedName
import com.slack.api.model.Message
import kotlinx.coroutines.flow.toList
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class SlackChannelNotFoundException(channelName: String) : Exception("Channel not found: $channelName")

class SlackChannelDocumentLoader(
    private val token: String,
    private val slackConversationResolver: SlackConversationResolver = SlackConversationResolver(
        token = token,
    ),
    private val slackUserCache: SlackUserCache = SlackUserCache(token = token),
    private val slackConversationHistoryWalker: SlackConversationHistoryWalker = SlackConversationHistoryWalker(
        token = token,
    ),
) {
    @Suppress("CyclomaticComplexMethod", "LongMethod")
    suspend fun loadChannelDocument(
        channelId: String,
        threadTs: String?,
        includeReplies: Boolean = true,
        threadOnly: Boolean = false,
        oldest: String? = null,
        latest: String? = null,
        limit: Int? = null,
        messageFilter: (Message) -> Boolean,
    ): SlackChannelDocumentModel? = runSuspendCatching {
        LOGGER.debugAsync(
            "channelId" to channelId,
            "threadTs" to (threadTs ?: "none"),
            "includeReplies" to includeReplies,
            "threadOnly" to threadOnly,
            "oldest" to oldest,
            "latest" to latest,
            "limit" to limit,
        ) {
            "Loading channel document"
        }

        val slackConversation = slackConversationResolver.resolveSlackConversationForId(channelId = channelId)?.also {
            LOGGER.debugAsync(
                "channelId" to channelId,
                "threadTs" to (threadTs ?: "none"),
                "includeReplies" to includeReplies,
                "threadOnly" to threadOnly,
                "oldest" to oldest,
                "latest" to latest,
                "limit" to limit,
            ) {
                "Found channel, checking access..."
            }
        } ?: throw SlackChannelNotFoundException(channelId)

        val messages = if (threadOnly) {
            threadTs?.let {
                slackConversationHistoryWalker.walk(
                    channelId = slackConversation.id,
                    includeReplies = true,
                    inclusive = true,
                    oldest = threadTs,
                    limit = 1,
                    messageFilter = messageFilter,
                )
            }
        } else {
            slackConversationHistoryWalker.walk(
                channelId = slackConversation.id,
                includeReplies = includeReplies,
                oldest = oldest,
                latest = latest,
                limit = limit,
                messageFilter = messageFilter,
            )
        }

        LOGGER.debugAsync(
            "channelId" to channelId,
            "threadTs" to (threadTs ?: "none"),
            "includeReplies" to includeReplies,
            "threadOnly" to threadOnly,
            "oldest" to oldest,
            "latest" to latest,
            "limit" to limit,
        ) {
            "Got messages"
        }

        val decorated = messages?.decorate(slackUserCache)?.toList()?.let {
            SlackChannelDocumentModel(
                channelName = slackConversation.resolvedName(),
                channelId = slackConversation.id,
                messages = it,
            )
        }

        LOGGER.debugAsync(
            "channelId" to channelId,
            "threadTs" to (threadTs ?: "none"),
            "includeReplies" to includeReplies,
            "threadOnly" to threadOnly,
            "oldest" to oldest,
            "latest" to latest,
            "limit" to limit,
        ) {
            "Decorated content"
        }

        decorated
    }.getOrElse {
        LOGGER.errorAsync(
            t = it,
            "channelId" to channelId,
            "threadTs" to (threadTs ?: "none"),
            "includeReplies" to includeReplies,
            "threadOnly" to threadOnly,
            "oldest" to oldest,
            "latest" to latest,
            "limit" to limit,
        ) {
            "Failed to load channel document"
        }
        throw it
    }
}
