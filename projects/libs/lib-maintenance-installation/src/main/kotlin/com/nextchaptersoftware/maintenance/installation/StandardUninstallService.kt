package com.nextchaptersoftware.maintenance.installation

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.stores.EmbeddingDeleteStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.RapidDeleteStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.slack.notify.SlackNotifier
import org.jetbrains.exposed.sql.Transaction

class StandardUninstallService(
    private val embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val providerUninstallService: ProviderUninstallService,
    private val slackNotifier: SlackNotifier,
    private val rapidDeleteStore: RapidDeleteStore = Stores.rapidDeleteStore,
) : UninstallService {

    override suspend fun uninstall(
        trx: Transaction?,
        orgId: OrgId,
        personId: PersonId,
        installationId: InstallationId,
        notify: Boolean,
    ): Unit = withLoggingContextAsync(
        "orgId" to orgId,
        "installationId" to installationId,
    ) {
        suspendedTransaction(trx) {
            providerUninstallService.uninstall(
                trx = this,
                orgId = orgId,
                personId = personId,
                installationId = installationId,
                notify = notify,
            )
            installationStore.markForDeletion(
                trx = this,
                orgId = orgId,
                installationId = installationId,
            )?.also { installation ->
                rapidDeleteStore.markCollectionForDeletion(
                    trx = this,
                    tenantId = orgId.value,
                    collectionId = installation.id.value,
                )
                embeddingDeleteStore.markInstallationForDeletion(
                    trx = this,
                    namespaceId = orgId,
                    installationId = installation.id,
                )
            }
        }?.also {
            if (notify) {
                slackNotifier.announceIntegrationRemoved(installation = it, personId = personId)
            }
        }
    }
}
