package com.nextchaptersoftware.api.services

import com.nextchaptersoftware.api.models.FileContext
import com.nextchaptersoftware.api.models.MessageReference
import com.nextchaptersoftware.api.models.converters.asMessageReferences
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.models.PullRequestModel
import com.nextchaptersoftware.db.models.ReferenceArtifact
import com.nextchaptersoftware.db.models.ReferenceBundle
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.RepoModel
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.ThreadStore
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.models.asUserLocalFileContext
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.StandardScmFileUrlProvider
import com.nextchaptersoftware.search.semantic.services.file.SemanticSearchFileService
import com.nextchaptersoftware.utils.KotlinUtils.required
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.and

class SemanticSearchFileReferenceService(
    private val semanticSearchFileService: SemanticSearchFileService,
    private val scmWebFactory: ScmWebFactory,
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    private val repoStore: RepoStore = Stores.repoStore,
    private val urlBuilderProvider: UrlBuilderProvider,
) {
    suspend fun getReferences(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        mustValidateRepoAccess: Set<OrgId>,
        personId: PersonId,
        fileContext: FileContext,
    ): List<MessageReference> {
        val repoId = RepoId(fileContext.repoId)
        val installationsByRepo = mapOf(repoId to repoStore.getRepoInstallationId(RepoId(fileContext.repoId)))
        val userLocalFileContext = fileContext.asUserLocalFileContext(installationsByRepo)
        val teamId = scmTeamStore.findIdByContextOrgAndRepo(
            orgId = orgId,
            repoId = userLocalFileContext.repoId,
        )
        val documents = semanticSearchFileService.query(
            mustValidateRepoAccess = mustValidateRepoAccess,
            orgId = orgId,
            orgMemberId = orgMemberId,
            teamId = teamId.required(),
            personId = personId,
            userLocalFileContext = userLocalFileContext,
        )

        val references = documents.mapNotNull {
            it.asReferenceArtifact()
        }

        val referenceBundle = getReferenceBundle(references = references)
        val scmFileUrlProvider = StandardScmFileUrlProvider.create(scmWebFactory = scmWebFactory, scmTeams = referenceBundle.teams())

        return references.asMessageReferences(
            orgId = orgId,
            referenceBundle = referenceBundle,
            urlBuilderProvider = urlBuilderProvider,
            scmFileUrlProvider = scmFileUrlProvider,
            archivedReferencesForMessage = emptyList(),
        )
    }

    private suspend fun getReferenceBundle(
        trx: Transaction? = null,
        references: List<ReferenceArtifact>,
    ): ReferenceBundle = suspendedTransaction(trx = trx) {
        val referenceThreadIds = references.filterIsInstance<ReferenceArtifact.Thread>()
            .map { it.threadId }
        val referencePullRequestIds = references.filterIsInstance<ReferenceArtifact.PullRequest>()
            .map { it.prId }
        val referenceReposForSourceCodeIds = references.filterIsInstance<ReferenceArtifact.SourceCode>()
            .map { it.repoId }

        val referenceThreads = ThreadDAO
            .find {
                ThreadModel.id inList referenceThreadIds.distinct() and ThreadStore.ACTIVE_THREAD_CLAUSE and
                ThreadStore.IS_SHARED_CLAUSE
            }
            .map { it.asDataModel() }

        val referencePullRequests = PullRequestDAO
            .find { PullRequestModel.id inList (referenceThreads.mapNotNull { it.pullRequestId } + referencePullRequestIds).distinct() }
            .map { it.asDataModel() }

        val referenceReposForSourceCode = RepoDAO
            .find { RepoModel.id inList referenceReposForSourceCodeIds }
            .map { it.asDataModel() }

        val referencesTeamsForSourceCode = ScmTeamDAO
            .find { ScmTeamModel.id inList referenceReposForSourceCode.map { it.teamId }.distinct() }
            .map { it.asDataModel() }

        ReferenceBundle(
            threads = referenceThreads,
            pullRequests = referencePullRequests,
            repos = referenceReposForSourceCode,
            teams = referencesTeamsForSourceCode,
            archivedReferences = emptyList(),
        )
    }
}
