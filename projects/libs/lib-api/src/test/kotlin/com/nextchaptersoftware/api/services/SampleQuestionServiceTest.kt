package com.nextchaptersoftware.api.services

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.api.models.MessageContent
import com.nextchaptersoftware.api.threads.services.ThreadService
import com.nextchaptersoftware.bot.services.BotAccountService
import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.fromProductAgentHeader
import com.nextchaptersoftware.db.stores.SampleQuestionStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.events.queue.payloads.SearchPriorityEvent
import com.nextchaptersoftware.semantic.bot.services.MessageAndRelatedModels
import com.nextchaptersoftware.semantic.bot.services.NotifyBotService
import com.nextchaptersoftware.semantic.bot.services.ThreadAndRelatedModels
import io.ktor.util.encodeBase64
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions

class SampleQuestionServiceTest : DatabaseTestsBase() {
    private val sampleQuestionStore: SampleQuestionStore = mock()
    private val searchPriorityEventEnqueueService: SearchPriorityEventEnqueueService = mock()
    private val threadService: ThreadService = mock()
    private val notifyBotService: NotifyBotService = mock()
    private val scmTeamStore: ScmTeamStore = mock()
    private val botAccountService: BotAccountService = mock()

    private val service = SampleQuestionService(
        sampleQuestionStore = sampleQuestionStore,
        searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
        threadService = threadService,
        notifyBotService = notifyBotService,
        botAccountService = botAccountService,
        urlBuilderProvider = StandardUrlBuilderProvider(),
    )

    private val org = MockDataClasses.org()
    private val orgId = org.id
    private val installation = MockDataClasses.installation(orgId = orgId, provider = Provider.GitHub)
    private val scmTeam = MockDataClasses.scmTeam(orgId = orgId, installationId = installation.id)
    private val teamId = scmTeam.id
    private val teamMember = MockDataClasses.member(installationId = installation.id)
    private val sampleQuestionA = MockDataClasses.sampleQuestion()
    private val sampleQuestionB = MockDataClasses.sampleQuestion()
    private val sampleQuestionC = MockDataClasses.sampleQuestion()

    @Test
    fun `getSampleQuestions -- existing questions`() = runTest {
        `when`(scmTeamStore.getOrgId(teamId = teamId))
            .thenReturn(orgId)
        `when`(scmTeamStore.findById(teamId = teamId))
            .thenReturn(scmTeam)
        `when`(sampleQuestionStore.findForMember(orgMemberId = eq(teamMember.orgMemberId)))
            .thenReturn(listOf(sampleQuestionA, sampleQuestionB, sampleQuestionC))

        assertThat(service.getSampleQuestions(orgMemberId = teamMember.orgMemberId))
            .containsExactlyInAnyOrder(sampleQuestionA, sampleQuestionB, sampleQuestionC)

        verifyNoInteractions(searchPriorityEventEnqueueService)
        verify(sampleQuestionStore, times(0)).deleteForOrgMember(orgMemberId = any())
    }

    @Suppress("LongMethod")
    @Test
    fun createThreadFromSampleQuestion() = suspendingDatabaseTest {
        val sampleQuestion = MockDataClasses.sampleQuestion()

        val thread = MockDataClasses.thread()
        val orgMemberId = teamMember.orgMemberId
        val identityId = teamMember.identityId
        val botMember = MockDataClasses.member()

        val personDAO = makePerson()
        val agent = "dashboard"

        `when`(scmTeamStore.getOrgId(teamId = teamId)).thenReturn(orgId)
        `when`(scmTeamStore.findById(teamId = teamId)).thenReturn(scmTeam)
        `when`(botAccountService.upsertBotAccountMember(orgId = orgId)).thenReturn(botMember)
        `when`(sampleQuestionStore.find(orgId = orgId, id = sampleQuestion.id)).thenReturn(sampleQuestion)
        `when`(
            threadService.createThread(
                person = eq(personDAO),
                identityId = eq(identityId),
                orgId = eq(orgId),
                orgMemberId = eq(orgMemberId),
                threadId = any(),
                title = eq(sampleQuestion.query),
                messageId = any(),
                repoId = eq(null),
                messageContent = eq(
                    sampleQuestion.query.asMessageBody().let {
                        MessageContent(
                            content = it.toByteArray().encodeBase64(),
                            version = it.version,
                        )
                    },
                ),
                mentions = eq(listOf(botMember)),
                legacySourcemarks = eq(null),
                sourcemark = eq(null),
                fileMarks = eq(null),
                participants = eq(null),
                agent = eq(ProductAgentType.fromProductAgentHeader(agent)),
                isPrivate = eq(null),
                userLocalContext = eq(null),
            ),
        ).thenReturn(
            ThreadAndRelatedModels(thread = thread, message = mock(), person = mock(), org = org),
        )

        val botMessage = MockDataClasses.message(authorId = botMember.id)

        `when`(
            notifyBotService.createBotMessageAndRunRelatedTasks(
                orgId = eq(orgId),
                thread = eq(thread),
                questionerOrgMemberId = eq(teamMember.orgMemberId),
            ),
        ).thenReturn(
            MessageAndRelatedModels(
                org = org,
                thread = thread,
                message = botMessage,
                newMentionedThreadParticipants = mock(),
            ),
        )

        assertThat(
            service.createThreadFromSampleQuestion(
                person = personDAO,
                identityId = identityId,
                orgId = orgId,
                orgMemberId = orgMemberId,
                sampleQuestionId = sampleQuestion.id,
                xUnblockedProductAgent = agent,
            ),
        ).isEqualTo(
            thread,
        )

        val captor = argumentCaptor<SearchPriorityEvent.ResolvePrefetchBotQuestion>()
        verify(searchPriorityEventEnqueueService, times(1)).enqueueEvent(
            event = captor.capture(),
            priority = eq(MessagePriority.UI),
            withDelay = anyOrNull(),
        )
        val value = captor.firstValue
        assertThat(value.orgId).isEqualTo(orgId)
        assertThat(value.sampleQuestionId).isEqualTo(sampleQuestion.id)
        assertThat(value.humanPersonId).isEqualTo(personDAO.id.value.value)
        assertThat(value.botMessageId).isEqualTo(botMessage.id)
    }
}
