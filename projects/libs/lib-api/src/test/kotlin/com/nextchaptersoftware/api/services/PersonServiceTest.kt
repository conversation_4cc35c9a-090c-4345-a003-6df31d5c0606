package com.nextchaptersoftware.api.services

import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PersonServiceTest : DatabaseTestsBase() {
    private val service = PersonService(
        personStore = Stores.personStore,
    )

    private lateinit var person: PersonDAO

    suspend fun setup() {
        person = makePerson()
    }

    @Test
    fun getPerson() = suspendingDatabaseTest {
        setup()
        val result = service.getPerson(personId = person.idValue)
        assertThat(result?.id).isEqualTo(person.idValue)
    }

    @Test
    fun `setHasSeenTutorial VSCode`() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.VSCode, hasSeenTutorial = true)
        assertThat(result1).isTrue

        val result2 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.VSCode, hasSeenTutorial = false)
        assertThat(result2).isTrue

        val result3 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.VSCode, hasSeenTutorial = false)
        assertThat(result3).isFalse()
    }

    @Test
    fun `setHasSeenTopFile VSCode`() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasSeenTopFile(personId = person.idValue, agent = ProductAgentType.VSCode, hasSeenTopFile = true)
        assertThat(result1).isTrue

        val result2 = service.setHasSeenTopFile(personId = person.idValue, agent = ProductAgentType.VSCode, hasSeenTopFile = false)
        assertThat(result2).isTrue

        val result3 = service.setHasSeenTopFile(personId = person.idValue, agent = ProductAgentType.VSCode, hasSeenTopFile = false)
        assertThat(result3).isFalse()
    }

    @Test
    fun `setHasDismissedToast VSCode`() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasDismissedToast(personId = person.idValue, agent = ProductAgentType.VSCode, hasDismissedToast = true)
        assertThat(result1).isTrue

        val result2 = service.setHasDismissedToast(personId = person.idValue, agent = ProductAgentType.VSCode, hasDismissedToast = false)
        assertThat(result2).isTrue

        val result3 = service.setHasDismissedToast(personId = person.idValue, agent = ProductAgentType.VSCode, hasDismissedToast = false)
        assertThat(result3).isFalse()
    }

    @Test
    fun `setHasSeenTutorial Intellij`() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasSeenTutorial = true)
        assertThat(result1).isTrue

        val result2 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasSeenTutorial = false)
        assertThat(result2).isTrue

        val result3 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasSeenTutorial = false)
        assertThat(result3).isFalse()
    }

    @Test
    fun `setHasSeenTutorial Hub`() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.Hub, hasSeenTutorial = true)
        assertThat(result1).isTrue

        val result2 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.Hub, hasSeenTutorial = false)
        assertThat(result2).isTrue

        val result3 = service.setHasSeenTutorial(personId = person.idValue, agent = ProductAgentType.Hub, hasSeenTutorial = false)
        assertThat(result3).isFalse()
    }

    @Test
    fun `setHasSeenTopFile Intellij`() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasSeenTopFile(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasSeenTopFile = true)
        assertThat(result1).isTrue

        val result2 = service.setHasSeenTopFile(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasSeenTopFile = false)
        assertThat(result2).isTrue

        val result3 = service.setHasSeenTopFile(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasSeenTopFile = false)
        assertThat(result3).isFalse()
    }

    @Test
    fun `setHasDismissedToast IntelliJ`() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasDismissedToast(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasDismissedToast = true)
        assertThat(result1).isTrue

        val result2 = service.setHasDismissedToast(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasDismissedToast = false)
        assertThat(result2).isTrue

        val result3 = service.setHasDismissedToast(personId = person.idValue, agent = ProductAgentType.IntelliJ, hasDismissedToast = false)
        assertThat(result3).isFalse()
    }

    @Test
    fun setHasInstalledHub() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasInstalledHub(personId = person.idValue, hasInstalledHub = true)
        assertThat(result1).isTrue

        val result2 = service.setHasInstalledHub(personId = person.idValue, hasInstalledHub = false)
        assertThat(result2).isTrue

        val result3 = service.setHasInstalledHub(personId = person.idValue, hasInstalledHub = false)
        assertThat(result3).isFalse
    }

    @Test
    fun setHasInstalledDesktop() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasInstalledDesktop(personId = person.idValue, hasInstalledDesktop = true)
        assertThat(result1).isTrue

        val result2 = service.setHasInstalledDesktop(personId = person.idValue, hasInstalledDesktop = false)
        assertThat(result2).isTrue

        val result3 = service.setHasInstalledDesktop(personId = person.idValue, hasInstalledDesktop = false)
        assertThat(result3).isFalse
    }

    @Test
    fun setHasInstalledIDEPlugins() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasInstalledIDEPlugins(personId = person.idValue, hasInstalledIDEPlugins = true)
        assertThat(result1).isTrue

        val result2 = service.setHasInstalledIDEPlugins(personId = person.idValue, hasInstalledIDEPlugins = false)
        assertThat(result2).isTrue

        val result3 = service.setHasInstalledIDEPlugins(personId = person.idValue, hasInstalledIDEPlugins = false)
        assertThat(result3).isFalse
    }

    @Test
    fun setHasSeenAnswersTutorial() = suspendingDatabaseTest {
        setup()
        val result1 = service.setHasSeenAnswersTutorial(personId = person.idValue, hasSeenAnswersTutorial = true)
        assertThat(result1).isTrue

        val result2 = service.setHasSeenAnswersTutorial(personId = person.idValue, hasSeenAnswersTutorial = false)
        assertThat(result2).isTrue

        val result3 = service.setHasSeenAnswersTutorial(personId = person.idValue, hasSeenAnswersTutorial = false)
        assertThat(result3).isFalse
    }
}
