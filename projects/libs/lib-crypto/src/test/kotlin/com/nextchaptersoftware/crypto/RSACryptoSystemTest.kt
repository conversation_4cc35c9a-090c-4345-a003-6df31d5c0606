package com.nextchaptersoftware.crypto

import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.utils.Base64.base64DecodeAsByteArray
import com.nextchaptersoftware.utils.Base64.base64Encode
import com.sksamuel.hoplite.Secret
import java.io.EOFException
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class RSACryptoSystemTest {
    private val contents = Secret("4a172940-faca-4652-b7f2-2bca523c72ec")

    @Test
    fun encryptsAndDecrypts() {
        val keyPair = RSACryptoSystem.generateRSAKeyPair()
        val encryption = RSACryptoSystem.RSAEncryption(keyPair.publicKey)
        val decryption = RSACryptoSystem.RSADecryption(keyPair.privateKey)

        val cipherText = encryption.encrypt(contents)
        val encoded = cipherText.value.base64Encode() // This will be stored in the DB
        val decoded = encoded.base64DecodeAsByteArray()

        // Sanity checks
        assertThat(decoded).isEqualTo(cipherText.value)
        assertThat(contents.value).isNotEqualTo(cipherText.value.decodeToString())

        val decrypted = decryption.decrypt(Ciphertext(decoded))
        assertThat(decrypted.value).isEqualTo(contents.value)
    }

    @Suppress("MaxLineLength")
    private val publicKey =
        "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKzJtMFU5lOf0gVcxZRXbtEsx5dF/0jQoHm6PTfa0KvzoiwuF4gNGRe7R/bFway+g88dmR2gm1VBFdcO/GB7PZ0CAwEAAQ=="

    @Suppress("MaxLineLength")
    private val privateKey =
        "MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEArMm0wVTmU5/SBVzFlFdu0SzHl0X/SNCgebo9N9rQq/OiLC4XiA0ZF7tH9sXBrL6Dzx2ZHaCbVUEV1w78YHs9nQIDAQABAkAeyyjZXrfYte4wUfcNZ6KiiUQ937XuJG5BG3GfE3Jmfm80WH7dRSxgJ2aTB8Y9zytt6KXBSiCNlRHPMHxv2Pl5AiEA3uIi4hyl7g6zQUvKG6PPhv/N97XT6tV28+UXhY1Hz+kCIQDGdhQlj7LnTPYD4sNKbPWlPTUmTYcRyjGnreo2OBODlQIgbGcqNjVe0tkdgD2fcDANKIjUIY2zI31tXJRpC1C6w7kCIQCjkq8IRmguPECI814C+ct9ZfVonH23K+/BRJbGGXH8AQIhAL8WNti9+oBWfKFuQwn/tBbLmUDniWgNXshRBWgKYETB"

    @Test
    fun encryptsAndDecryptsFromBase64EncodedKeys() {
        val encryption = RSACryptoSystem.RSAEncryption(publicKey = publicKey, modulusBitLength = 512)
        val decryption = RSACryptoSystem.RSADecryption(privateKey = privateKey, modulusBitLength = 512)

        val cipherText = encryption.encrypt(contents)
        val encoded = cipherText.value.base64Encode() // This will be stored in the DB
        val decoded = encoded.base64DecodeAsByteArray()

        // Sanity checks
        assertThat(decoded).isEqualTo(cipherText.value)
        assertThat(contents.value).isNotEqualTo(cipherText.value.decodeToString())

        val decrypted = decryption.decrypt(Ciphertext(decoded))
        assertThat(decrypted.value).isEqualTo(contents.value)
    }

    @Test
    fun throwsExceptionWithBadKey() {
        assertThrows<IllegalArgumentException> { RSACryptoSystem.RSAEncryption(privateKey) }
        assertThrows<IllegalArgumentException> { RSACryptoSystem.RSAEncryption(UUID.randomUUID().toString()) }
        assertThrows<EOFException> { RSACryptoSystem.RSAEncryption("blah") }
        assertThrows<IllegalArgumentException> { RSACryptoSystem.RSAEncryption("") }

        assertThrows<IllegalArgumentException> { RSACryptoSystem.RSADecryption(publicKey) }
        assertThrows<IllegalArgumentException> { RSACryptoSystem.RSADecryption(UUID.randomUUID().toString()) }
        assertThrows<EOFException> { RSACryptoSystem.RSADecryption("blah") }
        assertThrows<IllegalArgumentException> { RSACryptoSystem.RSADecryption("") }
    }
}
