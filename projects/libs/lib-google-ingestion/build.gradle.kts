plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:clients:client-google", "default"))
    implementation(project(":projects:libs:lib-google-events", "default"))
    implementation(project(":projects:libs:lib-ingestion", "default"))
    implementation(project(":projects:libs:lib-ingestion-common", "default"))
    implementation(project(":projects:libs:lib-pdf", "default"))

    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.postgresql)
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
    compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
}
