package com.nextchaptersoftware.pr.ingestion

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestIngestion
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.SourcePoint
import com.nextchaptersoftware.db.stores.MessageStore
import com.nextchaptersoftware.db.stores.PullRequestIngestionRepoAndTeamDAO
import com.nextchaptersoftware.db.stores.PullRequestIngestionStore
import com.nextchaptersoftware.db.stores.PullRequestStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.maintenance.scm.ScmMemberProvider
import com.nextchaptersoftware.maintenance.scm.ScmMembershipMaintenance
import com.nextchaptersoftware.pr.ingestion.providers.PullRequestIngestionServiceProvider
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestIngestionEventServiceImpl
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestIngestionEventServiceProvider
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.RedisLock
import com.nextchaptersoftware.scm.MockObjects
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmRepoApi
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.models.MockScmObjects
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import io.ktor.http.Url
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.dao.id.EntityID
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

class PullRequestBulkIngestionServiceTest : DatabaseTestsBase() {
    private val scmTeamStore = mock<ScmTeamStore>()
    private val scmRepoApi = mock<ScmRepoApi>()
    private val scmRepoApiFactory = mock<ScmRepoApiFactory>()
    private val lockProvider = mock<LockProvider>()
    private val pullRequestService = mock<PullRequestService>()
    private val pullRequestStore = mock<PullRequestStore>()
    private val pullRequestCommentService = mock<PullRequestCommentService>()
    private val pullRequestIngestionStore = mock<PullRequestIngestionStore>()
    private val pullRequestIngestionServiceProvider = mock<PullRequestIngestionServiceProvider>()
    private val messageStore = mock<MessageStore>()
    private val scmMembershipMaintenance = mock<ScmMembershipMaintenance>()
    private val pullRequestIngestionService = mock<PullRequestIngestionService>()
    private val pullRequestReviewCommentService = PullRequestReviewCommentService(
        pullRequestService = pullRequestService,
        pullRequestIngestionServiceProvider = pullRequestIngestionServiceProvider,
        dataEventEnqueueService = mock(),
        messageStore = messageStore,
    )
    private val pullRequestIngestionEventService = mock<PullRequestIngestionEventServiceImpl>()
    private val scmMemberProvider: ScmMemberProvider = mock()
    private val lock: RedisLock = mock()

    private val orgDataClass = MockDataClasses.org()
    private val teamDataClass = MockDataClasses.scmTeam(orgId = orgDataClass.id)
    private val orgId: OrgId
    private val teamId: ScmTeamId
    private val scmTeam: ScmTeamDAO
    private val teamEntityId: EntityID<ScmTeamId>

    private val repoDataClass = MockDataClasses.repo()
    private val repoId: RepoId
    private val repoEntityId: EntityID<RepoId>
    private val repoDAO: RepoDAO
    private val repo: Repo

    private val pullRequestIngestion: PullRequestIngestion
    private val pullRequestId = PullRequestId.random()
    private val pullRequestEntityId: EntityID<PullRequestId>
    private val pullRequest: PullRequestDAO

    init {
        orgId = orgDataClass.id
        teamId = teamDataClass.id
        teamEntityId = mock<EntityID<ScmTeamId>>().also { `when`(it.value).thenReturn(teamId) }
        scmTeam = mock<ScmTeamDAO>().also {
            `when`(it.orgId).thenReturn(orgId)
            `when`(it.id).thenReturn(teamEntityId)
            `when`(it.idValue).thenReturn(teamId)
            `when`(it.providerExternalInstallationId).thenReturn("12341234")
            `when`(it.isScmInstalled).thenReturn(teamDataClass.isScmInstalled)
            `when`(it.provider).thenReturn(teamDataClass.provider)
            `when`(it.asDataModel()).thenReturn(teamDataClass)
        }

        repoId = repoDataClass.id
        repoEntityId = mock<EntityID<RepoId>>().also { `when`(it.value).thenReturn(repoId) }
        repoDAO = mock<RepoDAO>().also {
            `when`(it.id).thenReturn(repoEntityId)
            `when`(it.idValue).thenReturn(repoId)
            `when`(it.externalId).thenReturn(repoDataClass.externalId)
            `when`(it.externalOwner).thenReturn(repoDataClass.externalOwner)
            `when`(it.externalName).thenReturn(repoDataClass.externalName)
            `when`(it.asDataModel()).thenReturn(repoDataClass)
            `when`(it.provider).thenReturn(Provider.GitLab)
        }
        repo = repoDAO.asDataModel()

        pullRequestEntityId = mock<EntityID<PullRequestId>>().also { `when`(it.value).thenReturn(pullRequestId) }
        pullRequest = mock<PullRequestDAO>().also {
            `when`(it.id).thenReturn(pullRequestEntityId)
        }

        pullRequestIngestion = MockDataClasses.pullRequestIngestion(
            repo = repoId,
        )
    }

    private val service = PullRequestBulkIngestionService(
        scmTeamStore = scmTeamStore,
        scmRepoApiFactory = scmRepoApiFactory,
        lockProvider = lockProvider,
        pullRequestService = pullRequestService,
        pullRequestStore = pullRequestStore,
        pullRequestCommentService = pullRequestCommentService,
        pullRequestIngestionStore = pullRequestIngestionStore,
        indexingAndEmbeddingService = mock(),
        pullRequestIngestionEventServiceProvider = PullRequestIngestionEventServiceProvider(pullRequestIngestionEventService),
        pullRequestReviewCommentService = pullRequestReviewCommentService,
        scmMembershipMaintenance = scmMembershipMaintenance,
    )

    private val pullRequests = listOf(
        MockScmObjects.scmPullRequest(number = 15),
        MockScmObjects.scmPullRequest(number = 14),
        MockScmObjects.scmPullRequest(number = 13),
    )

    private val pullRequestComments = listOf(
        MockObjects.gitHubIssueComment(
            id = **********,
            htmlUrl = Url("https://github.com/NextChapterSoftware/unblocked/pull/15#issuecomment-**********"),
        ),
        MockObjects.gitHubIssueComment(
            id = **********,
            htmlUrl = Url("https://github.com/NextChapterSoftware/unblocked/pull/14#issuecomment-**********"),
        ),
    ).map { it.asScmPrComment }

    private val pullRequestReviewComments = listOf(
        MockScmObjects.scmPullRequestLineComment(id = 1),
        MockScmObjects.scmPullRequestLineComment(id = 2),
        MockScmObjects.scmPullRequestLineComment(id = 3, inReplyToId = 1),
    )

    init {
        runBlocking {
            `when`(scmTeamStore.isActive(teamId = eq(teamId), activeDuration = any())).thenReturn(true)
            `when`(scmMembershipMaintenance.addMembers(eq(scmTeam.asDataModel()), any()))
                .thenReturn(scmMemberProvider)
            `when`(pullRequestIngestionServiceProvider.get(eq(scmTeam), eq(repo), any(), eq(scmMemberProvider), eq(false), anyOrNull()))
                .thenReturn(pullRequestIngestionService)
            `when`(pullRequestIngestionStore.getPullRequestIngestionsForBulkIngestion())
                .thenReturn(listOf(PullRequestIngestionRepoAndTeamDAO(pullRequestIngestion, repoDAO, scmTeam)))
            `when`(lockProvider.create(id = repo.id.value))
                .thenReturn(lock)
            `when`(lock.acquire()).thenReturn(true)
            `when`(lock.renew()).thenReturn(true)
            `when`(scmRepoApiFactory.getApiFromRepo(any<ScmTeam>(), any<Repo>(), any<Scm>(), any())).thenReturn(scmRepoApi)
            `when`(scmRepoApi.allPullRequests(eq(null), eq(null)))
                .thenReturn(flowOf(HttpClientBatch(items = pullRequests, nextCursor = null)))
            `when`(scmRepoApi.allPullRequestComments(eq(null)))
                .thenReturn(flowOf(HttpClientBatch(items = pullRequestComments, nextCursor = null)))
            `when`(scmRepoApi.allPullRequestCodeComments(eq(null)))
                .thenReturn(flowOf(HttpClientBatch(items = pullRequestReviewComments, nextCursor = null)))
            `when`(pullRequestService.find(repoId, pullRequestReviewComments.first().pullRequestNumber))
                .thenReturn(mock())
            `when`(messageStore.prCommentExists(anyOrNull(), eq(repoDataClass.id), any()))
                .thenReturn(false)
            `when`(pullRequestService.upsert(anyOrNull(), eq(Scm.GitHub), any(), any(), any(), any(), anyOrNull())).thenReturn(pullRequest)
        }
    }

    @Test
    fun bulkIngest() = suspendingDatabaseTest {
        service.run()

        verify(pullRequestService, times(1))
            .upsert(anyOrNull(), eq(Scm.GitHub), eq(orgId), eq(teamId), eq(repoId), eq(pullRequests[0]), anyOrNull())

        verify(pullRequestService, times(1))
            .upsert(anyOrNull(), eq(Scm.GitHub), eq(orgId), eq(teamId), eq(repoId), eq(pullRequests[1]), anyOrNull())

        verify(pullRequestService, times(1))
            .upsert(anyOrNull(), eq(Scm.GitHub), eq(orgId), eq(teamId), eq(repoId), eq(pullRequests[2]), anyOrNull())

        verify(pullRequestCommentService, times(1))
            .upsert(eq(Scm.GitHub), eq(scmTeam), eq(repo), eq(15), eq(pullRequestComments[0]), any(), eq(false))

        verify(pullRequestCommentService, times(1))
            .upsert(eq(Scm.GitHub), eq(scmTeam), eq(repo), eq(14), eq(pullRequestComments[1]), any(), eq(false))

        verify(pullRequestIngestionService, times(1))
            .createThread(
                scm = eq(Scm.GitHub),
                comment = eq(pullRequestReviewComments[0]),
                file = eq(ScmPullRequestFile(SourcePoint.FILE_SHA_SENTINEL_VALUE, pullRequestReviewComments[0].filePath)),
            )

        verify(pullRequestIngestionService, times(1))
            .createThread(
                scm = eq(Scm.GitHub),
                comment = eq(pullRequestReviewComments[1]),
                file = eq(ScmPullRequestFile(SourcePoint.FILE_SHA_SENTINEL_VALUE, pullRequestReviewComments[1].filePath)),
            )

        verify(pullRequestIngestionService, times(1))
            .createReplyMessage(
                scm = eq(Scm.GitHub),
                comment = eq(pullRequestReviewComments[2]),
                restoreArchivedThread = eq(true),
                thread = eq(null),
            )

        verify(pullRequestIngestionStore, times(1))
            .updateBulkIngestionComplete(pullRequestIngestion.id, true)

        verify(pullRequestIngestionEventService, times(3))
            .sendEvent(
                teamId = any(),
                repoId = any(),
                prNumber = any(),
                createUnreads = eq(false),
                priority = any(),
            )
    }
}
