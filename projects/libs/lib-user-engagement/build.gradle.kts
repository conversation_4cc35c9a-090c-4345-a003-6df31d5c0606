plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:libs:lib-common", "default"))
    implementation(project(":projects:libs:lib-insider", "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.postgresql)
}
