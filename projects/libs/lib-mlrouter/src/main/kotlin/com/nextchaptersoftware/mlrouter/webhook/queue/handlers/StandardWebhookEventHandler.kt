package com.nextchaptersoftware.mlrouter.webhook.queue.handlers

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MLRouterId
import com.nextchaptersoftware.db.stores.MLRouterStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.ktor.ForbiddenException
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.mlrouter.ml.service.MLRouterQueryProvider
import com.nextchaptersoftware.mlrouter.webhook.destination.MLRouteDestinationContainer
import com.nextchaptersoftware.mlrouter.webhook.queue.enqueue.MLRouterEventEnqueueService
import com.nextchaptersoftware.mlrouter.webhook.queue.payloads.MLRouterWebhookEvent
import com.nextchaptersoftware.mlrouter.webhook.stores.MLRouterWebhookEventStore
import com.nextchaptersoftware.utils.asUUID
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.nextchaptersoftware.utils.nullIfBlank
import io.ktor.client.HttpClient
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.content.TextContent
import io.ktor.http.isSuccess
import kotlin.time.Duration.Companion.seconds
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class StandardWebhookEventHandler(
    private val mlRouterStore: MLRouterStore = Stores.mlRouterStore,
    private val mlRouterEventEnqueueService: MLRouterEventEnqueueService,
    private val mlRouterQueryProvider: MLRouterQueryProvider,

    ) : TypedEventHandler<MLRouterWebhookEvent.StandardMLRouterWebhookEvent> {

    companion object {
        private const val MAX_ATTEMPTS = 3
        private val BACKOFF_DELAY_SECONDS = 20.seconds
    }

    override suspend fun handle(event: MLRouterWebhookEvent.StandardMLRouterWebhookEvent): Boolean {
        val queueLatency = Instant.nowWithMicrosecondPrecision() - event.enqueuedAt
        LOGGER.infoAsync(
            "queueLatencyMs" to queueLatency.inWholeMilliseconds,
        ) { "Processed standard webhook event" }

        val eventLog: MutableMap<String, String?> = mutableMapOf(
            "eventBody" to event.body,
            "createdAt" to event.enqueuedAt.toEpochMilliseconds().toString(),
        )

        @Suppress("ktlint:nextchaptersoftware:no-run-catching-expression-rule")
        runCatching {
            val routeInfo = suspendedTransaction {
                mlRouterStore.findMLRouter(event.routeId.let(MLRouterId::fromString)) ?: throw ForbiddenException("No event route found!")
            }

            val transformedMessage = routeInfo.transformationPrompt?.nullIfBlank()
                ?.let {
                    LOGGER.debugAsync { "transforming webhook payload" }
                    this.mlRouterQueryProvider.transformMessage(
                        routeInfo.transformationPrompt.toString(),
                        event.body,
                        event.enqueuedAt,
                    )
                }
                ?: event.body

            LOGGER.debugAsync("transformedMessage" to transformedMessage) { "transformed message" }
            eventLog += ("transformedMessage" to transformedMessage)

            val routingDestinations = this.mlRouterQueryProvider.getRoutesForMessage(
                routeInfo.routingPrompt,
                event.body,
                event.enqueuedAt,
            )

            LOGGER.debugAsync("routingDestination" to routingDestinations) { "routing destinations" }
            eventLog += ("routingDestinations" to routingDestinations)

            // Process each routing destination
            routeMessages(
                transformedMessage,
                routingDestinations,
                event.routeId,
            )

            eventLog += ("success" to true.toString())
        }.onFailure {
            val attempts = event.attemptNumber
            if (attempts < MAX_ATTEMPTS) {
                LOGGER.errorAsync(it) { "Error processing standard webhook event due to AI service issue, retrying" }

                val retryEvent = event.copy(attemptNumber = attempts + 1)
                mlRouterEventEnqueueService.enqueueEvent(
                    retryEvent,
                    withDelay = BACKOFF_DELAY_SECONDS * (attempts + 1),
                )
            } else {
                eventLog += ("success" to false.toString())
                LOGGER.errorAsync(it) { "Retry attempts exceeded for giving up" }
            }
        }

        when (MLRouterWebhookEventStore.store(eventLog, event.routeId.asUUID())) {
            null -> {
                LOGGER.errorAsync(
                    "routerId" to event.routeId,
                    "status" to eventLog["status"],
                ) { "failed to insert router event into dynamodb" }
            }

            else -> {
                LOGGER.debugAsync(
                    "routerId" to event.routeId,
                    "status" to eventLog["status"],
                ) { "router event saved to dynamodb" }
            }
        }
        return true
    }

    private suspend fun postRequest(url: String, payload: String): HttpResponse {
        val client = HttpClient()
        return client.post(url) {
            setBody(TextContent(payload, ContentType.Application.Json))
        }
    }

    private suspend fun routeHTTPMessages(
        transformedMessage: String,
        targets: List<String>,
        routeId: String,
    ) {
        targets.forEach { url ->
            val response = postRequest(url, transformedMessage)
            LOGGER.infoAsync { response.toString() }
            when (response.status.isSuccess()) {
                true -> {
                    LOGGER.debugAsync("routeId" to routeId) { "successfully routed webhook to http endpoint" }
                }

                else -> {
                    LOGGER.warnAsync("response" to response) { "failed to route webhook to http endpoint" }
                }
            }
        }
    }

    private suspend fun routeMessages(
        transformedMessage: String,
        routingDestinations: String,
        routeId: String,
    ) {
        val destinationsTargets: MLRouteDestinationContainer = routingDestinations.decode<MLRouteDestinationContainer>()

        destinationsTargets.destinations.forEach { item ->
            when (item.type) {
                "http" -> routeHTTPMessages(transformedMessage, item.targets, routeId)

                "slack" -> {
                    LOGGER.infoAsync { "outgoing slack message handling not implemented yet" }
                }

                "email" -> {
                    LOGGER.infoAsync { "outgoing slack message handling not implemented yet" }
                }

                "none" -> {
                    LOGGER.infoAsync { "target type is none, suppressing the message" }
                }

                else -> {
                    LOGGER.warnAsync("itemType" to item.type) { "unknown target type" }
                }
            }
        }
    }
}
