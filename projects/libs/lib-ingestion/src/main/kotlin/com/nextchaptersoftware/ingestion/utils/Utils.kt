package com.nextchaptersoftware.ingestion.utils

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.db.models.Ingestion

object Utils {
    fun Ingestion.priority() = when (lastSynced) {
        null -> when (initialIngestionCompletedAt) {
            null -> MessagePriority.HIGH

            // Initial ingest
            else -> MessagePriority.LOW // Re-ingest
        }

        else -> MessagePriority.DEFAULT
    }
}
