package com.nextchaptersoftware.slack.auth.oauth

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.SlackUserConnectOrigin
import io.ktor.http.Url
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SlackAuthStateTest {
    @Test
    fun testSlackAuthCompression() {
        val slackAuthState = SlackAuthState(
            orgId = OrgId.random(),
            redirectUrl = Url("http://bresninator.com"),
            identityId = IdentityId.random(),
            personId = PersonId.random(),
            origin = SlackUserConnectOrigin.SLACK,
            installationId = null,
        )

        val encodedAuthState = slackAuthState.encodeAuthState()
        val decodedAuthState = SlackAuthState.decodeAuthState(encodedAuthState)

        assertThat(decodedAuthState).isEqualTo(slackAuthState)
    }
}
