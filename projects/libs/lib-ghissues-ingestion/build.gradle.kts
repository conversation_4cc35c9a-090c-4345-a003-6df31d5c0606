plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:libs:lib-api-threads", "default"))
    implementation(project(":projects:libs:lib-ingestion", "default"))
    implementation(project(":projects:libs:lib-maintenance", "default"))
    implementation(project(":projects:libs:lib-scm", "default"))
    implementation(project(":projects:libs:lib-scm-data", "default"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.postgresql)

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))
    testImplementation(project(":projects:clients:client-scm", "test"))
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
    compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
}
