package com.nextchaptersoftware.slack.bot.models.view

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.db.models.AnswerConcisenessPreference
import com.nextchaptersoftware.db.models.AnswerDepthPreference
import com.nextchaptersoftware.db.models.AnswerPreferences
import com.nextchaptersoftware.db.models.AnswerTonePreference
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMember
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.SlackAutoAnswerMode
import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.stores.DataSourcePresetPreferencesStore
import com.nextchaptersoftware.db.stores.DataSourcePresetStore
import com.nextchaptersoftware.db.stores.SlackChannelPreferencesStore
import com.nextchaptersoftware.db.stores.SlackChannelStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType
import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotChannelSettingsAnswerPreferences
import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotChannelSettingsAnswerPreferencesSelectOption
import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotChannelSettingsAutoAnswerRadioOption
import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotChannelSettingsDataSourceSelectOption
import com.nextchaptersoftware.slack.notify.models.block.SlackBlockText
import com.nextchaptersoftware.slack.notify.models.block.asSlackBlockSelectOptionText
import com.nextchaptersoftware.utils.asUUIDOrNull
import com.slack.api.model.view.View
import com.slack.api.model.view.ViewState
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class DefaultSlackBotChannelSettingsViewBuilder(
    private val clientConfigService: ClientConfigService,
    private val slackChannelStore: SlackChannelStore = Stores.slackChannelStore,
    private val slackChannelPreferencesStore: SlackChannelPreferencesStore = Stores.slackChannelPreferencesStore,
    private val dataSourcePresetStore: DataSourcePresetStore = Stores.dataSourcePresetStore,
    private val dataSourcePresetPreferencesStore: DataSourcePresetPreferencesStore = Stores.dataSourcePresetPreferencesStore,
    private val urlBuilderProvider: UrlBuilderProvider = StandardUrlBuilderProvider(),
) {
    suspend fun build(
        orgId: OrgId,
        orgMember: OrgMember,
        slackTeamId: SlackTeamId,
        slackChannelId: SlackChannelId,
        privateMetadata: SlackBotChannelSettingsViewMetadata,
        selectedDataSourceOption: ViewState.SelectedOption? = null,
    ): View = withLoggingContextAsync(
        "orgId" to orgId,
        "slackTeamId" to slackTeamId,
        "slackChannelId" to slackChannelId,
    ) {
        val isIm = slackChannelStore.findById(id = slackChannelId)?.isIm ?: false

        val dataSourceSelectOptions = createDataSourceSelectOptions(
            orgId = orgId,
            orgMemberId = orgMember.id,
            slackTeamId = slackTeamId,
            slackChannelId = slackChannelId,
            isIm = isIm,
        )

        val autoAnswerRadioOptions = when (isIm) {
            true -> null

            false -> createAutoAnswerRadioOptions(
                orgId = orgId,
                slackTeamId = slackTeamId,
                slackChannelId = slackChannelId,
            )
        }

        val answerPreferencesOptions = createAnswerPreferencesOptions(
            orgId = orgId,
            orgMember = orgMember,
            slackTeamId = slackTeamId,
            slackChannelId = slackChannelId,
            isIm = isIm,
        )

        val selectedDataSourceOptionValue = selectedDataSourceOption?.value
            ?: dataSourceSelectOptions.firstOrNull { it.isSelected }?.optionValue?.text
        val selectedDataSourceOptionUUID = selectedDataSourceOptionValue?.asUUIDOrNull()

        val dataSourceViewLink = selectedDataSourceOptionUUID?.let {
            urlBuilderProvider.dashboard()
                .withOrg(orgId.value)
                .withIntegrations()
                .withDataSourcePreset(
                    dataSourcePresetId = it,
                ).build()
        }

        val answerPreferencesDocLink = answerPreferencesOptions?.let {
            urlBuilderProvider.docs()
                .withAnswerPreferences()
                .build()
        }

        SlackBotChannelSettingsView(
            dataSourceSelectOptions = dataSourceSelectOptions,
            autoAnswerRadioOptions = autoAnswerRadioOptions,
            answerPreferencesOptions = answerPreferencesOptions,
            answerPreferencesDocLink = answerPreferencesDocLink,
            dataSourceViewLink = dataSourceViewLink,
            title = SlackBlockText.ViewTitleText("Channel Settings"),
        ).createView(privateMetadata = privateMetadata.encode())
    }

    private suspend fun createDataSourceSelectOptions(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        slackTeamId: SlackTeamId,
        slackChannelId: SlackChannelId,
        isIm: Boolean,
    ): List<SlackBotChannelSettingsDataSourceSelectOption> {
        val selectedPresetId = when (isIm) {
            true -> dataSourcePresetPreferencesStore.find(
                orgMemberId = orgMemberId,
            )?.dataSourcePresetId

            false -> slackChannelPreferencesStore.findBySlackTeamId(
                slackTeamId = slackTeamId,
                slackChannelIds = listOf(slackChannelId),
            ).firstOrNull()?.dataSourcePresetId
        }

        val dataSourcePresets = dataSourcePresetStore.list(orgId = orgId).sortedBy { it.name.lowercase() }
        return buildList {
            add(
                SlackBotChannelSettingsDataSourceSelectOption.SlackBotChannelSettingsDataSourceAllSelectOption(
                    isSelected = selectedPresetId == null,
                ),
            )
            dataSourcePresets.forEach { dataSourcePreset ->
                add(
                    SlackBotChannelSettingsDataSourceSelectOption.SlackBotChannelSettingsDataSourcePresetSelectOption(
                        optionText = dataSourcePreset.name.ifBlank { "Unnamed Preset" }.asSlackBlockSelectOptionText(),
                        optionValue = dataSourcePreset.id.value.asSlackBlockSelectOptionText(),
                        isSelected = dataSourcePreset.id == selectedPresetId,
                    ),
                )
            }
        }
    }

    private suspend fun createAutoAnswerRadioOptions(
        orgId: OrgId,
        slackTeamId: SlackTeamId,
        slackChannelId: SlackChannelId,
    ): List<SlackBotChannelSettingsAutoAnswerRadioOption>? {
        val isUserAcceptanceTesting = clientConfigService.computeMergedCapabilityForType(
            personId = null,
            orgId = orgId,
            type = ClientCapabilityType.SlackUserAcceptanceTesting,
        )

        if (isUserAcceptanceTesting) {
            // Slack Review has indicated they do not approve of auto-answer.
            LOGGER.warnAsync { "Skipping creating auto answer radio options as we are in user acceptance testing mode." }
            return null
        }

        val autoAnswerMode = slackChannelPreferencesStore.findBySlackTeamId(
            slackTeamId = slackTeamId,
            slackChannelIds = listOf(slackChannelId),
        ).firstOrNull()?.autoAnswerMode ?: SlackAutoAnswerMode.Off

        return listOf(
            SlackBotChannelSettingsAutoAnswerRadioOption.SlackBotChannelSettingsAutoAnswerLessFrequentlyRadioOption(
                isSelected = autoAnswerMode == SlackAutoAnswerMode.LowFrequencyHighQuality,
            ),
            SlackBotChannelSettingsAutoAnswerRadioOption.SlackBotChannelSettingsAutoAnswerMoreFrequentlyRadioOption(
                isSelected = autoAnswerMode == SlackAutoAnswerMode.HighFrequencyLowQuality,
            ),
            SlackBotChannelSettingsAutoAnswerRadioOption.SlackBotChannelSettingsAutoAnswerDisableRadioOption(
                isSelected = autoAnswerMode == SlackAutoAnswerMode.Off,
            ),
        )
    }

    private suspend fun createAnswerPreferencesOptions(
        orgId: OrgId,
        orgMember: OrgMember,
        slackTeamId: SlackTeamId,
        slackChannelId: SlackChannelId,
        isIm: Boolean,
    ): SlackBotChannelSettingsAnswerPreferences? {
        val isUserAcceptanceTesting = clientConfigService.computeMergedCapabilityForType(
            personId = null,
            orgId = orgId,
            type = ClientCapabilityType.SlackUserAcceptanceTesting,
        )

        if (isUserAcceptanceTesting) {
            // Slack Review has indicated they do not approve of auto-answer.
            LOGGER.warnAsync { "Skipping creating auto answer radio options as we are in user acceptance testing mode." }
            return null
        }

        val slackChannelPreferences = when (isIm) {
            true -> orgMember.answerPreferences

            false -> slackChannelPreferencesStore.findBySlackTeamId(
                slackTeamId = slackTeamId,
                slackChannelIds = listOf(slackChannelId),
            ).firstOrNull()?.answerPreferences ?: AnswerPreferences(
                conciseness = AnswerConcisenessPreference.DefaultConcise,
                tone = AnswerTonePreference.DefaultTechnicalTone,
                depth = AnswerDepthPreference.DefaultDepth,
            )
        }

        val concisenessPreferences = listOf(
            AnswerConcisenessPreference.DefaultConcise,
            AnswerConcisenessPreference.LessConcise,
            AnswerConcisenessPreference.MoreConcise,
        )

        val tonePreferences = listOf(
            AnswerTonePreference.DefaultTechnicalTone,
            AnswerTonePreference.LessTechnicalTone,
            AnswerTonePreference.MoreTechnicalTone,
        )

        val depthPreferences = listOf(
            AnswerDepthPreference.DefaultDepth,
            AnswerDepthPreference.LessDepth,
            AnswerDepthPreference.MoreDepth,
        )

        return SlackBotChannelSettingsAnswerPreferences(
            concisenessPreferenceOptions = concisenessPreferences.map {
                SlackBotChannelSettingsAnswerPreferencesSelectOption.Conciseness(
                    optionText = it.label.asSlackBlockSelectOptionText(),
                    optionValue = it.label.asSlackBlockSelectOptionText(),
                    isSelected = it == slackChannelPreferences.conciseness,
                )
            },
            tonePreferenceOptions = tonePreferences.map {
                SlackBotChannelSettingsAnswerPreferencesSelectOption.Tone(
                    optionText = it.label.asSlackBlockSelectOptionText(),
                    optionValue = it.label.asSlackBlockSelectOptionText(),
                    isSelected = it == slackChannelPreferences.tone,
                )
            },
            depthPreferenceOptions = depthPreferences.map {
                SlackBotChannelSettingsAnswerPreferencesSelectOption.Depth(
                    optionText = it.label.asSlackBlockSelectOptionText(),
                    optionValue = it.label.asSlackBlockSelectOptionText(),
                    isSelected = it == slackChannelPreferences.depth,
                )
            },
        )
    }
}
