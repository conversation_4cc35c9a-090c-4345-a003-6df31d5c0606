@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.notification.events.queue.handlers

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.EmailEventType
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Person
import com.nextchaptersoftware.db.stores.EmailEventStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationOrgEvent
import com.nextchaptersoftware.sendgrid.SendGridTemplateEvent
import com.nextchaptersoftware.sendgrid.SendGridTemplateEventHandler
import com.nextchaptersoftware.types.EmailAddress
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SuggestedPeersInviteeEmailHandler(
    private val isEnabled: Boolean,
    private val sender: EmailAddress,
    private val senderName: String,
    private val emailEventStore: EmailEventStore = Stores.emailEventStore,
    private val urlBuilderProvider: UrlBuilderProvider,
    private val sendGridTemplateEventHandler: SendGridTemplateEventHandler,
) : TypedEventHandler<NotificationOrgEvent.SuggestedPeersInviteeEvent> {

    data class EmailObject(
        val person: Person,
        val org: Org,
    )

    @Suppress("MagicNumber")
    override suspend fun handle(event: NotificationOrgEvent.SuggestedPeersInviteeEvent): Boolean {
        if (!isEnabled) {
            return true
        }

        val emailObject = suspendedTransaction {
            val identity = checkNotNull(IdentityDAO.findById(event.senderIdentityId))
            val person = checkNotNull(identity.person?.asDataModel())
            val org = suspendedTransaction { OrgDAO[event.orgId] }.asDataModel()
            EmailObject(person, org)
        }

        val recommendedPeers = event.recipients
        val first = recommendedPeers.firstOrNull() ?: return false
        val navigationUrl = urlBuilderProvider.dashboard().withAskQuestion(
            orgId = event.orgId.value,
            withRecommendedInvitee = true,
        ).build().asString
        val subject = "Invite team members of ${emailObject.org.displayName} to Unblocked"

        val second = recommendedPeers.getOrNull(1)
        val third = recommendedPeers.getOrNull(2)

        val targetEvent = SendGridTemplateEvent.RecommendedInvitee(
            fullName1 = first.displayName,
            userIcon1 = first.userIcon,
            shortName1 = first.shortName,
            fullName2 = second?.displayName,
            userIcon2 = second?.userIcon,
            fullName3 = third?.displayName,
            userIcon3 = third?.userIcon,
            unblockedUrl = navigationUrl,
            fromEmailAddress = sender,
            fromName = senderName,
            toEmailAddress = emailObject.person.primaryEmail,
            subject = subject,
        )

        LOGGER.infoAsync("targetEvent" to targetEvent) {
            "Sending SuggestedPeersInvitee Email"
        }

        sendGridTemplateEventHandler.handle(
            targetEvent,
        )

        runSuspendCatching {
            emailEventStore.createEvent(
                personId = emailObject.person.id,
                orgId = event.orgId,
                emailAddress = emailObject.person.primaryEmail,
                emailEventType = EmailEventType.PeerInviteSuggestion,
            )
        }

        return true
    }
}
