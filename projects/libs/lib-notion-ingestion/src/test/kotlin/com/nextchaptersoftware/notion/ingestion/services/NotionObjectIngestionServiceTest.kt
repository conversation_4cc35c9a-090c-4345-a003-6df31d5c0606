package com.nextchaptersoftware.notion.ingestion.services

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.NotionObjectType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.NotionObjectStore
import com.nextchaptersoftware.integration.queue.redis.cache.IngestionProgressServiceProvider
import com.nextchaptersoftware.notion.api.NotionApiProvider
import com.nextchaptersoftware.notion.api.NotionBlocksApi
import com.nextchaptersoftware.notion.ingestion.queue.enqueue.NotionIngestionEventEnqueueService
import com.nextchaptersoftware.notion.ingestion.services.NotionIngestionService.Companion.INGESTION_PRIORITY_DEFAULT
import com.nextchaptersoftware.notion.models.Block
import com.nextchaptersoftware.notion.models.Page
import com.nextchaptersoftware.notion.models.Results
import com.nextchaptersoftware.test.utils.TestUtils.getResource
import java.util.UUID
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any

class NotionObjectIngestionServiceTest {
    private val blocksApi: NotionBlocksApi = mock()
    private val apiProvider: NotionApiProvider = mock<NotionApiProvider>().also { `when`(it.blocksApi).thenReturn(blocksApi) }
    private val notionEmbeddingService: NotionEmbeddingService = mock()
    private val enqueueService: NotionIngestionEventEnqueueService = mock()
    private val notionBlockService: NotionBlockService = mock()
    private val notionObjectStore: NotionObjectStore = mock()
    private val notionContentIngestionService = NotionContentIngestionService(
        cache = mock(),
        notionBlockService = notionBlockService,
        notionEmbeddingService = notionEmbeddingService,
        notionContentService = NotionContentService(
            apiProvider = apiProvider,
            enqueueService = enqueueService,
            notionObjectStore = notionObjectStore,
        ),
        progressServiceProvider = mock<IngestionProgressServiceProvider?>().also { `when`(it.get(any(), any())).thenReturn(mock()) },
        notionObjectStore = notionObjectStore,
    )

    private val service = NotionObjectIngestionService(
        apiProvider = apiProvider,
        notionObjectStore = notionObjectStore,
        notionContentIngestionService = notionContentIngestionService,
    )

    private val installation = MockDataClasses.installation(provider = Provider.Notion)
    private val identity = MockDataClasses.identity(provider = Provider.Notion)
    private val orgId = installation.orgId
    private val installationId = installation.id

    @Suppress("LongMethod")
    @Test
    fun ingest() = runTest {
        val page = getResource(this, "/results_page_1.json").decode<Results<Page>>().results.first()

        val resultsPage1 = getResource(this, "/page_blocks_page_1.json").decode<Results<Block>>()
        val resultsPage2 = getResource(this, "/page_blocks_page_2.json").decode<Results<Block>>()
        val blockA = getResource(this, "/blocks/440434ae-089d-4d04-be36-b16abaf95927.json").decode<Results<Block>>()
        val blockB = getResource(this, "/blocks/451743c7-29d3-4eb2-b466-eb15facf5319.json").decode<Results<Block>>()
        val blockC = getResource(this, "/blocks/aa21b9ac-0cde-424b-adc9-76eb7688dacd.json").decode<Results<Block>>()
        val blockD = getResource(this, "/blocks/29196956-7661-4a1c-bab2-c1ae6f9b98cc.json").decode<Results<Block>>()
        val blockE = getResource(this, "/blocks/2c1dccef-9a61-4567-8f44-a0fdbeda1e3a.json").decode<Results<Block>>()
        val blockF = getResource(this, "/blocks/50744462-3267-4797-95c6-46b624a86e97.json").decode<Results<Block>>()
        val blockG = getResource(this, "/blocks/bd25d060-8f6b-4264-9000-fbafdd66c6b5.json").decode<Results<Block>>()
        val blockH = getResource(this, "/blocks/96155e16-c62d-4afd-91bd-e04712bd25ad.json").decode<Results<Block>>()

        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = page.id.toString(),
                startCursor = null,
            ),
        ).thenReturn(resultsPage1)
        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = page.id.toString(),
                startCursor = resultsPage1.nextCursor,
            ),
        ).thenReturn(resultsPage2)

        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = "440434ae-089d-4d04-be36-b16abaf95927",
                startCursor = null,
            ),
        ).thenReturn(blockA)
        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = "451743c7-29d3-4eb2-b466-eb15facf5319",
                startCursor = null,
            ),
        ).thenReturn(blockB)
        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = "aa21b9ac-0cde-424b-adc9-76eb7688dacd",
                startCursor = null,
            ),
        ).thenReturn(blockC)
        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = "29196956-7661-4a1c-bab2-c1ae6f9b98cc",
                startCursor = null,
            ),
        ).thenReturn(blockD)
        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = "2c1dccef-9a61-4567-8f44-a0fdbeda1e3a",
                startCursor = null,
            ),
        ).thenReturn(blockE)
        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = "50744462-3267-4797-95c6-46b624a86e97",
                startCursor = null,
            ),
        ).thenReturn(blockF)
        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = "bd25d060-8f6b-4264-9000-fbafdd66c6b5",
                startCursor = null,
            ),
        ).thenReturn(blockG)
        `when`(
            blocksApi.getBlockChildren(
                installationId = installationId,
                identityId = identity.id,
                blockId = "96155e16-c62d-4afd-91bd-e04712bd25ad",
                startCursor = null,
            ),
        ).thenReturn(blockH)

        fun getBlockAndChildren(block: Block): BlockAndChildren {
            return BlockAndChildren(
                block = block,
                children = when (block.id.toString()) {
                    "440434ae-089d-4d04-be36-b16abaf95927" -> blockA.results.map { getBlockAndChildren(it) }
                    "451743c7-29d3-4eb2-b466-eb15facf5319" -> blockB.results.map { getBlockAndChildren(it) }
                    "aa21b9ac-0cde-424b-adc9-76eb7688dacd" -> blockC.results.map { getBlockAndChildren(it) }
                    "29196956-7661-4a1c-bab2-c1ae6f9b98cc" -> blockD.results.map { getBlockAndChildren(it) }
                    "2c1dccef-9a61-4567-8f44-a0fdbeda1e3a" -> blockE.results.map { getBlockAndChildren(it) }
                    "50744462-3267-4797-95c6-46b624a86e97" -> blockF.results.map { getBlockAndChildren(it) }
                    "bd25d060-8f6b-4264-9000-fbafdd66c6b5" -> blockG.results.map { getBlockAndChildren(it) }
                    "96155e16-c62d-4afd-91bd-e04712bd25ad" -> blockH.results.map { getBlockAndChildren(it) }
                    else -> emptyList()
                },
            )
        }

        val blocks = (resultsPage1.results + resultsPage2.results).map { getBlockAndChildren(it) }

        `when`(notionBlockService.blocksToMarkdown(blocks))
            .thenReturn("Blah blah blah")

        service.ingestPage(installation = installation, identityId = identity.id, page = page, lock = mock())

        verify(notionEmbeddingService, times(1)).embed(
            orgId = orgId,
            installationId = installationId,
            externalNotionId = page.id,
            url = page.url,
            title = page.title,
            content = "Blah blah blah",
            createdAt = page.createdTime,
        )

        verify(notionObjectStore, times(1)).markForIngestion(
            installationId = installationId,
            externalNotionId = UUID.fromString("34d93963-3841-4451-9cfe-d6ef0731db8f"),
            type = NotionObjectType.Page,
            priority = INGESTION_PRIORITY_DEFAULT,
        )
    }
}
