package com.nextchaptersoftware.notion.ingestion.services

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.NotionObjectType
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.stores.NotionObjectStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.integration.queue.redis.cache.IngestionProgressServiceProvider
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.log.sensitive.infoSensitiveAsync
import com.nextchaptersoftware.notion.ingestion.redis.cache.NotionIngestionCache
import com.nextchaptersoftware.notion.ingestion.services.NotionPageUtils.shouldNotEmbed
import com.nextchaptersoftware.notion.models.Page
import io.ktor.client.plugins.ClientRequestException
import io.ktor.http.HttpStatusCode
import java.util.UUID
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class NotionContentIngestionService(
    private val cache: NotionIngestionCache,
    private val notionBlockService: NotionBlockService,
    private val notionEmbeddingService: NotionEmbeddingService,
    private val notionContentService: NotionContentService,
    private val progressServiceProvider: IngestionProgressServiceProvider,
    private val notionObjectStore: NotionObjectStore = Stores.notionObjectStore,
) {
    suspend fun ingestPage(
        orgId: OrgId,
        installationId: InstallationId,
        identityId: IdentityId,
        page: Page,
        lock: NotionLock,
    ) = withLoggingContextAsync(
        "orgId" to orgId,
        "installationId" to installationId,
        "pageId" to page.id,
        "lastEditedTime" to page.lastEditedTime,
        "verified" to page.verified,
    ) {
        lock.renewOrThrow()

        when {
            shouldNotEmbed(orgId = orgId, url = page.url) -> {
                LOGGER.infoSensitiveAsync(sensitiveFields = mapOf("page.url" to page.url)) { "Not Embedding Notion page (ignored)" }
                notionObjectStore.markForDeletion(installationId = installationId, externalNotionIds = setOf(page.id))
            }

            page.archived -> {
                LOGGER.infoSensitiveAsync(sensitiveFields = mapOf("page.url" to page.url)) { "Not Embedding Notion page (archived)" }
                notionObjectStore.markForDeletion(installationId = installationId, externalNotionIds = setOf(page.id))
            }

            else -> {
                doIngest(orgId = orgId, installationId = installationId, identityId = identityId, page = page, lock = lock)
            }
        }

        progressServiceProvider.get(orgId = orgId, installationId = installationId).remove(page.id)
        cache.set(orgId = orgId, installationId = installationId, pageId = page.id, lastEditedTime = page.lastEditedTime)
    }

    private suspend fun doIngest(
        orgId: OrgId,
        installationId: InstallationId,
        identityId: IdentityId,
        page: Page,
        lock: NotionLock,
    ) = withLoggingContextAsync(
        "orgId" to orgId,
        "installationId" to installationId,
        "pageId" to page.id,
        "lastEditedTime" to page.lastEditedTime,
    ) {
        if (page.lastEditedTime == notionObjectStore.lastEditedTime(installationId = installationId, externalNotionId = page.id)) {
            LOGGER.debugAsync { "Skipping already ingested Notion page" }
            notionObjectStore.markAsIngested(
                installationId = installationId,
                externalNotionId = page.id,
                type = NotionObjectType.Page,
                lastEditedTime = page.lastEditedTime,
            )
            return@withLoggingContextAsync
        }

        val blocks = notionContentService.getContents(
            orgId = orgId,
            installationId = installationId,
            identityId = identityId,
            blockId = page.id,
            lock = lock,
        )

        val markdown = notionBlockService.blocksToMarkdown(blocks = blocks)

        when (markdown.isBlank()) {
            true -> {
                LOGGER.infoSensitiveAsync(sensitiveFields = mapOf("page.url" to page.url)) { "Not Embedding Notion page (no content)" }
                notionObjectStore.markForDeletion(installationId = installationId, externalNotionIds = setOf(page.id))
            }

            else -> {
                notionEmbeddingService.embed(
                    orgId = orgId,
                    installationId = installationId,
                    externalNotionId = page.id,
                    url = page.url,
                    createdAt = page.createdTime,
                    title = listOfNotNull(page.title, if (page.verified) "(VERIFIED DOCUMENT)" else null).joinToString(" "),
                    content = markdown,
                )
                notionObjectStore.markAsIngested(
                    installationId = installationId,
                    externalNotionId = page.id,
                    type = NotionObjectType.Page,
                    lastEditedTime = page.lastEditedTime,
                )
            }
        }
    }

    suspend fun ingestDatabase(
        orgId: OrgId,
        installationId: InstallationId,
        identityId: IdentityId,
        databaseId: UUID,
        lock: NotionLock,
    ) = withLoggingContextAsync(
        "orgId" to orgId,
        "installationId" to installationId,
        "identityId" to identityId,
        "databaseId" to databaseId,
    ) {
        lock.renewOrThrow()

        LOGGER.infoAsync { "Ingesting Notion database" }

        // TODO Convert the database to markdown and embed it

        // Call blockAndChildren for each block to ingest the pages and databases
        try {
            notionContentService.getContents(
                orgId = orgId,
                installationId = installationId,
                identityId = identityId,
                blockId = databaseId,
                lock = lock,
            )
        } catch (it: ClientRequestException) {
            if (it.response.status == HttpStatusCode.NotFound) {
                LOGGER.warnAsync(it) { "Notion database not found" }
                return@withLoggingContextAsync
            }
            throw it
        }
    }
}
