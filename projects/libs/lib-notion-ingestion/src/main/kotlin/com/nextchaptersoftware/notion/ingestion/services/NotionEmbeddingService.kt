package com.nextchaptersoftware.notion.ingestion.services

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.embedding.models.EmbeddingMetadata
import java.util.UUID
import kotlinx.datetime.Instant

class NotionEmbeddingService(
    private val embeddingEventEnqueueService: EmbeddingEventEnqueueService,
) {
    fun embed(
        orgId: OrgId,
        installationId: InstallationId,
        externalNotionId: UUID,
        url: String,
        title: String?,
        content: String,
        createdAt: Instant,
    ) {
        val metadata = EmbeddingMetadata(
            documentGroup = null,
            documentInstallation = installationId.value,
            documentOrg = orgId.value,
            documentSource = Provider.Notion,
            documentType = DocumentType.Documentation,
            externalUrl = url,
            insightType = InsightType.Documentation,
            sourceDocument = externalNotionId,
            timestamp = createdAt,
            title = title,
        )

        embeddingEventEnqueueService.enqueueEmbedDocumentEvent(
            orgId = orgId,
            installationId = installationId,
            sourceDocumentId = externalNotionId,
            metadata = metadata,
            content = content,
        )
    }

    fun remove(
        orgId: OrgId,
        installationId: InstallationId,
        externalNotionId: UUID,
        priority: MessagePriority = MessagePriority.DEFAULT,
    ) {
        embeddingEventEnqueueService.enqueueRemoveDocumentEmbeddingEvent(
            orgId = orgId,
            installationId = installationId,
            groupId = null,
            sourceDocumentId = externalNotionId,
            priority = priority,
        )
    }
}
