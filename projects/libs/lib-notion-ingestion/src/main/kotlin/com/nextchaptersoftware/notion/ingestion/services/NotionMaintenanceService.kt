package com.nextchaptersoftware.notion.ingestion.services

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.NotionObjectStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.log.kotlin.debugAsync
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class NotionMaintenanceService(
    private val notionEmbeddingService: NotionEmbeddingService,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val notionObjectStore: NotionObjectStore = Stores.notionObjectStore,
) {
    suspend fun removeObjectsMarkedForDeletion() {
        notionObjectStore.listObjectsMarkedForDeletion().groupBy { it.installationId }.forEach { (installationId, objects) ->
            LOGGER.debugAsync(
                "installationId" to installationId,
                "objectsSize" to objects.size,
            ) {
                "Removing objects marked for deletion"
            }

            val installation = installationStore.findById(installationId = installationId, includeDeleted = true)
                ?: return@forEach

            objects.forEach { obj ->
                notionEmbeddingService.remove(
                    orgId = installation.orgId,
                    installationId = installation.id,
                    externalNotionId = obj.externalNotionId,
                    priority = MessagePriority.LOW,
                )
            }

            notionObjectStore.delete(
                installationId = installationId,
                ids = objects.map { it.id },
            )
        }
    }
}
