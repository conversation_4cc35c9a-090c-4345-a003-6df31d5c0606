package com.nextchaptersoftware.api.integration.extension.services

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.cursors.Cursor
import com.nextchaptersoftware.db.models.Collection
import com.nextchaptersoftware.db.models.CollectionId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.CollectionStore
import com.nextchaptersoftware.db.stores.EmbeddingDeleteStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.utils.nullIfEmpty
import io.ktor.http.HttpStatusCode
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

interface CollectionService {
    suspend fun list(
        orgId: OrgId,
        limit: Int,
        after: Cursor?,
        before: Cursor?,
    ): List<Collection>

    suspend fun get(
        orgId: OrgId,
        installationId: InstallationId,
    ): Collection?

    suspend fun create(
        orgId: OrgId,
        installationId: InstallationId,
        name: String,
        description: String,
        iconUrl: String,
    ): Collection

    suspend fun update(
        orgId: OrgId,
        installationId: InstallationId,
        name: String?,
        description: String?,
        iconUrl: String?,
    )

    suspend fun delete(
        orgId: OrgId,
        installationId: InstallationId,
    )
}

class CollectionServiceImpl(
    private val installationStore: InstallationStore = Stores.installationStore,
    private val collectionStore: CollectionStore = Stores.collectionStore,
    private val embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
) : CollectionService {
    companion object {
        const val MAX_COLLECTIONS = 25
        const val MAX_NAME_LENGTH = 32
        const val MAX_DESCRIPTION_LENGTH = 1024
    }

    private fun validateName(name: String) {
        if (name.trim().length > MAX_NAME_LENGTH) {
            throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = "Invalid name",
                detail = "Name must be at most $MAX_NAME_LENGTH characters",
                url = null,
            )
        }
    }

    private fun validateDescription(description: String) {
        if (description.trim().length > MAX_DESCRIPTION_LENGTH) {
            throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = "Invalid description",
                detail = "Description must be at most $MAX_DESCRIPTION_LENGTH characters",
                url = null,
            )
        }
    }

    override suspend fun list(
        orgId: OrgId,
        limit: Int,
        after: Cursor?,
        before: Cursor?,
    ): List<Collection> {
        return collectionStore.list(
            orgId = orgId,
            limit = limit,
            after = after,
            before = before,
        )
    }

    override suspend fun get(
        orgId: OrgId,
        installationId: InstallationId,
    ): Collection? {
        return collectionStore.find(orgId = orgId, installationId = installationId)
    }

    override suspend fun create(
        orgId: OrgId,
        installationId: InstallationId,
        name: String,
        description: String,
        iconUrl: String,
    ): Collection = withLoggingContextAsync(
        "orgId" to orgId,
        "name" to name,
        "description" to description,
        "iconUrl" to iconUrl,
    ) {
        if (collectionStore.count(orgId = orgId) > MAX_COLLECTIONS) {
            LOGGER.errorAsync { "Collection limit reached" }
            throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = "Collection limit reached",
                detail = null,
                url = null,
            )
        }

        val sanitizedName = name.trim().nullIfEmpty()?.also { validateName(it) }
            ?: throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = "Invalid name",
                detail = "Name is required",
                url = null,
            )

        val sanitizedDescription = description.trim().nullIfEmpty()?.also { validateDescription(it) }
            ?: throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = "Invalid description",
                detail = "Description is required",
                url = null,
            )

        val sanitizedIconUrl = iconUrl.trim().nullIfEmpty()
            ?: throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = "Invalid iconUrl",
                detail = "Icon URL is required",
                url = null,
            )

        val provider = Provider.CustomIntegration
        val collectionId = CollectionId.random()

        suspendedTransaction {
            val installation = installationStore.insert(
                trx = this,
                id = installationId,
                orgId = orgId,
                installationExternalId = collectionId.value.toString(),
                provider = provider,
            )

            collectionStore.create(
                trx = this,
                id = collectionId,
                installationId = installation.id,
                name = sanitizedName,
                description = sanitizedDescription,
                iconUrl = sanitizedIconUrl,
            )
        }
    }

    override suspend fun update(
        orgId: OrgId,
        installationId: InstallationId,
        name: String?,
        description: String?,
        iconUrl: String?,
    ) {
        val collection = collectionStore.find(orgId = orgId, installationId = installationId)
            ?: throw NotFoundException()

        val sanitizedName = name?.trim()?.nullIfEmpty()?.also { validateName(it) }

        val sanitizedDescription = description?.trim()?.nullIfEmpty()?.also { validateDescription(it) }

        val sanitizedIconUrl = iconUrl?.trim()?.nullIfEmpty()

        collectionStore.update(
            orgId = orgId,
            id = collection.id,
            name = sanitizedName,
            description = sanitizedDescription,
            iconUrl = sanitizedIconUrl,
        )
    }

    override suspend fun delete(
        orgId: OrgId,
        installationId: InstallationId,
    ) {
        val collection = collectionStore.find(orgId = orgId, installationId = installationId)
            ?: return

        require(collection.installationId == installationId) { "Collection installationId does not match installationId" }

        // Don't delete the collection, just mark it for deletion and let the cleanup job handle it
        installationStore.markForDeletion(
            orgId = orgId,
            installationId = installationId,
        )

        embeddingDeleteStore.markInstallationForDeletion(
            namespaceId = orgId,
            installationId = installationId,
        )
    }
}
