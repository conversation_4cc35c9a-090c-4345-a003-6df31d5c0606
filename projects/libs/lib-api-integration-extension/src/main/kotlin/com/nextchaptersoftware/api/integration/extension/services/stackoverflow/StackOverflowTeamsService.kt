package com.nextchaptersoftware.api.integration.extension.services.stackoverflow

import com.nextchaptersoftware.api.models.StackOverflowTeam
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.sksamuel.hoplite.Secret

class StackOverflowTeamsService(
    private val stackOverflowTeamsDelegate: StackOverflowTeamsDelegateInterface,
    private val installationStore: InstallationStore = Stores.installationStore,
) {

    suspend fun get(
        orgId: OrgId,
    ): List<StackOverflowTeam> {
        val installation = installationStore.getIntegrationInstallations(
            orgId = orgId,
            providers = listOf(Provider.StackOverflowTeams),
        ).minByOrNull {
            it.createdAt
        }
        return listOfNotNull(installation?.asStackOverflowTeam)
    }

    suspend fun upsert(
        id: InstallationId,
        orgId: OrgId,
        personId: PersonId,
        orgMemberId: OrgMemberId,
        hostUrl: String?,
        accessToken: Secret,
    ): StackOverflowTeam {
        return stackOverflowTeamsDelegate.upsert(
            installationId = id,
            orgId = orgId,
            personId = personId,
            orgMemberId = orgMemberId,
            hostUrl = hostUrl,
            accessToken = accessToken,
        )
    }
}

val Installation.asStackOverflowTeam
    get() = StackOverflowTeam(
        id = id.value,
        teamId = orgId.value, // this assignment of org to team is correct
        name = installationExternalId,
        hostUrl = "https://stackoverflowteams.com/c/$installationExternalId",
    )
