package com.nextchaptersoftware.ingest.notion.jobs

import com.nextchaptersoftware.notion.ingestion.services.NotionMaintenanceService
import com.nextchaptersoftware.service.BackgroundJob

class NotionMaintenanceJob(
    private val maintenanceService: NotionMaintenanceService,
) : BackgroundJob {
    override val name: String = javaClass.simpleName

    override suspend fun run() {
        maintenanceService.removeObjectsMarkedForDeletion()
    }
}
