package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.avatar
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.LinearTeam
import com.nextchaptersoftware.db.models.LinearTeamDAO
import com.nextchaptersoftware.db.models.LinearTeamId
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.h1

object LinearTeamPage {
    suspend fun RoutingContext.renderLinearTeamPage(
        page: AdminPage,
    ) {
        val breadcrumb = call.makeBreadcrumb()

        val path = call.request.path()
        val org = call.parameters.org()
        val related = listOf(
            MenuItem(
                href = "$path/linearTeamIngestions",
                label = "Linear Team Ingestions",
                description = "Ingestions for this linear team.",
            ),
        )

        val adminIdentity = call.getAdminIdentity()
        val linearTeamId = call.parameters.requiredId("linearTeamId", ::LinearTeamId)

        val linearTeam = Database.suspendedTransaction {
            requireNotNull(LinearTeamDAO.findById(linearTeamId)).asDataModel()
        }

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            relatedMenu { renderRelatedMenu(related) }
            content {
                h1 { +page.label }
                renderLinearTeam(linearTeam)
            }
        }
    }

    private fun FlowContent.renderLinearTeam(
        linearTeam: LinearTeam,
    ) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Name", linearTeam.name)
                property("Avatar") { avatar(linearTeam) }
                property("Created", linearTeam.createdAt)
                property("Modified", linearTeam.modifiedAt)
                property("Slack Team Id", linearTeam.linearTeamId)
                property("Traits") {
                }
            }
        }
    }
}
