package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.api.utils.CallExtensions.respondNotFound
import com.nextchaptersoftware.config.reflection.ConfigReflectionUtils
import com.nextchaptersoftware.config.serialization.ConfigRenderer
import io.ktor.http.ContentDisposition
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.header
import io.ktor.server.response.respondText
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.id
import kotlinx.html.img
import kotlinx.html.pre
import kotlinx.html.style

object ConfigsPage {

    suspend fun RoutingContext.renderConfigsPage(page: AdminPage) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()
        val path = call.request.path()

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            actionMenu { renderActionMenu(items = getConfigActions(path = path)) }
            content {
                div(classes = "d-flex justify-content-between") {
                    h1 { +page.label }
                }

                renderConfigsPageWithHtmx(path = path)
            }
        }
    }

    private fun getConfigActions(
        path: String,
    ) = buildList {
        add(
            MenuItem(
                label = "Export Configs",
                style = BootstrapStyle.Primary,
                href = "$path/exportConfigs",
                description = "Exports all resolved configs as hocon to a file",
            ),
        )
    }

    suspend fun RoutingContext.exportConfigs(configRenderer: ConfigRenderer = ConfigRenderer()) {
        val classInstances = ConfigReflectionUtils.findClassesWithInstanceUsingConfigLoader()
        val configHocon = buildString {
            classInstances.forEach { classInstance ->
                val renderedText = configRenderer.render(data = classInstance.instance)
                appendLine(renderedText)
            }
        }

        if (configHocon.isEmpty()) {
            call.respondNotFound()
            return
        }

        call.response.header(
            HttpHeaders.ContentDisposition,
            ContentDisposition.Attachment.withParameter(ContentDisposition.Parameters.FileName, "config.conf")
                .toString(),
        )
        call.respondText(
            text = configHocon,
            contentType = ContentType.Text.Plain,
            status = HttpStatusCode.OK,
        )
    }

    private fun FlowContent.renderConfigsPageWithHtmx(path: String) {
        div {
            h1 { +"Configuration Details" }
            pre(classes = "bg-secondary p-3 rounded font-monospace") {
                id = "config-details"
                attributes["hx-get"] = "$path/getFullConfig"
                attributes["hx-trigger"] = "load"
                attributes["hx-target"] = "#config-details"
                img(
                    alt = "Loading …",
                    src = "$WEB_ROOT/images/bars.svg",
                ) {
                    style = "filter: brightness(160%); opacity: .85;"
                }
            }
        }
    }

    suspend fun RoutingContext.getFullConfig(configRenderer: ConfigRenderer = ConfigRenderer()) {
        val classInstances = ConfigReflectionUtils.findClassesWithInstanceUsingConfigLoader()
        val configHocon = buildString {
            classInstances.forEach { classInstance ->
                val renderedText = configRenderer.render(data = classInstance.instance)
                appendLine(renderedText)
            }
        }

        if (configHocon.isEmpty()) {
            call.respondText("No configuration found", ContentType.Text.Plain, HttpStatusCode.NotFound)
            return
        }

        call.respondText(configHocon, ContentType.Text.Plain, HttpStatusCode.OK)
    }
}

fun main() {
    val classInstances = ConfigReflectionUtils.findClassesWithInstanceUsingConfigLoader()
    println("Classes with INSTANCE using ConfigLoader:")
    classInstances.forEach { classInstance ->
        println("Class: ${classInstance.clazz.name}")
        val renderedText = ConfigRenderer().render(data = classInstance.instance)
        println(renderedText)
    }
}
