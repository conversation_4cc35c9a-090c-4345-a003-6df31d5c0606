package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalRepo
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalScmTeam
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminTitles.title
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItemInput
import com.nextchaptersoftware.adminwebservice.adminweb.component.Text.textSmall
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeDiff
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.triageRefreshMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.triageRerunMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagesPage.renderTriages
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.utils.CallExtensions.respondJson
import com.nextchaptersoftware.ci.CIProjectApi
import com.nextchaptersoftware.ci.CIProjectApiFactory
import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.CiFetchApi
import com.nextchaptersoftware.ci.logging.LogFocusService
import com.nextchaptersoftware.ci.logging.focus.LogCleaner
import com.nextchaptersoftware.db.models.Build
import com.nextchaptersoftware.db.models.BuildBundle
import com.nextchaptersoftware.db.models.BuildId
import com.nextchaptersoftware.db.models.BuildJob
import com.nextchaptersoftware.db.models.BuildJobBundle
import com.nextchaptersoftware.db.models.BuildJobId
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.stores.Stores.buildJobStore
import com.nextchaptersoftware.db.stores.Stores.buildStore
import com.nextchaptersoftware.db.stores.Stores.buildTriageStore
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.utils.Base64.base64Decode
import com.nextchaptersoftware.utils.Base64.urlSafeBase64Encode
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.CollectionsUtils.onNotEmpty
import com.nextchaptersoftware.utils.KotlinUtils.required
import io.ktor.http.Parameters
import io.ktor.http.Url
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respondText
import io.ktor.server.routing.RoutingContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.toList
import kotlinx.html.FlowContent
import kotlinx.html.ThScope
import kotlinx.html.a
import kotlinx.html.code
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.h4
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object CiBuildPage {

    suspend fun RoutingContext.renderCiBuildPage(page: AdminPage) {
        val path = call.request.path()

        val adminIdentity = call.getAdminIdentity()
        val breadcrumb = call.makeBreadcrumb()

        val org = call.parameters.org()
        val scmTeam = call.parameters.optionalScmTeam()
        val repo = call.parameters.optionalRepo()
        val build = call.parameters.build()

        val pullRequest = build.pullRequest()
        val ciInstallation = build.ciInstallation()

        val jobs = buildJobStore.findAll(buildId = build.id)?.sortedByDescending { it.createdAt }
        val triages = buildTriageStore.findAllBundles(buildIds = listOf(build.id))?.sortedByDescending { it.triage.createdAt }

        val basePath = path.removeSuffix("/builds/${build.id}")

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org = org, scmTeam = scmTeam) }
            relatedMenu {
                renderRelatedMenu(
                    items = relatedMenuItems(
                        basePath = basePath,
                        build = build,
                        ciInstallation = ciInstallation,
                    ),
                )
            }
            actionMenu {
                renderActionMenu(
                    items = actionMenuItems(
                        path = path,
                        build = build,
                        jobs = jobs,
                    ),
                )
            }
            content {
                h1 { +page.label }

                insert(PropertyListTemplate()) {
                    propertyList {
                        property("Number", build.displayNumber)
                        property("Name", build.displayName)
                        property("Runner", build.runner)
                        property("Type", build.type)
                        property("External Id", build.externalId)
                        repo?.let {
                            property("Repo", repo.fullName)
                        }
                        property("PR", title(pullRequest))
                        property("Base") { build.baseSha?.let { code { +it } } ?: +"-" }
                        property("Head") { code { +build.headSha } }
                        property("Html Url", build.htmlUrl)
                        property("Api Url", build.apiUrl)
                    }
                }

                insert(PropertyListTemplate()) {
                    propertyList {
                        property("Attempt", build.attempt)
                        property("Status", build.status)
                        property("Result", build.result)
                        property("Created At", build.createdAt)
                        property("Started At", build.startedAt)
                        property("Completed At", build.completedAt)
                        property("Modified At", build.modifiedAt)
                    }
                }

                jobs?.onNotEmpty {
                    h4(classes = "mt-5") { +"Build Jobs" }
                    renderBuildJobs(jobs = it)
                }

                triages?.onNotEmpty {
                    h4(classes = "mt-5") { +"Build Triages" }
                    renderTriages(
                        basePath = basePath,
                        triages = it,
                    )
                }
            }
        }
    }

    internal fun Parameters.buildId(): BuildId = requiredId("buildId", ::BuildId)

    internal suspend fun Parameters.build(): Build = buildId().let {
        buildStore.findById(it).required { "Unknown build" }
    }

    internal suspend fun Parameters.buildBundle(): BuildBundle = buildId().let {
        buildStore.findAllBundles(buildIds = listOf(it))?.firstOrNull() ?: error("Unknown build")
    }

    internal fun Parameters.jobId(): BuildJobId = requiredId("jobId", ::BuildJobId)

    internal suspend fun Parameters.jobBundle(): BuildJobBundle = jobId().let {
        buildJobStore.findBundle(jobId = it).required { "Missing job" }
    }

    private fun relatedMenuItems(
        basePath: String,
        ciInstallation: Installation,
        build: Build,
    ) = listOfNotNull(
        MenuItem(
            href = "$WEB_ROOT/orgs/${ciInstallation.orgId}/installations/${ciInstallation.provider}/${ciInstallation.id}",
            label = "CI Installation",
            description = "The CI Installation for this build",
        ),
        MenuItem(
            href = "$basePath/pullRequests/${build.pullRequestId}",
            label = "Pull Request",
            description = "The Pull Request for this build",
        ),
    )

    private fun actionMenuItems(
        path: String,
        build: Build,
        jobs: List<BuildJob>?,
    ) = listOfNotNull(
        buildFetchMenuItem(
            path = path,
            build = build,
        ),
        buildGetLogsMenuItem(
            path = path,
            buildId = build.id,
        ),
        triageRefreshMenuItem(
            path = path,
            pullRequestId = build.pullRequestId,
        ),
        triageRerunMenuItem(
            path = path,
            build = build,
        ),
        jobs?.let {
            buildJobFetchMenuItem(
                path = path,
                jobs = jobs,
                buildId = build.id,
            )
        },
        jobs?.let {
            buildJobGetLogsMenuItem(
                path = path,
                jobs = jobs,
            )
        },
        jobs?.let {
            buildJobGetFocusLogsMenuItem(
                path = path,
                jobs = jobs,
            )
        },
        jobs?.let {
            buildJobGetAnnotationsMenuItem(
                path = path,
                jobs = jobs,
            )
        },
    )

    private fun FlowContent.renderBuildJobs(
        jobs: Collection<BuildJob>,
    ) {
        div(classes = "table-responsive") {
            table(classes = "table table-hover align-middle") {
                thead {
                    tr {
                        th(scope = ThScope.col) { +"#" }
                        th(scope = ThScope.col) { +"Name" }
                        th(scope = ThScope.col) { +"Type" }
                        th(scope = ThScope.col) { +"External Id" }
                        th(scope = ThScope.col) { +"Html Url" }
                        th(scope = ThScope.col) { +"Api Url" }
                        th(scope = ThScope.col) { +"Attempt" }
                        th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                        th(scope = ThScope.col, classes = "noSearch") { +"Started" }
                        th(scope = ThScope.col, classes = "noSearch") { +"Completed" }
                        th(scope = ThScope.col, classes = "noSearch") { +"Updated" }
                        th(scope = ThScope.col, classes = "noSearch") { +"Run Time" }
                        th(scope = ThScope.col) { +"Status" }
                        th(scope = ThScope.col) { +"Result" }
                    }
                }
                tbody(classes = "table-dark") {
                    jobs
                        .forEachIndexed { index, job ->
                            tr {
                                td { +"${jobs.size - index}" }
                                td {
                                    +job.displayName
                                    textSmall(job.id)
                                }
                                td { +job.type }
                                td { +job.externalId }
                                td(classes = "text-truncate") {
                                    style = "max-width: 150px;"
                                    job.htmlUrl?.let { url ->
                                        a(href = url.asString, classes = "text-truncate d-inline-block w-16") {
                                            +"$url"
                                        }
                                    } ?: +"-"
                                }
                                td(classes = "text-truncate") {
                                    style = "max-width: 150px;"
                                    job.apiUrl?.let { url ->
                                        a(href = url.asString, classes = "text-truncate d-inline-block w-100") {
                                            +"$url"
                                        }
                                    } ?: +"-"
                                }
                                td { +"${job.attempt ?: "-"}" }
                                td { timeAgo(job.createdAt) }
                                td { timeAgo(job.startedAt) }
                                td { timeAgo(job.completedAt) }
                                td { timeAgo(job.modifiedAt) }
                                td {
                                    when {
                                        job.isRunning -> timeDiff(from = job.startedAt ?: job.createdAt)
                                        else -> timeDiff(from = job.startedAt ?: job.createdAt, until = job.completedAt ?: job.modifiedAt)
                                    }
                                }
                                td { asBadge(job.status) }
                                td { asBadge(job.result) }
                            }
                        }
                }
            }
        }
    }

    private fun ciFetchMenuItem(
        path: String,
        label: String,
        description: String? = null,
        buildId: BuildId,
        urlLabel: String? = null,
        urls: Collection<Pair<String, Url>>,
    ) = MenuItem(
        href = "$path/ciFetch",
        label = label,
        description = description ?: "Fetch data from live API request to CI provider",
        inputs = listOfNotNull(
            MenuItemInput.Hidden(
                name = "buildId",
                defaultValue = "$buildId",
            ),
            when (urls.size) {
                1 -> MenuItemInput.Hidden(
                    name = "url",
                    defaultValue = urls.first().second.asString.urlSafeBase64Encode(),
                )

                else -> MenuItemInput.SelectInput(
                    label = urlLabel ?: "url",
                    name = "url",
                    items = urls.map {
                        MenuItemInput.SelectInput.Item(
                            label = it.first,
                            value = it.second.asString.urlSafeBase64Encode(),
                        )
                    },
                )
            },
        ),
    )

    suspend fun RoutingContext.ciFetch(
        ciProjectApiFactory: CIProjectApiFactory,
    ) {
        val bodyParams = call.receiveParameters()
        val apiUrl = bodyParams["url"].required().base64Decode()
        val bundle = bodyParams.buildBundle()

        val ciProjectApi = ciProjectApiFactory.getProjectApi(
            ciInstallation = bundle.ciInstallation,
            projectContext = bundle.build.projectContext.decode<CIProjectContext>(),
        )

        val json = when (ciProjectApi) {
            is CiFetchApi -> ciProjectApi.fetch(apiUrl)

            else -> {
                call.respondText { "Fetch not supported" }
                return
            }
        }

        when (json) {
            null -> call.respondText { "null" }
            else -> call.respondJson { json }
        }
    }

    fun buildFetchMenuItem(
        path: String,
        build: Build,
    ) = build.apiUrl?.let { apiUrl ->
        ciFetchMenuItem(
            path = path,
            label = "CI Build: fetch live",
            buildId = build.id,
            urls = listOf(
                "build" to apiUrl,
            ),
        )
    }

    fun buildGetLogsMenuItem(
        path: String,
        buildId: BuildId,
        description: String? = null,
    ) = MenuItem(
        href = "$path/buildGetLogs",
        label = "CI Build: get logs (raw)",
        description = description ?: "Gets build logs from live API request to CI provider",
        inputs = listOf(
            MenuItemInput.Hidden(
                name = "buildId",
                defaultValue = "$buildId",
            ),
        ),
    )

    suspend fun RoutingContext.buildGetLogs(
        ciProjectApiFactory: CIProjectApiFactory,
    ) {
        val bodyParams = call.receiveParameters()
        val bundle = bodyParams.buildBundle()

        val text = ciProjectApiFactory.getProjectApi(
            ciInstallation = bundle.ciInstallation,
            projectContext = bundle.build.projectContext.decode<CIProjectContext>(),
        ).use {
            it.buildLogs(
                buildExternalId = bundle.build.externalId,
            )
                .toList()
                .nullIfEmpty()
                ?.joinToString("\n")
        }

        call.respondText {
            text ?: "null"
        }
    }

    fun buildJobFetchMenuItem(
        path: String,
        description: String? = null,
        buildId: BuildId,
        jobs: Collection<BuildJob>,
    ) = jobs
        .mapNotNull {
            it.apiUrl?.let { apiUrl ->
                val label = "${it.displayName} -- ${it.externalId} -- ${it.status} -- ${it.result}"
                label to apiUrl
            }
        }
        .nullIfEmpty()
        ?.let { urls ->
            ciFetchMenuItem(
                path = path,
                label = "CI Job: fetch live",
                description = description,
                buildId = buildId,
                urlLabel = "Job",
                urls = urls,
            )
        }

    fun buildJobGetLogsMenuItem(
        path: String,
        jobs: Collection<BuildJob>,
        description: String? = null,
    ) = MenuItem(
        href = "$path/buildJobGetLogs",
        label = "CI Job: get logs (raw)",
        description = description ?: "Gets job logs from live API request to CI provider",
        inputs = listOf(
            MenuItemInput.SelectInput(
                label = "Build job",
                name = "jobId",
                items = jobs.map {
                    MenuItemInput.SelectInput.Item(
                        label = "${it.displayName} -- ${it.externalId} -- ${it.status} -- ${it.result}",
                        value = "${it.id}",
                    )
                },
            ),
        ),
    )

    suspend fun RoutingContext.buildJobGetLogs(
        ciProjectApiFactory: CIProjectApiFactory,
    ) {
        val bodyParams = call.receiveParameters()
        val text = resolveBuildLogs(ciProjectApiFactory, bodyParams.jobId())
            ?.let { LogCleaner.cleanLogStream(it) }
            ?.toList()
            ?.joinToString("\n")

        call.respondText {
            text ?: "null"
        }
    }

    private suspend fun BuildJobBundle.getApi(
        ciProjectApiFactory: CIProjectApiFactory,
    ): CIProjectApi {
        val projectContext = build.projectContext.decode<CIProjectContext>()
        return ciProjectApiFactory.getProjectApi(ciInstallation = ciInstallation, projectContext = projectContext)
    }

    private fun resolveBuildLogs(
        ciProjectApiFactory: CIProjectApiFactory,
        jobId: BuildJobId,
    ): Flow<String> = flow {
        val bundle = buildJobStore.findBundle(jobId = jobId).required { "Unknown job" }
        // danger: .use will close the underlying flow operation
        //          so a collect and emit in place is required to propagate correctly
        bundle.getApi(ciProjectApiFactory).use {
            it.jobLogs(
                buildExternalId = bundle.build.externalId,
                jobExternalId = bundle.job.externalId,
            )
                .also { emitAll(it) }
        }
    }

    fun buildJobGetFocusLogsMenuItem(
        path: String,
        jobs: Collection<BuildJob>,
        description: String? = null,
    ) = MenuItem(
        href = "$path/buildJobGetFocusLogs",
        label = "CI Job: get logs (focus)",
        description = description ?: "Gets job focus log as it was feed to the triage model",
        inputs = listOf(
            MenuItemInput.SelectInput(
                label = "Build job",
                name = "jobId",
                items = jobs.map {
                    MenuItemInput.SelectInput.Item(
                        label = "${it.displayName} -- ${it.externalId} -- ${it.status} -- ${it.result}",
                        value = "${it.id}",
                    )
                },
            ),
        ),
    )

    suspend fun RoutingContext.buildJobGetFocusLogs(
        ciProjectApiFactory: CIProjectApiFactory,
        logFocusService: LogFocusService,
    ) {
        val bodyParams = call.receiveParameters()
        val logFocusResult = resolveBuildLogs(
            ciProjectApiFactory = ciProjectApiFactory,
            jobId = bodyParams.jobId(),
        )
            .let { logFocusService.getFocusedLogs(it) }

        call.respondText {
            """
                |------------------------------------------------------
                |
                |LOG LINE COUNT: ${logFocusResult.lineCount}
                |LOG SIZE BYTES: ${logFocusResult.logs.length}
                |LOGS ARE EMPTY: ${logFocusResult.isEmpty} (true when not enough logs to run CI Triage)
                |LOGS ARE SMALL: ${logFocusResult.isSmall} (true when not enough logs to need log summarization)
                |
                |------------------------------------------------------
                |${logFocusResult.logs}
            """.trimMargin()
        }
    }

    fun buildJobGetAnnotationsMenuItem(
        path: String,
        jobs: Collection<BuildJob>,
        description: String? = null,
    ) = MenuItem(
        href = "$path/buildJobGetAnnotations",
        label = "CI Job: get annotations (raw)",
        description = description ?: "Gets job annotations from live API request to CI provider",
        inputs = listOf(
            MenuItemInput.SelectInput(
                label = "Build job",
                name = "jobId",
                items = jobs.map {
                    MenuItemInput.SelectInput.Item(
                        label = "${it.displayName} -- ${it.externalId} -- ${it.status} -- ${it.result}",
                        value = "${it.id}",
                    )
                },
            ),
        ),
    )

    suspend fun RoutingContext.buildJobGetAnnotations(
        ciProjectApiFactory: CIProjectApiFactory,
    ) {
        val bodyParams = call.receiveParameters()
        val bundle = bodyParams.jobBundle()

        val job = bundle.job
        val build = bundle.build
        val ciInstallation = bundle.ciInstallation
        val projectContext = build.projectContext.decode<CIProjectContext>()

        val text = job.annotationsId?.let { annotationsId ->
            ciProjectApiFactory.getProjectApi(ciInstallation = ciInstallation, projectContext = projectContext).use {
                it.annotations(annotationsId = annotationsId)
                    ?.toList()
                    ?.joinToString("\n")
            }
        }

        call.respondText {
            text ?: "null"
        }
    }
}
