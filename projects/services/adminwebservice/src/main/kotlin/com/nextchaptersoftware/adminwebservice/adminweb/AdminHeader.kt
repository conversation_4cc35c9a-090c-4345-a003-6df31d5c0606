package com.nextchaptersoftware.adminwebservice.adminweb

import kotlinx.html.HEAD
import kotlinx.html.link
import kotlinx.html.meta
import kotlinx.html.script
import kotlinx.html.title

fun HEAD.renderHeader(page: AdminPage) {
    title { +"$WEB_NAME | ${page.parentTab.label}" }
    meta(name = "Description", content = "Internal Unblocked Admin Tooling.")
    link(rel = "icon", href = "$WEB_ROOT/images/favicon.ico")

    // CSS
    link(rel = "stylesheet", href = "https://cdn.jsdelivr.net/npm/bootswatch@5.3.0/dist/darkly/bootstrap.min.css")
    link(rel = "stylesheet", href = "https://cdn.jsdelivr.net/gh/xcatliu/simplemde-theme-dark@master/dist/simplemde-theme-dark.min.css")
    link(rel = "stylesheet", href = "https://cdn.datatables.net/1.13.8/css/dataTables.bootstrap5.min.css")
    link(rel = "stylesheet", href = "$WEB_ROOT/css/admin.css")

    // JS
    script(src = "https://code.jquery.com/jquery-3.6.1.js") {}
    script(src = "https://kit.fontawesome.com/e0809dd3f8.js") {}
    script(src = "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js") {}
    script(src = "https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js") {}
    script(src = "https://cdn.jsdelivr.net/npm/marked/marked.min.js") {}
    script(src = "https://cdn.datatables.net/1.13.8/js/jquery.dataTables.min.js") {}
    script(src = "https://cdn.datatables.net/1.13.8/js/dataTables.bootstrap5.min.js") {}
    script(src = "https://cdn.jsdelivr.net/npm/chart.js/dist/chart.umd.min.js") {}
    script(src = "https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom/dist/chartjs-plugin-zoom.min.js") { }
    script(src = "https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js") { }
    script(src = "https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.min.js") {}
    script(src = "https://cdn.jsdelivr.net/npm/chartjs-chart-funnel/build/index.umd.min.js") { }
    script(src = "https://cdn.jsdelivr.net/npm/chartjs-chart-matrix@2") { }
    script(src = "$WEB_ROOT/scripts/data-table-config.js") {}
    script(src = "$WEB_ROOT/scripts/plot-utils.js") {}
    script(src = "$WEB_ROOT/scripts/htmx.min.js") {}
    script(src = "https://accounts.google.com/gsi/client") {
        async = true
        defer = true
    }
}
