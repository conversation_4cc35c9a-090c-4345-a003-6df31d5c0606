package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminNavigationTab
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.db.models.RegressionTestId
import com.nextchaptersoftware.search.semantic.services.eval.InflatedRegressionEval
import com.nextchaptersoftware.search.semantic.services.eval.PassState
import com.nextchaptersoftware.search.semantic.services.eval.RegressionTestService
import com.nextchaptersoftware.search.semantic.services.eval.durationDriftPass
import com.nextchaptersoftware.search.semantic.services.eval.evalScoreDriftPass
import com.nextchaptersoftware.search.semantic.services.eval.similarityScorePass
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.TBODY
import kotlinx.html.ThScope
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object TemplateRegressionTestDetailsPage {

    suspend fun RoutingContext.renderTemplateRegressionTestDetailsPage(
        regressionTestService: RegressionTestService,
        page: AdminPage,
    ) {
        val breadcrumb = call.makeBreadcrumb()

        val adminIdentity = call.getAdminIdentity()

        val templateId = call.parameters.requiredId("templateId", ::MLInferenceTemplateId)

        val testId = call.parameters.requiredId("testId", ::RegressionTestId)

        val test = regressionTestService.inflateFromTest(testId)

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                h1 { +"Regression Test Details" }
                renderRegressionEvals(
                    templateId = templateId,
                    testId = testId,
                    evals = test.regressionEvals,
                )
            }
        }
    }

    private fun FlowContent.renderRegressionEvals(
        templateId: MLInferenceTemplateId,
        testId: RegressionTestId,
        evals: List<InflatedRegressionEval>,
    ) {
        table(classes = "table table-striped table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Pass/Fail" }
                    th(scope = ThScope.col) { +"Date" }
                    th(scope = ThScope.col) { +"Status" }
                    th(scope = ThScope.col) { +"Score Improvement" }
                    th(scope = ThScope.col) { +"Duration Improvement" }
                    th(scope = ThScope.col) { +"Similarity" }
                }
            }
            tbody(classes = "table-dark") {
                evals.forEach {
                    renderRegressionEval(
                        templateId = templateId,
                        testId = testId,
                        eval = it,
                    )
                }
            }
        }
    }

    @Suppress("CyclomaticComplexMethod")
    private fun TBODY.renderRegressionEval(
        templateId: MLInferenceTemplateId,
        testId: RegressionTestId,
        eval: InflatedRegressionEval,
    ) {
        tr {
            attributes["onclick"] =
                "window.location='${AdminNavigationTab.MLTemplates.route}/$templateId/regression-tests/$testId/evals/${eval.evalId}';"
            style = "cursor: pointer;"

            val passBadge = when (eval.pass) {
                PassState.Pending -> null
                PassState.Passed -> true
                is PassState.Failed -> false
            }

            td {
                asBadge(
                    bool = passBadge,
                    trueText = "Pass",
                    falseText = "Fail",
                    unknownText = "Pending",
                    unknownStyle = BootstrapStyle.Warning,
                )
                if (eval.pass is PassState.Failed) {
                    div(classes = "small text-muted d-none d-xl-block") {
                        +(eval.pass as PassState.Failed).reason
                    }
                }
            }
            td { timeAgo(eval.createdAt) }
            td {
                asBadge(eval)
            }
            val evalScoreDrift = eval.evalScoreDrift
            val evalScoreCellClass = if (evalScoreDrift == null) {
                ""
            } else {
                when (evalScoreDriftPass(evalScoreDrift)) {
                    true -> if (evalScoreDrift > 0) {
                        "table-success"
                    } else {
                        "table-warning"
                    }

                    false -> "table-danger"
                }
            }
            val durationDrift = eval.durationDrift
            val durationCellClass = if (durationDrift == null) {
                ""
            } else {
                when (durationDriftPass(durationDrift.toDouble())) {
                    true -> if (durationDrift > 0) {
                        "table-success"
                    } else {
                        "table-warning"
                    }

                    false -> "table-danger"
                }
            }
            val similarityScore = eval.similarityScore
            val similarityCellClass = if (similarityScore == null) {
                ""
            } else {
                when (similarityScorePass(similarityScore)) {
                    true -> "table-success"
                    false -> "table-danger"
                }
            }

            td(classes = evalScoreCellClass) {
                +evalScoreDrift.toString()
            }
            td(classes = durationCellClass) {
                +durationDrift.toString()
            }
            td(classes = similarityCellClass) {
                +similarityScore.toString()
            }
        }
    }
}
