package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminNavigationTab
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click
import com.nextchaptersoftware.adminwebservice.adminweb.component.DropDownOption
import com.nextchaptersoftware.adminwebservice.adminweb.component.dropDownList
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.DocumentRetrievalPage.renderDocumentFieldSet
import com.nextchaptersoftware.adminwebservice.adminweb.page.DocumentRetrievalPage.renderInsightFieldSet
import com.nextchaptersoftware.adminwebservice.adminweb.page.DocumentRetrievalPage.renderSourceFieldSet
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.api.utils.CallExtensions.respondNotFound
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.MLDocumentRelevancyEvaluationType
import com.nextchaptersoftware.db.models.MLFunctionOutputFormat
import com.nextchaptersoftware.db.models.MLInferenceEngine
import com.nextchaptersoftware.db.models.MLInferenceTemplate
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.MLReferenceResolverType
import com.nextchaptersoftware.db.models.MLRerankModel
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.utils.asUUIDOrNull
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.RoutingContext
import kotlinx.html.ButtonType
import kotlinx.html.FlowContent
import kotlinx.html.FormMethod
import kotlinx.html.InputType
import kotlinx.html.TBODY
import kotlinx.html.TextAreaWrap
import kotlinx.html.button
import kotlinx.html.div
import kotlinx.html.fieldSet
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.h5
import kotlinx.html.input
import kotlinx.html.numberInput
import kotlinx.html.small
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.textArea
import kotlinx.html.tr

@Suppress("LargeClass")
object MLTemplatePage {
    suspend fun RoutingContext.renderMLTemplatePage(
        page: AdminPage,
        templateService: MLInferenceTemplateService,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val template = AdminPathUtils.getRequiredTemplate(call.parameters)
        val path = call.request.path()
        val adminIdentity = call.getAdminIdentity()
        val orgsUsingTemplate = templateService.orgsUsingTemplate(template.id)

        val templatesOfSameKind = templateService.findAllByKind(template.templateKind).filterNot { it.id == template.id }
        val currentFallback = template.fallbackTemplateId?.let { templateService.get(it) }

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                h1 { +template.name }
                renderOrgsUsingTemplate(orgsUsingTemplate)
                renderTemplateForm(template, currentFallback, templatesOfSameKind, path)
            }
        }
    }

    private fun FlowContent.renderTemplateForm(
        template: MLInferenceTemplate,
        fallbackTemplate: MLInferenceTemplate?,
        templatesOfSameKind: List<MLInferenceTemplate>,
        path: String,
    ) {
        form(
            action = "$path/updateTemplate",
            method = FormMethod.post,
        ) {
            attributes["onsubmit"] = "return confirm('Are you sure you want to update the template?');"
            fieldSet(classes = "d-flex flex-column form-group") {
                disabled = template.isGlobalDefault

                h5(classes = "mt-5") { +"Meta" }
                insert(PropertyListTemplate()) {
                    propertyList { renderTemplateMeta(template, fallbackTemplate, templatesOfSameKind) }
                }

                h5(classes = "mt-5") { +"Retrieval" }
                insert(PropertyListTemplate()) {
                    propertyList { renderTemplateRetrieval(template) }
                }

                h5(classes = "mt-5") { +"Ranker" }
                insert(PropertyListTemplate()) {
                    propertyList { renderTemplateRanker(template) }
                }

                if (template.templateKind == MLInferenceTemplateKind.RAGFunctions) {
                    h5(classes = "mt-5") { +"Functions" }
                    insert(PropertyListTemplate()) {
                        propertyList { renderTemplateFunctions(template) }
                    }
                }

                if (template.templateKind == MLInferenceTemplateKind.SampleQuestionGenerator) {
                    h5(classes = "mt-5") { +"Sample Questions Generator Settings" }
                    insert(PropertyListTemplate()) {
                        propertyList { renderTemplateSampleQuestionsGeneratorSettings(template) }
                    }
                }

                h5(classes = "mt-5") { +"Prompt Compiler Settings" }
                insert(PropertyListTemplate()) {
                    propertyList { renderTemplatePromptCompilerSettings(template) }
                }

                h5(classes = "mt-iter5") { +"Inference" }
                insert(PropertyListTemplate()) {
                    propertyList { renderTemplateInference(template) }
                }

                h5(classes = "mt-5") { +"Output Chain" }
                insert(PropertyListTemplate()) {
                    propertyList { renderTemplateOutputChain(template) }
                }

                button(classes = "btn btn-primary", type = ButtonType.submit) {
                    +"Save Template"
                }
            }
        }
    }

    @Suppress("LongMethod")
    private fun TBODY.renderTemplateMeta(
        template: MLInferenceTemplate,
        fallbackTemplate: MLInferenceTemplate?,
        templatesOfSameKind: List<MLInferenceTemplate>,
    ) {
        property("Name", legend = {}) {
            div {
                textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                    name = "templateName"
                    +template.name
                }
            }
        }
        property("Template Kind", legend = {}) {
            dropDownList(
                name = "templateKind",
                options = MLInferenceTemplateKind.entries.sortedBy { it.name }.map { DropDownOption(it.name) },
                selectedValue = DropDownOption(template.templateKind.name),
            )
        }
        property("Inference Engine", legend = {}) {
            dropDownList(
                name = "inferenceEngine",
                options = MLInferenceEngine.entries.sortedBy { it.name }.map { DropDownOption(it.name) },
                selectedValue = DropDownOption(template.inferenceEngine.name),
            )
        }
        property("Rerank Model", legend = {}) {
            dropDownList(
                name = "rerankModel",
                options = MLRerankModel.entries.sortedBy { it.name }.map { DropDownOption(it.name) },
                selectedValue = DropDownOption(template.rerankModel.name),
            )
        }
        property(
            "Max Prompt Length",
            legend = {
                small(classes = "text-muted") {
                    div { +"Maximum length of the prompt in Bytes" }
                }
            },
        ) {
            numberInput(classes = "form-control") {
                name = "maxPromptLength"
                value = template.maxPromptLength.toString()
            }
        }
        property("Fallback Template", legend = {}) {
            dropDownList(
                name = "fallbackTemplateId",
                default = DropDownOption(
                    "-- None --",
                ),
                options = templatesOfSameKind.sortedBy { it.name }.map {
                    DropDownOption(
                        value = it.id.toString(),
                        description = it.name,
                    )
                },
                selectedValue = fallbackTemplate?.let {
                    DropDownOption(
                        value = it.id.toString(),
                        description = it.name,
                    )
                } ?: DropDownOption(
                    "-- None --",
                ),
            )
        }
    }

    @Suppress("LongMethod")
    private fun TBODY.renderTemplateRetrieval(template: MLInferenceTemplate) {
        property("Document types", legend = {}) {
            renderDocumentFieldSet { template.documentTypes.contains(it) }
        }
        property("Insight types", legend = {}) {
            renderInsightFieldSet { template.insightTypes.contains(it) }
        }
        property("Source types", legend = {}) {
            renderSourceFieldSet { template.sourceTypes.contains(it) }
        }
        property("Max Documents", legend = {}) {
            numberInput(classes = "form-control") {
                name = "maxDocuments"
                value = template.maxDocuments.toString()
            }
        }
        property("Max Local Context Documents", legend = {}) {
            numberInput(classes = "form-control") {
                name = "maxLocalDocuments"
                value = template.maxLocalDocuments.toString()
            }
        }
        property(
            "Local Context Weight",
            legend = {
                small(classes = "text-muted") {
                    div { +"0.0 = no local context docs" }
                    div { +"1.0 = only local context docs" }
                }
            },
        ) {
            numberInput(classes = "form-control") {
                step = "any"
                name = "localContextRetrievalWeight"
                value = template.localContextRetrievalWeight.toString()
            }
        }
        property(
            "Sparse Vector Weight",
            legend = {
                small(classes = "text-muted") {
                    div { +"0.0 = only dense vectors" }
                    div { +"1.0 = only sparse vectors" }
                }
            },
        ) {
            numberInput(classes = "form-control") {
                step = "any"
                name = "sparseVectorWeight"
                value = template.sparseVectorWeight.toString()
            }
        }
        property(
            "Use Highlighted Code Compressor",
            legend = {
                small(classes = "text-muted") {
                    div { +"Compresses Highlighted Code in the document query" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "useHighlightedCodeCompressor"
                checked = template.useHighlightedCodeCompressor
                value = true.toString()
            }
        }
        property(
            "Use Agentic Retrieval",
            legend = {
                small(classes = "text-muted") {
                    div { +"Use agentic retrieval to improve document quality before generation" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "useAgenticRetrieval"
                checked = template.useAgenticRetrieval
                value = true.toString()
            }
        }
        property(
            "Enable Sparse Vector Code Search",
            legend = {
                small(classes = "text-muted") {
                    div { +"Perform additional SourceCode queries with sparse vector weight 1.0f" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "enableSparseVectorCodeSearch"
                checked = template.enableSparseVectorCodeSearch
                value = true.toString()
            }
        }
        if (template.templateKind == MLInferenceTemplateKind.DocumentRelevanceEvaluator ||
            template.templateKind == MLInferenceTemplateKind.CITriageDocumentEvaluator
        ) {
            property("Document Relevancy Evaluation Max Depth", legend = {}) {
                numberInput(classes = "form-control") {
                    name = "documentRelevancyEvaluationMaxDepth"
                    value = template.documentRelevancyEvaluationMaxDepth.toString()
                }
            }
            property(
                "Document Relevancy Evaluation Type",
                legend = {
                    small(classes = "text-muted") {
                        div { +"Evaluate against generated query or user query" }
                    }
                },
            ) {
                dropDownList(
                    name = "documentRelevancyEvaluationType",
                    options = MLDocumentRelevancyEvaluationType.entries.sortedBy { it.name }.map { DropDownOption(it.name) },
                    selectedValue = DropDownOption(template.documentRelevancyEvaluationType.name),
                )
            }
        }
    }

    private fun TBODY.renderTemplateFunctions(template: MLInferenceTemplate) {
        property(
            "Enable MLFunctions",
            legend = {
                small(classes = "text-muted") {
                    div { +"Use LLM assisted procedural functions to retrieve additional documents" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "useMLFunctions"
                checked = template.useMLFunctions
                value = true.toString()
            }
        }
        property(
            "ML Function Output Format",
            legend = {
                small(classes = "text-muted") {
                    div { +"Format of the output of the ML functions" }
                }
            },
        ) {
            dropDownList(
                name = "mlFunctionOutputFormat",
                options = MLFunctionOutputFormat.entries.sortedBy { it.name }.map { DropDownOption(it.name) },
                selectedValue = DropDownOption(template.mlFunctionOutputFormat.name),
            )
        }
        property(
            "Enable experimental functions",
            legend = {
                small(classes = "text-muted") {
                    div { +"Enable MLFunctions annotated with MLFunctionExperimental" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "enableExperimentalFunctions"
                checked = template.enableExperimentalFunctions
                value = true.toString()
            }
        }
        property(
            "Parallelize MLFunctions by class",
            legend = {
                small(classes = "text-muted") {
                    div { +"Run each MLFunctions class separately, in parallel" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "parallelizeMLFunctionsByClass"
                checked = template.parallelizeMLFunctionsByClass
                value = true.toString()
            }
        }
    }

    private fun TBODY.renderTemplateSampleQuestionsGeneratorSettings(template: MLInferenceTemplate) {
        property(
            "Number of Generated Sample Questions",
            legend = {
                small(classes = "text-muted") {
                    div { +"Number of sample questions to generate" }
                }
            },
        ) {
            numberInput(classes = "form-control") {
                name = "numGeneratedSampleQuestions"
                value = template.numGeneratedSampleQuestions.toString()
            }
        }

        property(
            "Use PRs as seed data if available (first priority)",
            legend = {
                small(classes = "text-muted") {
                    div { +"Use PRs as seed data if available (first priority)" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "usePRsAsSeedDataForSampleQuestionGeneration"
                checked = template.usePRsAsSeedDataForSampleQuestionGeneration
                value = true.toString()
            }
        }

        property(
            "Use Commits as seed data if available (second priority)",
            legend = {
                small(classes = "text-muted") {
                    div { +"Use Commits as seed data if available (second priority)" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "useCommitsAsSeedDataForSampleQuestionGeneration"
                checked = template.useCommitsAsSeedDataForSampleQuestionGeneration
                value = true.toString()
            }
        }

        property(
            "Use diffs as seed data if available (either for PRs or Commits, whichever is available)",
            legend = {
                small(classes = "text-muted") {
                    div { +"Use diffs as seed data if available (either PRs or Commits, whichever is available)" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "useDiffsAsSeedDataForSampleQuestionGeneration"
                checked = template.useDiffsAsSeedDataForSampleQuestionGeneration
                value = true.toString()
            }
        }

        property(
            "Use seed data only (skip document based generation)",
            legend = {
                small(classes = "text-muted") {
                    div { +"Use seed data only (skip document based generation)" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "useSeedDataOnlyForSampleQuestionGeneration"
                checked = template.useSeedDataOnlyForSampleQuestionGeneration
                value = true.toString()
            }
        }
    }

    @Suppress("LongMethod")
    private fun TBODY.renderTemplateRanker(template: MLInferenceTemplate) {
        property(
            "Reciprocal Rank Fusion",
            legend = {
                small(classes = "text-muted") {
                    div { +"Use RRF to combine results from multiple sources" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "useRRF"
                checked = template.useRRF
                value = true.toString()
            }
        }
        property(
            "Cross-Encoder Re-Rank (Cohere)",
            legend = {
                small(classes = "text-muted") {
                    div { +"Re-rank results by semantic relevancy to the query" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "useCERR"
                checked = template.useCERR
                value = true.toString()
            }
        }
        property(
            "Minimum score for document to be included",
            legend = {
                small(classes = "text-muted") {
                    div { +"between 0 and 1" }
                }
            },
        ) {
            numberInput(classes = "form-control") {
                step = "any"
                name = "minRankScore"
                value = template.minRankScore.toString()
            }
        }
        property(
            "Slack Auto Response Min Document Score",
            legend = {
                small(classes = "text-muted") {
                    div { +"between 0 and 1" }
                }
            },
        ) {
            numberInput(classes = "form-control") {
                step = "any"
                name = "slackAutoResponseMinDocScore"
                value = template.slackAutoResponseMinDocScore.toString()
            }
        }
        property(
            "Max Rerank Docs",
            legend = {
                small(classes = "text-muted") {
                    div { +"Maximum number of docs sent to reranker (exceeding will result in parallel queries)" }
                }
            },
        ) {
            numberInput(classes = "form-control") {
                name = "maxRerankDocs"
                value = template.maxRerankDocs.toString()
            }
        }
    }

    private fun TBODY.renderTemplatePromptCompilerSettings(template: MLInferenceTemplate) {
        property(
            "Recompose Document Partitions",
            legend = {
                small(classes = "text-muted") {
                    div { +"Recompose document partitions that originate from the same source document" }
                }
            },
        ) {
            input(classes = "form-check-input", type = InputType.checkBox) {
                name = "recomposeDocumentPartitions"
                checked = template.recomposeDocumentPartitions
                value = true.toString()
            }
        }
    }

    @Suppress("LongMethod", "CyclomaticComplexMethod")
    private fun TBODY.renderTemplateInference(template: MLInferenceTemplate) {
        property(
            "Model Temperature",
            legend = {
                small(classes = "text-muted") {
                    div { +"between 0 and 2" }
                }
            },
        ) {
            numberInput(classes = "form-control") {
                step = "any"
                name = "temperature"
                value = template.temperature.toString()
            }
        }

        property(
            "Prompt Template",
            legend = {
                +"{activeIntegrations}"
                div {
                    small(classes = "text-muted") {
                        +"Enabled providers. See Active Integrations Section Template."
                    }
                }
                +"{prefix}"
                div {
                    small(classes = "text-muted") { +"Filled by the contents of the prefix template" }
                }

                when (template.templateKind) {
                    MLInferenceTemplateKind.ExpertSummary,
                    MLInferenceTemplateKind.TopicSummary,
                    -> {
                        +"{topics}"
                        div {
                            small(classes = "text-muted") { +"Filled by topics (components). See Topics template" }
                        }
                    }

                    else -> {
                        Unit
                    }
                }

                +"{questioner}"
                div {
                    small(classes = "text-muted") { +"Filled by this questioner info" }
                }
                +"{examples}"
                div {
                    small(classes = "text-muted") { +"Filled by Golden Record Q&A (see example template)" }
                }
                +"{summaries}"
                div {
                    small(classes = "text-muted") {
                        +"When summary documents are retrieved, this field is filled with the contents of the Summaries Template."
                    }
                }
                +"{documents} (soon to be deprecated)"
                div {
                    small(classes = "text-muted") {
                        +"Filled by documents ordered by relevancy. See Document template"
                    }
                }
                +"{documentation}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by documentation documents (like Notion) ordered by relevancy. Omitted if empty. See Documentation Section template"
                    }
                }
                +"{highlightedCode}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by code highlighted by the user"
                    }
                }
                +"{zeroDocuments}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by a series of instructions when no documents are found. See Zero Docs template"
                    }
                }
                +"{code}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by code documents ordered by relevancy. Omitted if empty. See Code Section template"
                    }
                }
                +"{pullRequests}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by pull request summaries ordered by relevancy. Omitted if empty. See Pull Request Section template"
                    }
                }
                +"{slack}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by slack thread content ordered by relevancy. Omitted if empty. See Slack Section template"
                    }
                }
                +"{issues}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by issue content ordered by relevancy. Omitted if empty. See Issue Section template"
                    }
                }
                +"{repos}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by supplied repos. See the Repo Template"
                    }
                }
                if (template.templateKind == MLInferenceTemplateKind.RAGFunctions) {
                    +"{functions}"
                    div {
                        small(classes = "text-muted") {
                            +"Filled by the supplied functions. See the Function template"
                        }
                    }
                }
                +"{question}"
                div {
                    small(classes = "text-muted") { +"Filled by the contents of the query template" }
                }
                +"{answer}"
                div {
                    small(classes = "text-muted") { +"Filled by the contents of the answer template" }
                }
                +"{conversation}"
                div {
                    small(classes = "text-muted") {
                        +"""
                                        Filled by the conversation. See conversationTemplate and conversationMessageTemplate
                        """.trimIndent()
                    }
                }
                +"{currentDate}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by the current date. Essentially, Instant.nowWithMicrosecondPrecision().toString()"
                    }
                }
                +"{orgName}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by the org name. Optional"
                    }
                }
                +"{userLocalActiveContext}"
                div {
                    small(classes = "text-muted") {
                        +"User's local active context. Optional. See User Local Active Context Template."
                    }
                }
                +"{userLocalHighlightedContext}"
                div {
                    small(classes = "text-muted") {
                        +"User's local highlighted context. Optional. See User Local Highlighted Context Template."
                    }
                }
                +"{suffix}"
                div {
                    small(classes = "text-muted") { +"Filled by contents of the suffix template" }
                }
                +"{answerConcisenessPreference}"
                div {
                    small(classes = "text-muted") { +"Filled by the user's answer conciseness preference" }
                }
                +"{answerTonePreference}"
                div {
                    small(classes = "text-muted") { +"Filled by the user's answer tone preference" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "12", cols = "1", wrap = TextAreaWrap.soft) {
                name = "promptTemplate"
                +template.promptTemplate
            }
        }
        property(
            "Prefix Template",
            legend = {
                +"{orgName}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by the org name. Optional"
                    }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "prefixPromptTemplate"
                template.prefixPromptTemplate?.let { +it }
            }
        }
        property("Suffix Template", legend = {}) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "suffixPromptTemplate"
                template.suffixPromptTemplate?.let { +it }
            }
        }
        property(
            "Example Template",
            legend = {
                +"{question}"
                div {
                    small(classes = "text-muted") { +"Filled by example question" }
                }
                +"{answer}"
                div {
                    small(classes = "text-muted") { +"Filled by example answer" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "examplePromptTemplate"
                template.examplePromptTemplate?.let { +it }
            }
        }
        property("Max Examples", legend = {}) {
            numberInput(classes = "form-control") {
                name = "maxExamples"
                value = template.maxExamples.toString()
            }
        }
        property(
            "Summaries Template",
            legend = {
                +"{summaryDocuments}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of summary documents." }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "4", cols = "1", wrap = TextAreaWrap.soft) {
                name = "summariesTemplate"
                template.summariesTemplate?.let { +it }
            }
        }
        property(
            "Documentation Section Template",
            legend = {
                +"{documents}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of documents. See document template" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "4", cols = "1", wrap = TextAreaWrap.soft) {
                name = "documentSectionPromptTemplate"
                template.documentSectionPromptTemplate?.let { +it }
            }
        }
        property(
            "Active Integrations Section Template",
            legend = {
                +"{integrations}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of active integrations and their descriptions" }
                }
                +"{integrationLinks}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of active integrations and their sample links" }
                }
                +"{integrationDomains}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of active integrations domains" }
                }
                +"{teamName}"
                div {
                    small(classes = "text-muted") { +"Org Name" }
                }
                +"{teamSettingsLink}"
                div {
                    small(classes = "text-muted") { +"Org Settings Link" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "4", cols = "1", wrap = TextAreaWrap.soft) {
                name = "activeIntegrationsPromptTemplate"
                template.activeIntegrationsPromptTemplate?.let { +it }
            }
        }
        property(
            "Code Section Template",
            legend = {
                +"{code}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of sourcecode snippets. See document template" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "4", cols = "1", wrap = TextAreaWrap.soft) {
                name = "codeSectionPromptTemplate"
                template.codeSectionPromptTemplate?.let { +it }
            }
        }
        property(
            "Pull Request Section Template",
            legend = {
                +"{pullRequests}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of pull request summaries. See document template" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "4", cols = "1", wrap = TextAreaWrap.soft) {
                name = "pullRequestSectionPromptTemplate"
                template.pullRequestSectionPromptTemplate?.let { +it }
            }
        }
        property(
            "Slack Section Template",
            legend = {
                +"{slack}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of slack thread summaries. See document template" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "4", cols = "1", wrap = TextAreaWrap.soft) {
                name = "slackSectionPromptTemplate"
                template.slackSectionPromptTemplate?.let { +it }
            }
        }
        property(
            "Issue Section Template",
            legend = {
                +"{issues}"
                div {
                    small(classes = "text-muted") { +"Filled with a list of issues (linear or jira for example). See document template" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "4", cols = "1", wrap = TextAreaWrap.soft) {
                name = "issueSectionPromptTemplate"
                template.issueSectionPromptTemplate?.let { +it }
            }
        }
        property(
            "Document Template",
            legend = {
                +"{sourceType}"
                div {
                    small(classes = "text-muted") { +"Filled by source type. Ex. Jira, Slack, SourceCode, etc" }
                }
                +"{sourceId}"
                div {
                    small(classes = "text-muted") { +"Reference ID that can be used to refer back to documents" }
                }
                +"{source}"
                div {
                    small(classes = "text-muted") { +"Source metadata, generated at runtime depending on sourceType" }
                }
                +"{content}"
                div {
                    small(classes = "text-muted") { +"Document content" }
                }
                +"{date}"
                div {
                    small(classes = "text-muted") { +"Date that this document was created (optional)" }
                }
                +"{score}"
                div {
                    small(classes = "text-muted") { +"Relevancy score this document received during retrieval (optional)" }
                }
                +"{author}"
                div {
                    small(classes = "text-muted") { +"Author's display name (optional)" }
                }
                +"{repo}"
                div {
                    small(classes = "text-muted") { +"Repo full name (optional)" }
                }
                +"{metadata}"
                div {
                    small(classes = "text-muted") { +"Set of key/value pairs joined with newlines" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "documentPromptTemplate"
                template.documentPromptTemplate?.let { +it }
            }
        }
        property(
            "Query Template",
            legend = {
                +"{metadata}"
                div {
                    small(classes = "text-muted") { +"Filled by metadata specific to the query, like date constraints etc" }
                }
                +"{question}"
                div {
                    small(classes = "text-muted") { +"Filled by the user question" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "queryPromptTemplate"
                template.queryPromptTemplate?.let { +it }
            }
        }
        property(
            "Answer Template",
            legend = {
                +"{answer}"
                div {
                    small(classes = "text-muted") { +"Filled by the answer to the question." }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "answerPromptTemplate"
                template.answerPromptTemplate?.let { +it }
            }
        }
        property(
            "Topics Template",
            legend = {
                +"{topics}"
                div {
                    small(classes = "text-muted") { +"Filled with the list of topics for this org" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "topicsPromptTemplate"
                template.topicsPromptTemplate?.let { +it }
            }
        }
        property(
            "Questioner Template",
            legend = {
                +"{questioner}"
                div {
                    small(classes = "text-muted") { +"Filled in with the questioner. See TeamMemberTemplate" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "questionerPromptTemplate"
                template.questionerPromptTemplate?.let { +it }
            }
        }
        property(
            "Member Template",
            legend = {
                +"{displayName}"
                div {
                    small(classes = "text-muted") { +"Filled with the display name for this member" }
                }
                +"{teamMemberId}"
                div {
                    small(classes = "text-muted") { +"Filled with the UUID for this member" }
                }
                +"{topicExpertise}"
                div {
                    small(classes = "text-muted") { +"Filled with the list of topics this member is an expert in" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "teamMemberPromptTemplate"
                template.memberPromptTemplate?.let { +it }
            }
        }
        property(
            "Conversation Template",
            legend = {
                +"{messages}"
                div {
                    small(classes = "text-muted") { +"Filled with previous messages. See Conversation Message Template" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "conversationTemplate"
                template.conversationTemplate?.let { +it }
            }
        }
        property(
            "Conversation Message Template",
            legend = {
                +"{teamMemberRole}"
                div {
                    small(classes = "text-muted") {
                        +"Filled with the role (System, User, Assistant). Changes depending on the model"
                    }
                }
                +"{teamMemberName}"
                div {
                    small(classes = "text-muted") {
                        +"Filled with the displayName ?: username of the user"
                    }
                }
                +"{teamMemberRoleInline}"
                div {
                    small(classes = "text-muted") {
                        +"Filled with the role (System, User, Assistant) in a way that remains inline to the prompt. Changes depending on the model"
                    }
                }
                +"{contents}"
                div {
                    small(classes = "text-muted") { +"Filled with the contents of the message" }
                }
                +"{userLocalHighlightedContext}"
                div {
                    small(classes = "text-muted") {
                        +"User's local highlighted context. Optional. See User Local Highlighted Context Template."
                    }
                }
                +"{userLocalActiveContext}"
                div {
                    small(classes = "text-muted") {
                        +"User's local active context. Optional. See User Local Active Context Template."
                    }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "conversationMessageTemplate"
                template.conversationMessageTemplate?.let { +it }
            }
        }
        property(
            "Repo Template",
            legend = {
                +"{repoId}"
                div {
                    small(classes = "text-muted") { +"Filled with the repo id" }
                }
                +"{repoFullName}"
                div {
                    small(classes = "text-muted") {
                        +"Filled with the repo full name. Ex: NextChapterSoftware/unblocked"
                    }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "repoTemplate"
                template.repoTemplate?.let { +it }
            }
        }
        if (template.templateKind == MLInferenceTemplateKind.RAGFunctions) {
            property(
                "Function Template",
                legend = {
                    +"{kotlinFormat}"
                    div {
                        small(classes = "text-muted") { +"Filled with the kotlin style function definition" }
                    }
                    +"{jsonFormat}"
                    div {
                        small(classes = "text-muted") {
                            +"Filled with the json style function definition. See MLFunctionDefinition"
                        }
                    }
                },
            ) {
                textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                    name = "functionTemplate"
                    template.functionTemplate?.let { +it }
                }
            }
        }
        property(
            "Zero Docs Template",
            legend = {
                +"{orgName}"
                div {
                    small(classes = "text-muted") {
                        +"Filled by the org name. Optional"
                    }
                }
                div {
                    small(classes = "text-muted") {
                        +"Contains instructions for the model to follow when there were no documents found during retrieval"
                    }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "zeroDocsTemplate"
                template.zeroDocsTemplate?.let { +it }
            }
        }
        property(
            "User Local Active Context Template",
            legend = {
                +"{activeFiles}"
                div {
                    small(classes = "text-muted") { +"Filled with the list of active files" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "userLocalActiveContextTemplate"
                template.userLocalActiveContextTemplate?.let { +it }
            }
        }
        property(
            "User Local Highlighted Context Template",
            legend = {
                +"{highlightedCode}"
                div {
                    small(classes = "text-muted") { +"Contains any highlighted code on the IDE" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "userLocalHighlightedContextTemplate"
                template.userLocalHighlightedContextTemplate?.let { +it }
            }
        }
        property(
            "Json Output Format Instructions",
            legend = {
                div {
                    small(classes = "text-muted") { +"Instruct the model how to output result in json format" }
                }
            },
        ) {
            textArea(classes = "d-flex form-control flex-grow-1", rows = "8", cols = "1", wrap = TextAreaWrap.soft) {
                name = "jsonOutputFormatInstructions"
                template.jsonOutputFormatInstructions?.let { +it }
            }
        }
    }

    private fun TBODY.renderTemplateOutputChain(template: MLInferenceTemplate) {
        property("Reference Resolver", legend = {}) {
            dropDownList(
                name = "referenceResolver",
                options = MLReferenceResolverType.entries.filterNot { it == MLReferenceResolverType.InPrompt }.sortedBy { it.name }
                    .map { DropDownOption(it.name) },
                selectedValue = DropDownOption(template.referenceResolverType.name),
            )
        }
    }

    private fun FlowContent.renderOrgsUsingTemplate(
        orgs: List<Org>,
    ) {
        if (orgs.isEmpty()) {
            return
        }

        h5(classes = "mt-5") { +"Teams using this template" }
        table(classes = "table table-hover align-middle") {
            tbody(classes = "table-dark") {
                orgs.forEach { org ->
                    tr {
                        attributes["onclick"] = Click.onClickAction("$WEB_ROOT/orgs/${org.id}")
                        style = "cursor: pointer;"
                        td { profile(org) }
                    }
                }
            }
        }
    }

    @Suppress("CyclomaticComplexMethod", "LongMethod")
    suspend fun RoutingContext.updateMLTemplate(mlInferenceTemplateService: MLInferenceTemplateService) {
        val templateId = call.parameters.optionalId("templateId", ::MLInferenceTemplateId)
        if (templateId == null) {
            call.respondNotFound()
            return
        }

        val template = mlInferenceTemplateService.get(templateId)
        require(!template.isGlobalDefault) {
            "Cannot edit the global default template"
        }

        val params = call.receiveParameters()

        val answerPromptTemplate = params["answerPromptTemplate"]
        val activeIntegrationsPromptTemplate = params["activeIntegrationsPromptTemplate"]
        val codeSectionPromptTemplate = params["codeSectionPromptTemplate"]
        val conversationMessageTemplate = params["conversationMessageTemplate"]
        val conversationTemplate = params["conversationTemplate"]
        val documentRelevancyEvaluationMaxDepth = params["documentRelevancyEvaluationMaxDepth"]?.toIntOrNull()
        val documentRelevancyEvaluationType = params["documentRelevancyEvaluationType"]?.let {
            MLDocumentRelevancyEvaluationType.valueOf(it)
        }
        val documentPromptTemplate = params["documentPromptTemplate"]
        val documentSectionPromptTemplate = params["documentSectionPromptTemplate"]
        val documentTypes = params.getAll(DocumentRetrievalPage.RetrievalParameters.DocumentTypes.name)
            ?.mapNotNull { it.toIntOrNull()?.let(DocumentType.Companion::fromDbOrdinal) }
            .orEmpty()
        val enableExperimentalFunctions = params["enableExperimentalFunctions"]?.toBoolean() ?: false
        val enableSparseVectorCodeSearch = params["enableSparseVectorCodeSearch"]?.toBoolean() ?: false
        val examplePromptTemplate = params["examplePromptTemplate"]
        val fallbackTemplateId = params["fallbackTemplateId"]?.asUUIDOrNull()?.let(::MLInferenceTemplateId)
        val functionTemplate = params["functionTemplate"]
        val inferenceEngine = requireNotNull(params["inferenceEngine"]?.let { MLInferenceEngine.valueOf(it) }) {
            "inferenceModel must be a valid MLInferenceModel"
        }
        val insightTypes = params.getAll(DocumentRetrievalPage.RetrievalParameters.InsightTypes.name)
            ?.mapNotNull { it.toIntOrNull()?.let(InsightType.Companion::fromDbOrdinal) }
            .orEmpty()
        val issueSectionPromptTemplate = params["issueSectionPromptTemplate"]
        val jsonOutputFormatInstructions = params["jsonOutputFormatInstructions"]
        val localContextRetrievalWeight = requireNotNull(params["localContextRetrievalWeight"]?.toFloatOrNull()) {
            "localContextRetrievalWeight must be a float"
        }
        val maxDocuments = requireNotNull(params["maxDocuments"]?.toIntOrNull()) { "maxDocuments must be an integer" }
        val maxExamples = requireNotNull(params["maxExamples"]?.toIntOrNull()) { "maxExamples must be an integer" }
        val maxLocalDocuments = requireNotNull(params["maxLocalDocuments"]?.toIntOrNull()) { "maxLocalDocuments must be an integer" }
        val maxPromptLength = requireNotNull(params["maxPromptLength"]?.toIntOrNull()) { "maxPromptLength must be an integer" }
        val maxRerankDocs = requireNotNull(params["maxRerankDocs"]?.toIntOrNull()) { "maxRerankDocs must be an integer" }
        val minRankScore = requireNotNull(params["minRankScore"]?.toFloatOrNull()) { "minRankScore must be a float" }
        val mlFunctionOutputFormat = params["mlFunctionOutputFormat"]?.let { MLFunctionOutputFormat.valueOf(it) }
        val name = params["templateName"] ?: ""
        val numGeneratedSampleQuestions = params["numGeneratedSampleQuestions"]?.toIntOrNull()
        val parallelizeMLFunctionsByClass = params["parallelizeMLFunctionsByClass"]?.toBoolean() ?: false
        val prefixPromptTemplate = params["prefixPromptTemplate"]
        val promptTemplate = params["promptTemplate"] ?: ""
        val pullRequestSectionPromptTemplate = params["pullRequestSectionPromptTemplate"]
        val queryPromptTemplate = params["queryPromptTemplate"]
        val questionerPromptTemplate = params["questionerPromptTemplate"]
        val recomposeDocumentPartitions = params["recomposeDocumentPartitions"]?.toBoolean() ?: false
        val referenceResolver = requireNotNull(params["referenceResolver"]?.let { MLReferenceResolverType.valueOf(it) }) {
            "referenceResolver must be a valid MLReferenceResolverType"
        }
        val repoTemplate = params["repoTemplate"]
        val rerankModel = requireNotNull(params["rerankModel"]?.let { MLRerankModel.valueOf(it) }) { "rerankModel must be a valid MLRerankModel" }
        val slackAutoResponseMinDocScore = requireNotNull(
            params["slackAutoResponseMinDocScore"]
            ?.toFloatOrNull(),
        ) { "slackAutoResponseMinDocScore must be a float" }
        val slackSectionPromptTemplate = params["slackSectionPromptTemplate"]
        val sourceTypes = params.getAll(DocumentRetrievalPage.RetrievalParameters.SourceTypes.name)
            ?.mapNotNull { it.toIntOrNull()?.let(Provider.Companion::fromDbOrdinal) }
            .orEmpty()
        val sparseVectorWeight = requireNotNull(params["sparseVectorWeight"]?.toFloatOrNull()) { "sparseVectorWeight must be a float" }
        val suffixPromptTemplate = params["suffixPromptTemplate"]
        val summariesTemplate = params["summariesTemplate"]
        val teamMemberPromptTemplate = params["teamMemberPromptTemplate"]
        val templateKind = requireNotNull(params["templateKind"]?.let { MLInferenceTemplateKind.valueOf(it) }) {
            "templateKind must be a valid MLInferenceTemplateKind"
        }
        val temperature = requireNotNull(params["temperature"]?.toFloatOrNull()) { "temperature must be a float" }
        val topicsPromptTemplate = params["topicsPromptTemplate"]
        val useAgenticRetrieval = params["useAgenticRetrieval"]?.toBoolean() ?: false
        val useCERR = params["useCERR"]?.toBoolean() ?: false
        val useCommitsAsSeedDataForSampleQuestionGeneration = params["useCommitsAsSeedDataForSampleQuestionGeneration"]?.toBoolean() ?: false
        val useDiffsAsSeedDataForSampleQuestionGeneration = params["useDiffsAsSeedDataForSampleQuestionGeneration"]?.toBoolean() ?: false
        val useHighlightedCodeCompressor = params["useHighlightedCodeCompressor"]?.toBoolean() ?: false
        val useMLFunctions = params["useMLFunctions"]?.toBoolean() ?: false
        val usePRsAsSeedDataForSampleQuestionGeneration = params["usePRsAsSeedDataForSampleQuestionGeneration"]?.toBoolean() ?: false
        val useRRF = params["useRRF"]?.toBoolean() ?: false
        val useSeedDataOnlyForSampleQuestionGeneration = params["useSeedDataOnlyForSampleQuestionGeneration"]?.toBoolean() ?: false
        val userLocalActiveContextTemplate = params["userLocalActiveContextTemplate"]
        val userLocalHighlightedContextTemplate = params["userLocalHighlightedContextTemplate"]
        val zeroDocsTemplate = params["zeroDocsTemplate"]

        mlInferenceTemplateService.update(
            answerPromptTemplate = answerPromptTemplate,
            activeIntegrationsPromptTemplate = activeIntegrationsPromptTemplate,
            codeSectionPromptTemplate = codeSectionPromptTemplate,
            conversationMessageTemplate = conversationMessageTemplate,
            conversationTemplate = conversationTemplate,
            documentRelevancyEvaluationMaxDepth = documentRelevancyEvaluationMaxDepth,
            documentRelevancyEvaluationType = documentRelevancyEvaluationType,
            documentPromptTemplate = documentPromptTemplate,
            documentSectionPromptTemplate = documentSectionPromptTemplate,
            documentTypes = documentTypes,
            enableExperimentalFunctions = enableExperimentalFunctions,
            enableSparseVectorCodeSearch = enableSparseVectorCodeSearch,
            examplePromptTemplate = examplePromptTemplate,
            fallbackTemplateId = fallbackTemplateId,
            functionTemplate = functionTemplate,
            inferenceEngine = inferenceEngine,
            insightTypes = insightTypes,
            issueSectionPromptTemplate = issueSectionPromptTemplate,
            jsonOutputFormatInstructions = jsonOutputFormatInstructions,
            localContextRetrievalWeight = localContextRetrievalWeight,
            maxDocuments = maxDocuments,
            maxExamples = maxExamples,
            maxLocalDocuments = maxLocalDocuments,
            maxPromptLength = maxPromptLength,
            maxRerankDocs = maxRerankDocs,
            minRankScore = minRankScore,
            mlFunctionOutputFormat = mlFunctionOutputFormat,
            name = name,
            numGeneratedSampleQuestions = numGeneratedSampleQuestions,
            parallelizeMLFunctionsByClass = parallelizeMLFunctionsByClass,
            prefixPromptTemplate = prefixPromptTemplate,
            promptTemplate = promptTemplate,
            pullRequestSectionPromptTemplate = pullRequestSectionPromptTemplate,
            queryPromptTemplate = queryPromptTemplate,
            questionerPromptTemplate = questionerPromptTemplate,
            recomposeDocumentPartitions = recomposeDocumentPartitions,
            referenceResolverType = referenceResolver,
            repoTemplate = repoTemplate,
            rerankModel = rerankModel,
            slackAutoResponseMinDocScore = slackAutoResponseMinDocScore,
            slackSectionPromptTemplate = slackSectionPromptTemplate,
            sourceTypes = sourceTypes,
            sparseVectorWeight = sparseVectorWeight,
            suffixPromptTemplate = suffixPromptTemplate,
            summariesTemplate = summariesTemplate,
            teamMemberPromptTemplate = teamMemberPromptTemplate,
            templateId = templateId,
            templateKind = templateKind,
            temperature = temperature,
            topicsPromptTemplate = topicsPromptTemplate,
            useAgenticRetrieval = useAgenticRetrieval,
            useCERR = useCERR,
            useCommitsAsSeedDataForSampleQuestionGeneration = useCommitsAsSeedDataForSampleQuestionGeneration,
            useDiffsAsSeedDataForSampleQuestionGeneration = useDiffsAsSeedDataForSampleQuestionGeneration,
            useHighlightedCodeCompressor = useHighlightedCodeCompressor,
            useMLFunctions = useMLFunctions,
            usePRsAsSeedDataForSampleQuestionGeneration = usePRsAsSeedDataForSampleQuestionGeneration,
            useRRF = useRRF,
            useSeedDataOnlyForSampleQuestionGeneration = useSeedDataOnlyForSampleQuestionGeneration,
            userLocalActiveContextTemplate = userLocalActiveContextTemplate,
            userLocalHighlightedContextTemplate = userLocalHighlightedContextTemplate,
            zeroDocsTemplate = zeroDocsTemplate,
        )

        call.respondRedirect(AdminNavigationTab.MLTemplates.route)
    }
}
