package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.stores.McpInferenceStore
import com.nextchaptersoftware.db.stores.OrgMemberDecorator
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.Stores
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.routing.RoutingContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.html.FlowContent
import kotlinx.html.ThScope
import kotlinx.html.h1
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object McpInferencesPage {
    suspend fun RoutingContext.renderMcpInferencesPage(
        page: AdminPage,
        orgStore: OrgStore,
        mcpInferenceStore: McpInferenceStore,
        orgMemberDecorator: OrgMemberDecorator,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()

        // Fetch all McpInferenceModel objects
        val mcpInferences = mcpInferenceStore.all().sortedByDescending { it.createdAt }

        // Batch fetch orgs
        val orgIds = mcpInferences.map { it.orgId }.toSet()
        val orgsById = orgIds.associateWith { orgId ->
            withContext(Dispatchers.IO) { Stores.orgStore.findById(orgId = orgId) }
        }

        // Batch fetch org member bundles (questioners)
        val orgMemberIds = mcpInferences.map { it.questionerOrgMemberId }.toSet()
        val orgMemberBundlesById = orgMemberIds.associateWith { orgMemberId ->
            withContext(Dispatchers.IO) { orgMemberDecorator.decorateOrgMember(orgMemberId = orgMemberId) }
        }

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                h1 { +"All MCP Inferences" }
                renderMcpInferencesTable(mcpInferences, orgsById, orgMemberBundlesById)
            }
        }
    }

    private fun FlowContent.renderMcpInferencesTable(
        mcpInferences: List<com.nextchaptersoftware.db.models.McpInference>,
        orgsById: Map<OrgId, com.nextchaptersoftware.db.models.Org?>,
        orgMemberBundlesById: Map<OrgMemberId, com.nextchaptersoftware.db.models.OrgMemberBundle?>,
    ) {
        table(classes = "table table-hover align-middle searchable") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Org" }
                    th(scope = ThScope.col) { +"Org Member" }
                    th(scope = ThScope.col) { +"Created At" }
                    th(scope = ThScope.col) { +"Modified At" }
                    th(scope = ThScope.col) { +"Tool Name" }
                    th(scope = ThScope.col) { +"Product Agent" }
                }
            }
            tbody {
                mcpInferences.forEach { inference ->
                    tr {
                        // Make row clickable if resultInferenceId is present
                        inference.resultInferenceId?.let { resultId ->
                            attributes["onclick"] = onClickAction("$WEB_ROOT/orgs/${inference.orgId}/machineLearning/inferences/${resultId.value}")
                            style = "cursor: pointer;"
                        }
                        // Org info
                        val org = orgsById[inference.orgId]
                        td {
                            if (org != null) {
                                profile(org)
                            } else {
                                +"-"
                            }
                        }
                        // Org member info
                        val bundle = orgMemberBundlesById[inference.questionerOrgMemberId]
                        td {
                            val identity = bundle?.orgMemberAndPerson?.person?.let { person ->
                                bundle.orgMembersAndIdentities.firstOrNull { it.identity.person == person.id }?.identity
                            } ?: bundle?.orgMembersAndIdentities?.firstOrNull()?.identity
                            if (identity != null) {
                                profile(identity)
                            } else {
                                +"-"
                            }
                        }
                        td { timeAgo(inference.createdAt) }
                        td { timeAgo(inference.modifiedAt) }
                        td { +inference.mcpToolName }
                        td { asBadge(inference.productAgent) }
                    }
                }
            }
        }
    }
}
