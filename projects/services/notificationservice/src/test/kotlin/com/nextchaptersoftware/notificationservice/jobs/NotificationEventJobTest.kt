package com.nextchaptersoftware.notificationservice.jobs

import com.nextchaptersoftware.activemq.ActiveMQConsumer.ActiveMQConsumerSession
import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.test.utils.MockAWSClientProvider
import com.nextchaptersoftware.aws.test.utils.SqsTestUtils.createFifoQueue
import com.nextchaptersoftware.aws.test.utils.SqsTestUtils.deleteQueueWithName
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.event.queue.dequeue.SequentialBatchEventDequeue
import com.nextchaptersoftware.event.queue.dequeue.StandardEventMessageProcessor
import com.nextchaptersoftware.event.queue.handlers.EventHandler
import com.nextchaptersoftware.event.queue.payloads.EventPayloadCompressor
import com.nextchaptersoftware.notification.events.email.models.EmailTrigger
import com.nextchaptersoftware.notification.events.queue.handlers.ThreadInviteEmailHandler
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationEvent
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationOrgEvent
import com.nextchaptersoftware.notification.events.services.OrgDescriptionModelService
import com.nextchaptersoftware.types.EmailAddress
import jakarta.jms.TextMessage
import java.util.UUID
import kotlin.time.Duration.Companion.seconds
import org.apache.activemq.command.ActiveMQTextMessage
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode.SAME_THREAD
import org.mockito.Mockito
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import software.amazon.awssdk.services.sqs.model.QueueAttributeName

@Execution(SAME_THREAD)
class NotificationEventJobTest : DatabaseTestsBase() {
    private val batchSize: Int = 2
    private val awsClientProvider: AWSClientProvider = MockAWSClientProvider.from()
    private val sender: EmailAddress = EmailAddress.of("<EMAIL>")
    private val senderName: String = "Unblocked"
    private val emailQueueName: String = "test-email-queue-.fifo"
    private val messageConsumer: ActiveMQConsumerSession = mock()
    private lateinit var job: NotificationEventJob

    private fun setup() {
        deleteQueueWithName(awsClientProvider.sqsClient, emailQueueName)
        createFifoQueue(awsClientProvider.sqsClient, emailQueueName, mapOf(QueueAttributeName.CONTENT_BASED_DEDUPLICATION to "true"))

        job = NotificationEventJob(
            jobTimeout = 10.seconds,
            eventDequeueService = SequentialBatchEventDequeue(
                batchSize = batchSize,
                messageConsumer = messageConsumer,
                eventMessageProcessor = StandardEventMessageProcessor(
                    handler = object : EventHandler {
                        override suspend fun handle(event: String): Boolean {
                            return ThreadInviteEmailHandler(
                                sender = sender,
                                senderName = senderName,
                                threadStore = Stores.threadStore,
                                messageStore = Stores.messageStore,
                                sourcePointStore = Stores.sourcePointStore,
                                sendGridTemplateEventHandler = mock(),
                                orgDescriptionModelService = OrgDescriptionModelService(),
                                slackNotifier = mock(),
                                urlBuilderProvider = StandardUrlBuilderProvider(),
                            ).handle(event.decode<NotificationEvent>() as NotificationOrgEvent.ThreadInviteEmailEvent)
                        }
                    },
                ),
            ),
        )
    }

    private suspend fun generatePayload(): TextMessage {
        val org = ModelBuilders.makeOrg()
        val scmTeam = ModelBuilders.makeScmTeam(org = org)
        val repo = ModelBuilders.makeRepo(scmTeam = scmTeam)
        val identity = ModelBuilders.makeIdentity()
        val member = ModelBuilders.makeMember(scmTeam = scmTeam, identity = identity)
        val thread = ModelBuilders.makeThread(org = org, author = member)
        ModelBuilders.makeSourceMark(thread = thread, scmTeam = scmTeam, repo = repo)
        ModelBuilders.makeMessage(thread = thread)
        val testMessage = Database.suspendedTransaction {
            NotificationOrgEvent.ThreadInviteEmailEvent(
                recipients = listOf(EmailAddress.of("${UUID.randomUUID()}@getunblocked.com")),
                orgId = scmTeam.orgId,
                senderIdentityId = identity.id.value,
                threadId = thread.idValue,
                trigger = EmailTrigger.THREAD_CREATED,
            )
        }

        return ActiveMQTextMessage().apply {
            text = EventPayloadCompressor().compressBody(testMessage.encode())
        }
    }

    @Test
    fun `queue multiple email messages`() = suspendingDatabaseTest {
        setup()
        Mockito.`when`(messageConsumer.receiveNoWait())
            .thenReturn(generatePayload())
            .thenReturn(generatePayload())

        job.run()
        verify(messageConsumer, times(batchSize)).receiveNoWait()
    }
}
