package com.nextchaptersoftware.scmservice.handlers.github.services

import com.nextchaptersoftware.db.stores.PullRequestStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.ThreadStore
import com.nextchaptersoftware.ghissues.services.GitHubIssuesIngestionService
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.maintenance.scm.ScmMembershipMaintenance
import com.nextchaptersoftware.pr.ingestion.PullRequestCommentService
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestArchiveEventService
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.github.models.GitHubIssueCommentEvent
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class GitHubIssueCommentEventService(
    private val teamAndRepoProvider: TeamAndRepoProvider,
    private val scmMembershipMaintenance: ScmMembershipMaintenance,
    private val pullRequestCommentService: PullRequestCommentService,
    private val indexingAndEmbeddingService: IndexingAndEmbeddingService,
    private val pullRequestArchiveEventService: PullRequestArchiveEventService,
    private val gitHubIssuesIngestionService: GitHubIssuesIngestionService,
    private val pullRequestStore: PullRequestStore = Stores.pullRequestStore,
    private val threadStore: ThreadStore = Stores.threadStore,
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
) {
    suspend fun processForPullRequest(
        scm: Scm,
        event: GitHubIssueCommentEvent,
    ): Unit = withLoggingContextAsync(
        "ownerExternalId" to event.ownerExternalId,
        "repoExternalId" to event.repoExternalId,
        "prNumber" to event.comment.pullRequestNumber,
    ) {
        val (scmTeam, repo) = teamAndRepoProvider.get(
            scm = scm,
            ownerExternalId = event.ownerExternalId,
            repoExternalId = event.repoExternalId,
        ) ?: return@withLoggingContextAsync

        val teamId = scmTeam.idValue
        val repoId = repo.id
        val prNumber = event.comment.pullRequestNumber

        when (event.action) {
            GitHubIssueCommentEvent.Action.Created,
            GitHubIssueCommentEvent.Action.Edited,
            -> {
                val scmMemberProvider = scmMembershipMaintenance.addMembers(
                    scmTeam = scmTeam.asDataModel(),
                    scmUsers = listOfNotNull(event.comment.user).map { it.asScmUser },
                )

                pullRequestCommentService.upsert(
                    scm = scm,
                    scmTeam = scmTeam,
                    repo = repo,
                    prNumber = prNumber,
                    comment = event.comment.asScmPrComment,
                    scmMemberProvider = scmMemberProvider,
                    createThreadUnreads = true,
                )
            }

            GitHubIssueCommentEvent.Action.Deleted,
            -> {
                pullRequestCommentService.delete(
                    repoId = repoId,
                    prNumber = prNumber,
                    commentId = event.comment.id.toString(),
                )
            }
        }

        pullRequestStore.findIdByRepoAndNumber(repoId = repoId, number = prNumber)?.let { prId ->
            indexingAndEmbeddingService.indexPullRequest(orgId = scmTeam.orgId, pullRequestId = prId)
        }
        pullRequestArchiveEventService.sendEvent(teamId = teamId, repoId = repoId, pullRequestNumber = prNumber)
    }

    suspend fun processForGitHubIssue(
        scm: Scm,
        event: GitHubIssueCommentEvent,
    ): Unit = withLoggingContextAsync(
        "ownerExternalId" to event.ownerExternalId,
        "repoExternalId" to event.repoExternalId,
        "issueNumber" to event.comment.issueNumber,
    ) {
        val (scmTeam, repo) = teamAndRepoProvider.get(
            scm = scm,
            ownerExternalId = event.ownerExternalId,
            repoExternalId = event.repoExternalId,
        ) ?: run {
            LOGGER.debugAsync("ownerExternalId" to event.ownerExternalId, "repoExternalId" to event.repoExternalId) {
                "Team and repo not found"
            }
            return@withLoggingContextAsync
        }

        val teamId = scmTeam.idValue
        val repoId = repo.id
        val issueNumber = event.comment.issueNumber
        val orgId = scmTeamStore.getOrgId(teamId = teamId)
        val threadId = threadStore.findByGitHubIssueNumber(
            trx = null,
            orgId = orgId,
            repoId = repoId,
            issueNumber = issueNumber,
        )?.idValue ?: run {
            LOGGER.debugAsync("teamId" to teamId, "repoId" to repoId, "issueNumber" to issueNumber) {
                "Thread not found"
            }
            return@withLoggingContextAsync
        }

        when (event.action) {
            GitHubIssueCommentEvent.Action.Created,
            GitHubIssueCommentEvent.Action.Edited,
            -> {
                val scmMemberProvider = scmMembershipMaintenance.addMembers(
                    scmTeam = scmTeam.asDataModel(),
                    scmUsers = listOfNotNull(event.comment.user?.asScmUser),
                )

                gitHubIssuesIngestionService.ingestComment(
                    orgId = orgId,
                    repoId = repoId,
                    comment = event.comment.asScmIssueComment,
                    scmMemberProvider = scmMemberProvider,
                )
            }

            GitHubIssueCommentEvent.Action.Deleted,
            -> {
                gitHubIssuesIngestionService.deleteComment(
                    threadId = threadId,
                    gitHubIssueCommentId = event.comment.id.toString(),
                )
            }
        }

        indexingAndEmbeddingService.indexThread(threadId = threadId)
    }
}
