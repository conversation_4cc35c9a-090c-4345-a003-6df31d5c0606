package com.nextchaptersoftware.authservice.api

import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.api.models.Provider
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.asana.api.AsanaApiProvider
import com.nextchaptersoftware.asana.auth.oauth.AsanaProviderAuthApi
import com.nextchaptersoftware.asana.ingestion.services.AsanaMemberModelService
import com.nextchaptersoftware.atlassian.api.AtlassianAuthApi
import com.nextchaptersoftware.atlassian.api.AtlassianUserApi
import com.nextchaptersoftware.atlassian.api.Me
import com.nextchaptersoftware.auth.oauth.EncryptedOAuthTokens
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.auth.provider.services.ProviderAuthenticationStateService
import com.nextchaptersoftware.authservice.providers.ProviderAuthApiFactory
import com.nextchaptersoftware.authservice.test.utils.UnblockedAuthApiClient
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.confluence.auth.oauth.ConfluenceAuthState
import com.nextchaptersoftware.confluence.auth.oauth.ConfluenceProviderAuthApi
import com.nextchaptersoftware.confluence.auth.services.ConfluenceAuthInstallResponse
import com.nextchaptersoftware.confluence.auth.services.ConfluenceAuthInstallService
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.SlackUserConnectOrigin
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.google.api.GoogleApiProvider
import com.nextchaptersoftware.google.api.GoogleAuthApi
import com.nextchaptersoftware.google.api.GoogleAuthApiProvider
import com.nextchaptersoftware.google.api.GoogleDriveApi
import com.nextchaptersoftware.google.auth.oauth.GoogleAuthState
import com.nextchaptersoftware.google.auth.oauth.GoogleProviderAuthApi
import com.nextchaptersoftware.google.auth.services.GoogleAuthInstallResponse
import com.nextchaptersoftware.google.auth.services.GoogleAuthInstallService
import com.nextchaptersoftware.google.services.GoogleCredentialProvider
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.auth.oauth.JiraAuthState
import com.nextchaptersoftware.jira.auth.oauth.JiraProviderAuthApi
import com.nextchaptersoftware.jira.ingestion.services.JiraMemberModelService
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.linear.api.LinearApiProvider
import com.nextchaptersoftware.linear.api.LinearAuthApi
import com.nextchaptersoftware.linear.auth.oauth.LinearAuthState
import com.nextchaptersoftware.linear.auth.oauth.LinearProviderAuthApi
import com.nextchaptersoftware.linear.auth.services.LinearAuthInstallResponse
import com.nextchaptersoftware.linear.auth.services.LinearAuthInstallService
import com.nextchaptersoftware.membership.alignment.IdentityAlignment
import com.nextchaptersoftware.notion.api.NotionApiProvider
import com.nextchaptersoftware.notion.api.NotionAuthApi
import com.nextchaptersoftware.notion.api.NotionUserApi
import com.nextchaptersoftware.notion.api.OAuthTokensAndIntegrationInfo
import com.nextchaptersoftware.notion.auth.oauth.NotionAuthState
import com.nextchaptersoftware.notion.auth.oauth.NotionProviderAuthApi
import com.nextchaptersoftware.notion.auth.services.NotionAuthInstallResponse
import com.nextchaptersoftware.notion.auth.services.NotionAuthInstallService
import com.nextchaptersoftware.notion.models.BotUser
import com.nextchaptersoftware.notion.models.User as NotionUser
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.nextchaptersoftware.slack.api.SlackAuthApi
import com.nextchaptersoftware.slack.api.SlackTeamApi
import com.nextchaptersoftware.slack.api.SlackUserApi
import com.nextchaptersoftware.slack.auth.oauth.SlackAuthState
import com.nextchaptersoftware.slack.auth.oauth.SlackProviderAuthApi
import com.nextchaptersoftware.slack.auth.services.SlackAuthInstallService
import com.nextchaptersoftware.slack.auth.services.SlackOpenIdConnectService
import com.nextchaptersoftware.slack.auth.services.SlackUserAuthInstallService
import com.nextchaptersoftware.slack.config.SlackConfigProvider
import com.nextchaptersoftware.slack.services.SlackMemberModelService
import com.nextchaptersoftware.user.secret.UserSecretServiceInterface
import com.nextchaptersoftware.utils.Base64.urlSafeBase64Encode
import com.sksamuel.hoplite.Secret
import com.slack.api.methods.response.oauth.OAuthV2AccessResponse
import com.slack.api.methods.response.oauth.OAuthV2AccessResponse.AuthedUser
import com.slack.api.methods.response.oauth.OAuthV2AccessResponse.Team
import com.slack.api.methods.response.openid.connect.OpenIDConnectTokenResponse
import com.slack.api.methods.response.openid.connect.OpenIDConnectUserInfoResponse
import com.slack.api.methods.response.team.TeamInfoResponse
import com.slack.api.methods.response.users.UsersInfoResponse
import com.slack.api.model.TeamIcon
import com.slack.api.model.User
import io.ktor.http.HttpStatusCode
import io.ktor.http.Url
import java.util.UUID
import kotlin.time.Duration.Companion.minutes
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.Transaction
import org.junit.jupiter.api.Test
import org.mockito.Mockito.times
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify

class AuthInstallApiDelegateImplTest : DatabaseTestsBase() {
    private val mockUserSecretService = object : UserSecretServiceInterface {
        override fun encrypt(secret: Secret): Ciphertext {
            return Ciphertext(secret.value.toByteArray())
        }

        override fun decrypt(ciphertext: Ciphertext): Secret {
            return Secret(ciphertext.value.toString())
        }

        override suspend fun saveEncryptedIdentityTokens(trx: Transaction, identityId: IdentityId, newTokens: OAuthTokens) {
            TODO("Not yet implemented")
        }

        override suspend fun getDecryptedIdentityTokens(trx: Transaction?, identityId: IdentityId): OAuthTokens? {
            TODO("Not yet implemented")
        }

        override fun encryptTokens(tokens: OAuthTokens): EncryptedOAuthTokens {
            return EncryptedOAuthTokens(
                rawAccessToken = Ciphertext(tokens.accessToken.value.toByteArray()),
                rawRefreshToken = tokens.refreshToken?.let { Ciphertext(it.value.toByteArray()) },
                accessTokenExpiresAt = tokens.accessTokenExpiresAt,
                refreshTokenExpiresAt = tokens.refreshTokenExpiresAt,
                accessTokenScope = tokens.accessTokenScope,
            )
        }

        override fun decryptTokens(tokens: EncryptedOAuthTokens): OAuthTokens? {
            TODO("Not yet implemented")
        }
    }

    private val installationStore = Stores.installationStore
    private val memberStore = Stores.memberStore
    private val identityAlignment = IdentityAlignment()

    private val exchangeCode = "richie"
    private val slackConfigProvider: SlackConfigProvider = SlackConfigProvider()
    private val slackAuthApi: SlackAuthApi = mock()
    private val slackTeamApi: SlackTeamApi = mock()
    private val slackUserApi: SlackUserApi = mock()
    private val slackApiProvider: SlackApiProvider = mock()
    private val providerAuthenticationStateService: ProviderAuthenticationStateService = ProviderAuthenticationStateService(
        providerAuthenticationStateStore = Stores.providerAuthenticationStateStore,
        jwt = Jwt(authenticationConfig = GlobalConfig.INSTANCE.authentication),
        expiry = 30.minutes,
    )
    private val slackTeamStore = Stores.slackTeamStore
    private val slackMemberModelService = SlackMemberModelService()
    private val slackAuthInstallService = SlackAuthInstallService(
        slackApiProvider = slackApiProvider,
        userSecretService = mockUserSecretService,
        slackTeamStore = slackTeamStore,
        slackEventEnqueueService = mock(),
        installationStore = installationStore,
        slackNotifier = mock(),
        providerAuthenticationStateService = providerAuthenticationStateService,
        slackConfigProvider = slackConfigProvider,
        identityAlignment = mock(),
        segmentProvider = mock(),
        slackMemberModelService = slackMemberModelService,
    )
    private val slackOpenIdConnectService = SlackOpenIdConnectService(
        slackConfigProvider = slackConfigProvider,
        slackApiProvider = slackApiProvider,
    )
    private val slackUserAuthInstallService = SlackUserAuthInstallService(
        slackOpenIdConnectService = slackOpenIdConnectService,
        slackTeamStore = slackTeamStore,
        identityAlignment = identityAlignment,
        slackMemberModelService = slackMemberModelService,
        slackUserConnectService = mock(),
    )
    private val asanaApiProvider: AsanaApiProvider = mock()
    private val asanaMemberModelService: AsanaMemberModelService = mock()
    private val jiraApiProvider: JiraApiProvider = mock()
    private val jiraAuthApi: AtlassianAuthApi = mock()
    private val jiraMemberModelService: JiraMemberModelService = mock()
    private val confluenceAuthApi: AtlassianAuthApi = mock()
    private val confluenceAuthInstallService: ConfluenceAuthInstallService = mock()
    private val linearApiProvider: LinearApiProvider = mock()
    private val linearAuthInstallService: LinearAuthInstallService = mock()
    private val notionApiProvider: NotionApiProvider = mock()
    private val notionAuthInstallService: NotionAuthInstallService = mock()
    private val googleApiProvider: GoogleApiProvider = mock()
    private val googleAuthApiProvider: GoogleAuthApiProvider = mock()
    private val googleAuthInstallService: GoogleAuthInstallService = mock()
    private val googleCredentialProvider: GoogleCredentialProvider = mock()

    private val providerAuthApiFactory = ProviderAuthApiFactory(
        asanaProviderAuthApi = AsanaProviderAuthApi(
            asanaApiProvider = asanaApiProvider,
            asanaMemberModelService = asanaMemberModelService,
        ),
        slackProviderAuthApi = SlackProviderAuthApi(
            slackAuthInstallService = slackAuthInstallService,
            slackUserAuthInstallService = slackUserAuthInstallService,
        ),
        jiraProviderAuthApi = JiraProviderAuthApi(
            jiraAuthApi = jiraAuthApi,
            jiraApiProvider = jiraApiProvider,
            jiraMemberModelService = jiraMemberModelService,
        ),
        confluenceProviderAuthApi = ConfluenceProviderAuthApi(
            confluenceAuthApi = confluenceAuthApi,
            confluenceAuthInstallService = confluenceAuthInstallService,
        ),
        notionProviderAuthApi = NotionProviderAuthApi(
            notionApiProvider = notionApiProvider,
            notionAuthInstallService = notionAuthInstallService,
        ),
        linearProviderAuthApi = LinearProviderAuthApi(
            linearApiProvider = linearApiProvider,
            linearAuthInstallService = linearAuthInstallService,
        ),
        googleProviderAuthApi = GoogleProviderAuthApi(
            googleApiProvider = googleApiProvider,
            googleAuthApiProvider = googleAuthApiProvider,
            googleAuthInstallService = googleAuthInstallService,
            googleCredentialProvider = googleCredentialProvider,
        ),
    )

    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var person: PersonDAO
    private lateinit var identity: IdentityDAO
    private lateinit var installation: InstallationDAO

    @Suppress("LongMethod")
    private suspend fun setup() {
        org = ModelBuilders.makeOrg()
        scmTeam = ModelBuilders.makeScmTeam(
            org = org,
        )
        installation = ModelBuilders.makeInstallation(
            org = org,
        )
        person = ModelBuilders.makePerson()
        identity = ModelBuilders.makeIdentity(person = person)
    }

    private suspend fun setupSlack() {
        val slackConfig = slackConfigProvider.getSlackConfig(orgId = org.idValue)

        `when`(slackApiProvider.slackTeamApi).thenReturn(slackTeamApi)
        `when`(slackApiProvider.slackAuthApi).thenReturn(slackAuthApi)
        `when`(slackApiProvider.slackUserApi).thenReturn(slackUserApi)

        `when`(
            slackAuthApi.oauthV2Access(
                clientId = eq(slackConfig.oauth.clientId),
                clientSecret = eq(slackConfig.clientSecret.value),
                code = eq(exchangeCode),
                redirectUri = anyOrNull(),
            ),
        )
            .thenReturn(
                OAuthV2AccessResponse().also {
                    it.botUserId = "richieBot"
                    it.accessToken = "richieBotToken"
                    it.scope = "richieBotScope"
                    it.authedUser = AuthedUser().apply {
                        id = "richieId"
                        accessToken = "authedUserToken"
                        scope = "richieScope"
                    }
                    it.team = Team().apply {
                        id = installation.installationExternalId
                        name = "RichieTeam"
                    }
                    it.isOk = true
                },
            )

        `when`(
            slackAuthApi.openIdConnectUserInfo(
                token = any(),
            ),
        )
            .thenReturn(
                OpenIDConnectUserInfoResponse().also {
                    it.name = "Richard Bresnan"
                    it.email = "<EMAIL>"
                    it.userImage72 = "blah"
                    it.userId = "richieId"
                    it.teamId = installation.installationExternalId
                    it.isOk = true
                },
            )

        `when`(
            slackAuthApi.openIdConnectToken(
                clientId = eq(slackConfig.oauth.clientId),
                clientSecret = eq(slackConfig.clientSecret.value),
                code = eq(exchangeCode),
                redirectUri = anyOrNull(),
            ),
        )
            .thenReturn(
                OpenIDConnectTokenResponse().also {
                    it.accessToken = "blah"
                    it.isOk = true
                },
            )

        `when`(
            slackTeamApi.getTeamInfo(token = anyOrNull(), teamId = anyOrNull()),
        ).thenReturn(
            TeamInfoResponse().also { teamInfo ->
                teamInfo.team = com.slack.api.model.Team().also { team ->
                    team.id = "richie"
                    team.name = "riche"
                    team.icon = TeamIcon().also { teamIcon ->
                        teamIcon.imageOriginal = "http://richie.com"
                    }
                    team.url = "https://richie.slack.com/"
                    team.domain = "richie"
                }
            },
        )

        `when`(
            slackUserApi.getUsersInfo(token = anyOrNull(), userId = anyOrNull()),
        ).thenReturn(
            UsersInfoResponse().also {
                it.user = User().apply {
                    this.id = "richieId"
                    this.isOwner = true
                    this.name = "richie"
                    this.realName = "Richard Bresnan"
                    this.isBot = false
                    this.teamId = installation.installationExternalId
                }
            },
        )
    }

    @Suppress("LongMethod")
    @Test
    fun `test slack auth token exchange`() = suspendingDatabaseTest {
        setup()
        setupSlack()

        val slackAuthState = SlackAuthState(
            orgId = org.idValue,
            redirectUrl = Url("https://google.com"),
            identityId = identity.id.value,
            personId = person.idValue,
            origin = SlackUserConnectOrigin.SLACK,
            installationId = null,
        )

        val nonce = UUID.randomUUID()

        val oAuthState = OAuthState(
            provider = Provider.slack,
            state = slackAuthState.encodeAuthState(),
            nonce = nonce,
        )

        providerAuthenticationStateService.setState(
            provider = com.nextchaptersoftware.db.models.Provider.Slack,
            orgId = org.idValue,
            identityId = identity.id.value,
            nonce = nonce,
            state = slackAuthState.encodeAuthState(),
        )

        assertThat(slackTeamStore.findBySlackExternalTeamId(slackExternalTeamId = installation.installationExternalId))
            .isNull()

        UnblockedAuthApiClient(
            database = database,
            providerAuthApiFactory = providerAuthApiFactory,
        ).authExchange(
            provider = Provider.slack,
            code = exchangeCode,
            state = oAuthState.encode().urlSafeBase64Encode(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Found)

            val slackTeamInfo = slackTeamStore.findBySlackExternalTeamId(slackExternalTeamId = installation.installationExternalId)
                ?: error("No slack team found")

            // Check that the installation is associated with the slack team
            assertThat(slackTeamInfo.installation.id.value)
                .isEqualTo(installation.id.value)

            val members = memberStore.findByInstallationId(installationId = installation.id.value)

            assertThat(members).hasSize(1)
            assertThat(members.single().identity.rawAccessToken?.value?.let(::String)).isEqualTo("authedUserToken")
            assertThat(members.single().identity.accessTokenScope).isEqualTo("richieScope")
        }
    }

    @Suppress("LongMethod")
    @Test
    fun `test slack auth token exchange failure when another team has installation`() = suspendingDatabaseTest {
        setup()
        setupSlack()

        val otherOrg = ModelBuilders.makeOrg()
        ModelBuilders.makeScmTeam(
            org = otherOrg,
        )

        ModelBuilders.makeInstallation(
            org = otherOrg,
            provider = com.nextchaptersoftware.db.models.Provider.Slack,
            installationExternalId = installation.installationExternalId,
        )

        val slackAuthState = SlackAuthState(
            orgId = otherOrg.idValue,
            redirectUrl = Url("https://google.com"),
            identityId = IdentityId.random(),
            personId = PersonId.random(),
            installationId = null,
            origin = SlackUserConnectOrigin.SLACK,
        )

        val nonce = UUID.randomUUID()

        val oAuthState = OAuthState(
            provider = Provider.slack,
            state = slackAuthState.encodeAuthState(),
            nonce = nonce,
        )

        providerAuthenticationStateService.setState(
            provider = com.nextchaptersoftware.db.models.Provider.Slack,
            orgId = otherOrg.idValue,
            identityId = identity.id.value,
            nonce = nonce,
            state = slackAuthState.encodeAuthState(),
        )

        UnblockedAuthApiClient(
            database = database,
            providerAuthApiFactory = providerAuthApiFactory,
        ).authExchange(
            provider = Provider.slack,
            code = exchangeCode,
            state = oAuthState.encode().urlSafeBase64Encode(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Found)
        }
    }

    @Test
    fun `test slack user auth token exchange`() = suspendingDatabaseTest {
        setup()
        setupSlack()

        ModelBuilders.makeSlackTeam(
            installation = installation,
            org = org,
            slackExternalTeamId = installation.installationExternalId,
        )

        val slackAuthState = SlackAuthState(
            orgId = org.idValue,
            redirectUrl = Url("https://google.com"),
            identityId = identity.id.value,
            personId = person.idValue,
            installationId = installation.idValue,
            origin = SlackUserConnectOrigin.SLACK,
        )

        val nonce = UUID.randomUUID()

        val oAuthState = OAuthState(
            provider = Provider.slack,
            state = slackAuthState.encodeAuthState(),
            nonce = nonce,
        )

        providerAuthenticationStateService.setState(
            provider = com.nextchaptersoftware.db.models.Provider.Slack,
            orgId = org.idValue,
            identityId = identity.id.value,
            nonce = nonce,
            state = slackAuthState.encodeAuthState(),
        )

        UnblockedAuthApiClient(
            database = database,
            providerAuthApiFactory = providerAuthApiFactory,
        ).authExchange(
            provider = Provider.slack,
            code = exchangeCode,
            state = oAuthState.encode().urlSafeBase64Encode(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Found)

            val members = memberStore.findByInstallationId(installationId = installation.id.value)

            assertThat(members).hasSize(1)
            assertThat(members.first().identity.person).isEqualTo(person.idValue)
        }
    }

    @Test
    fun `test jira auth token exchange`() = suspendingDatabaseTest {
        setup()
        val jiraAuthState = JiraAuthState(
            orgId = org.idValue,
            personId = person.idValue,
            redirectUrl = Url("https://google.com"),
        )

        val oAuthState = OAuthState(
            provider = Provider.jira,
            state = jiraAuthState.encodeAuthState(),
        )

        val jiraUserApi: AtlassianUserApi = mock()
        `when`(jiraApiProvider.jiraUserApi).thenReturn(jiraUserApi)

        val oauthTokens = OAuthTokens(accessToken = Secret("hello"))
        `when`(jiraAuthApi.exchangeForToken(code = eq(exchangeCode))).thenReturn(oauthTokens)

        val me = Me(
            accountId = "richie",
            avatarUrl = "www.google.com".asUrl,
            name = "richie",
            email = "<EMAIL>",
        )
        `when`(jiraUserApi.me(accessToken = oauthTokens.accessToken)).thenReturn(me)

        UnblockedAuthApiClient(
            database = database,
            providerAuthApiFactory = providerAuthApiFactory,
        ).authExchange(
            provider = Provider.jira,
            code = exchangeCode,
            state = oAuthState.encode().urlSafeBase64Encode(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Found)

            verify(jiraMemberModelService, times(1)).upsertIdentity(
                personId = jiraAuthState.personId,
                jiraAccountId = me.accountId,
                jiraName = me.name,
                jiraAvatarUrl = me.avatarUrl,
                oauthTokens = oauthTokens,
            )
        }
    }

    @Test
    fun `test confluence auth token exchange`() = suspendingDatabaseTest {
        setup()
        val confluenceAuthState = ConfluenceAuthState(
            orgId = org.idValue,
            personId = person.idValue,
            redirectUrl = Url("https://google.com"),
        )

        val oAuthState = OAuthState(
            provider = Provider.confluence,
            state = confluenceAuthState.encodeAuthState(),
        )

        val oauthTokens = OAuthTokens(accessToken = Secret("hello"))
        `when`(confluenceAuthApi.exchangeForToken(code = eq(exchangeCode))).thenReturn(oauthTokens)

        `when`(
            confluenceAuthInstallService.handleAuthInstall(
                orgId = confluenceAuthState.orgId,
                personId = confluenceAuthState.personId,
                oauthTokens = oauthTokens,
            ),
        ).thenReturn(
            ConfluenceAuthInstallResponse(
                installation = installation.asDataModel(),
            ),
        )

        UnblockedAuthApiClient(
            database = database,
            providerAuthApiFactory = providerAuthApiFactory,
        ).authExchange(
            provider = Provider.confluence,
            code = exchangeCode,
            state = oAuthState.encode().urlSafeBase64Encode(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Found)
        }
    }

    @Test
    fun `test linear auth token exchange`() = suspendingDatabaseTest {
        setup()
        val linearAuthState = LinearAuthState(
            orgId = OrgId.fromString("9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361"),
            personId = PersonId.fromString("204887ae-4863-4f3b-88ad-eab8f68ce155"),
            redirectUrl = Url("https://google.com"),
        )

        val oAuthState = OAuthState(
            provider = Provider.linear,
            state = linearAuthState.encodeAuthState(),
        )

        val linearAuthApi: LinearAuthApi = mock()
        `when`(linearApiProvider.linearAuthApi).thenReturn(linearAuthApi)

        val oauthTokens = OAuthTokens(accessToken = Secret("hello"))
        `when`(linearAuthApi.exchangeForToken(eq(exchangeCode))).thenReturn(oauthTokens)

        `when`(
            linearAuthInstallService.handleAuthInstall(
                orgId = linearAuthState.orgId,
                personId = linearAuthState.personId,
                oauthTokens = oauthTokens,
            ),
        ).thenReturn(
            LinearAuthInstallResponse(
                installation = installation.asDataModel(),
            ),
        )

        UnblockedAuthApiClient(
            database = database,
            providerAuthApiFactory = providerAuthApiFactory,
        ).authExchange(
            provider = Provider.linear,
            code = exchangeCode,
            state = oAuthState.encode().urlSafeBase64Encode(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Found)
        }
    }

    @Suppress("LongMethod")
    @Test
    fun `test notion auth token exchange`() = suspendingDatabaseTest {
        setup()
        val authState = NotionAuthState(
            orgId = OrgId.fromString("9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361"),
            personId = PersonId.fromString("204887ae-4863-4f3b-88ad-eab8f68ce155"),
            redirectUrl = Url("https://google.com"),
        )

        val oAuthState = OAuthState(
            provider = Provider.linear,
            state = authState.encodeAuthState(),
        )

        val oauthTokensAndIntegrationInfo = OAuthTokensAndIntegrationInfo(
            oAuthTokens = OAuthTokens(accessToken = Secret("hello")),
            avatarUrl = Url("www.google.com"),
            installationExternalId = "1234",
            displayName = "notion_workspace",
        )

        val authApi: NotionAuthApi = mock()
        val userApi: NotionUserApi = mock()
        `when`(notionApiProvider.authApi).thenReturn(authApi)
        `when`(notionApiProvider.userApi).thenReturn(userApi)

        `when`(authApi.exchangeForToken(code = eq(exchangeCode))).thenReturn(oauthTokensAndIntegrationInfo)

        val botUser = BotUser(
            bot = BotUser.Bot(
                owner = BotUser.Owner(
                    type = "person",
                    user = NotionUser(
                        id = "1234",
                        name = "notion_bot",
                        avatarUrl = "www.google.com",
                        person = NotionUser.Person(
                            email = "<EMAIL>",
                        ),
                    ),
                ),
            ),
        )

        `when`(userApi.getBotUser(accessToken = oauthTokensAndIntegrationInfo.oAuthTokens.accessToken)).thenReturn(botUser)

        `when`(
            notionAuthInstallService.handleAuthInstall(
                orgId = authState.orgId,
                personId = authState.personId,
                user = botUser.bot.owner.user,
                oauthTokens = oauthTokensAndIntegrationInfo.oAuthTokens,
                avatarUrl = oauthTokensAndIntegrationInfo.avatarUrl,
                installationExternalId = oauthTokensAndIntegrationInfo.installationExternalId,
                displayName = oauthTokensAndIntegrationInfo.displayName,
            ),
        ).thenReturn(
            NotionAuthInstallResponse(
                installation = installation.asDataModel(),
            ),
        )

        UnblockedAuthApiClient(
            database = database,
            providerAuthApiFactory = providerAuthApiFactory,
        ).authExchange(
            provider = Provider.notion,
            code = exchangeCode,
            state = oAuthState.encode().urlSafeBase64Encode(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Found)
        }
    }

    @Test
    fun `test google auth token exchange`() = suspendingDatabaseTest {
        setup()
        val authState = GoogleAuthState(
            orgId = OrgId.fromString("9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361"),
            personId = PersonId.fromString("204887ae-4863-4f3b-88ad-eab8f68ce155"),
            redirectUrl = Url("https://google.com"),
        )

        val oAuthState = OAuthState(
            provider = Provider.google,
            state = authState.encodeAuthState(),
        )

        val authApi: GoogleAuthApi = mock()
        val driveApi: GoogleDriveApi = mock()
        `when`(googleAuthApiProvider.authApi).thenReturn(authApi)
        `when`(googleApiProvider.driveApi).thenReturn(driveApi)

        val oauthTokens = OAuthTokens(accessToken = Secret("hello"))
        `when`(authApi.exchangeForToken(code = eq(exchangeCode))).thenReturn(oauthTokens)

        val user = com.google.api.services.drive.model.User()
        user.displayName = "blah blah blah"
        user.emailAddress = "<EMAIL>"
        user.photoLink = "google.com"

        val about = com.google.api.services.drive.model.About()
        about.user = user

        `when`(googleCredentialProvider.getCredential(accessToken = any())).thenReturn(mock())
        `when`(driveApi.about(credential = any())).thenReturn(about)

        `when`(
            googleAuthInstallService.handleAuthInstall(
                orgId = authState.orgId,
                personId = authState.personId,
                oauthTokens = oauthTokens,
                user = user,
            ),
        ).thenReturn(
            GoogleAuthInstallResponse(
                installation = installation.asDataModel(),
            ),
        )

        UnblockedAuthApiClient(
            database = database,
            providerAuthApiFactory = providerAuthApiFactory,
        ).authExchange(
            provider = Provider.google,
            code = exchangeCode,
            state = oAuthState.encode().urlSafeBase64Encode(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Found)
        }
    }
}
