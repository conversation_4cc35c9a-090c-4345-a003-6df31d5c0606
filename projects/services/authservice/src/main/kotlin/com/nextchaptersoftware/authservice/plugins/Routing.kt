package com.nextchaptersoftware.authservice.plugins

import com.nextchaptersoftware.access.RestrictedAccessServiceInterface
import com.nextchaptersoftware.api.AuthApi
import com.nextchaptersoftware.api.AuthInstallApi
import com.nextchaptersoftware.api.HealthApi
import com.nextchaptersoftware.api.SAMLAuthApi
import com.nextchaptersoftware.api.SCMEnterpriseIntegrationsApi
import com.nextchaptersoftware.api.auth.services.AccessValidation
import com.nextchaptersoftware.api.auth.services.LoginOptionsService
import com.nextchaptersoftware.api.auth.services.LoginService
import com.nextchaptersoftware.api.auth.services.PreAuthService
import com.nextchaptersoftware.api.auth.services.url.redirect.RedirectAuthOverrideService
import com.nextchaptersoftware.api.serialization.SerializationExtensions.installSerializer
import com.nextchaptersoftware.api.services.PersonService
import com.nextchaptersoftware.api.services.SegmentService
import com.nextchaptersoftware.api.services.SessionEventService
import com.nextchaptersoftware.api.services.VersionObsoleteService
import com.nextchaptersoftware.api.services.enterprise.ScmEnterpriseService
import com.nextchaptersoftware.auth.saml.SamlAuthProviderService
import com.nextchaptersoftware.auth.saml.SamlDiscoveryService
import com.nextchaptersoftware.auth.saml.SamlSettingsProviderService
import com.nextchaptersoftware.authservice.api.AuthApiDelegateImpl
import com.nextchaptersoftware.authservice.api.AuthInstallApiDelegateImpl
import com.nextchaptersoftware.authservice.api.HealthApiDelegateImpl
import com.nextchaptersoftware.authservice.api.SAMLAuthApiDelegateImpl
import com.nextchaptersoftware.authservice.api.SCMEnterpriseIntegrationsApiDelegateImpl
import com.nextchaptersoftware.authservice.providers.ProviderAuthService
import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.cas.RedisCAS
import com.nextchaptersoftware.config.AuthenticationConfig
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.maintenance.MemberMaintenance
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.scim.ScimService
import com.nextchaptersoftware.scm.ScmNoAuthApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.apps.AppManifestProvider
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.service.ServiceHealthApi
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.http.content.staticResources
import io.ktor.server.resources.Resources
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import kotlin.time.Duration.Companion.minutes

fun Application.configureRouting(
    appManifestProvider: AppManifestProvider,
    authenticationConfig: AuthenticationConfig,
    insiderService: InsiderServiceInterface,
    jwt: Jwt,
    loginOptionsService: LoginOptionsService,
    loginService: LoginService,
    memberMaintenance: MemberMaintenance,
    personService: PersonService,
    planCapabilitiesService: PlanCapabilitiesService,
    providerAuthService: ProviderAuthService,
    redirectAuthOverrideService: RedirectAuthOverrideService,
    repoAccessService: RepoAccessService,
    restrictedAccessService: RestrictedAccessServiceInterface,
    samlAuthProviderService: SamlAuthProviderService,
    samlSettingsProviderService: SamlSettingsProviderService,
    scmConfig: ScmConfig,
    scmEnterpriseService: ScmEnterpriseService,
    scmNoAuthApiFactory: ScmNoAuthApiFactory,
    scmWebFactory: ScmWebFactory,
    segmentService: SegmentService,
    serviceLifecycle: ServiceLifecycle,
    sessionEventService: SessionEventService,
    urlBuilderProvider: UrlBuilderProvider,
) {
    install(Resources) {
        installSerializer()
    }

    val samlDiscoveryService by lazy {
        SamlDiscoveryService()
    }
    val healthApiDelegateImpl by lazy {
        HealthApiDelegateImpl(ServiceHealthApi(serviceLifecycle = serviceLifecycle))
    }

    val accessValidation by lazy {
        AccessValidation()
    }

    val capabilityValidation by lazy {
        CapabilityValidation(planCapabilitiesService = planCapabilitiesService)
    }

    val versionObsoleteService by lazy {
        VersionObsoleteService()
    }

    val samlRelayStateStore by lazy {
        RedisCAS(namespace = "SAML-RELAY-STATE", ttl = 5.minutes)
    }

    val samlCodeExchangeStore by lazy {
        RedisCAS(namespace = "SAML-CODE-EXCHANGE", ttl = 5.minutes)
    }

    val scimService by lazy {
        ScimService(
            memberMaintenance = memberMaintenance,
        )
    }

    routing {
        route("/api") {
            route("/auth/public") {
                staticResources(remotePath = "", basePackage = "public")
            }

            HealthApi(healthApiDelegateImpl)
            AuthApi(
                AuthApiDelegateImpl(
                    authenticationConfig = authenticationConfig,
                    insiderService = insiderService,
                    loginOptionsService = loginOptionsService,
                    loginService = loginService,
                    personService = personService,
                    preAuthService = PreAuthService(jwt),
                    repoAccessService = repoAccessService,
                    samlAuthProviderService = samlAuthProviderService,
                    samlRelayStateStore = samlRelayStateStore,
                    scmConfig = scmConfig,
                    segmentService = segmentService,
                    sessionEventService = sessionEventService,
                    versionObsoleteService = versionObsoleteService,
                ),
            )
            AuthInstallApi(
                AuthInstallApiDelegateImpl(
                    urlBuilderProvider = urlBuilderProvider,
                    providerAuthService = providerAuthService,
                ),
            )
            SAMLAuthApi(
                SAMLAuthApiDelegateImpl(
                    config = authenticationConfig,
                    loginService = loginService,
                    memberMaintenance = memberMaintenance,
                    redirectAuthOverrideService = redirectAuthOverrideService,
                    samlAuthProviderService = samlAuthProviderService,
                    samlCodeExchangeStore = samlCodeExchangeStore,
                    samlDiscoveryService = samlDiscoveryService,
                    samlRelayStateStore = samlRelayStateStore,
                    samlSettingsProviderService = samlSettingsProviderService,
                    scimService = scimService,
                ),
            )
            SCMEnterpriseIntegrationsApi(
                SCMEnterpriseIntegrationsApiDelegateImpl(
                    accessValidation = accessValidation,
                    appManifestProvider = appManifestProvider,
                    authenticationConfig = authenticationConfig,
                    capabilityValidation = capabilityValidation,
                    loginService = loginService,
                    restrictedAccessService = restrictedAccessService,
                    scmConfig = scmConfig,
                    scmEnterpriseService = scmEnterpriseService,
                    scmNoAuthApiFactory = scmNoAuthApiFactory,
                    scmWebFactory = scmWebFactory,
                ),
            )
        }

        // Path used for exposing health endpoint to Kubernetes
        route("/api/health/authservice") {
            HealthApi(healthApiDelegateImpl)
        }
    }
}
