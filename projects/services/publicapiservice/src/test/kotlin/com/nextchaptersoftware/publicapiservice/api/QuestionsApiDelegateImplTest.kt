package com.nextchaptersoftware.publicapiservice.api

import com.nextchaptersoftware.api.public.key.ApiKeyPersistence
import com.nextchaptersoftware.api.public.models.FeedbackFilterType
import com.nextchaptersoftware.api.public.models.Question
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.db.ModelBuilders.makeMLInference
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.ModelBuilders.makeMessageFeedback
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.getDatabase
import com.nextchaptersoftware.db.models.ApiKeyAudience
import com.nextchaptersoftware.db.models.FeedbackType
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.ktor.client.LinkHeaders
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.publicapiservice.test.conditions.HttpHeaderConditions.hasLinkWithRelToNext
import com.nextchaptersoftware.publicapiservice.test.conditions.HttpHeaderConditions.hasLinks
import com.nextchaptersoftware.publicapiservice.test.conditions.HttpHeaderConditions.notHasLinkWithRelToNext
import com.nextchaptersoftware.publicapiservice.test.conditions.HttpHeaderConditions.notHasLinkWithRelToPrevious
import com.nextchaptersoftware.publicapiservice.test.conditions.HttpHeaderConditions.notHasLinks
import com.nextchaptersoftware.publicapiservice.test.utils.PublicApiAuthContext
import com.nextchaptersoftware.publicapiservice.test.utils.UnblockedPublicApiClient
import com.nextchaptersoftware.utils.asApiDateTime
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.sksamuel.hoplite.Secret
import io.ktor.client.call.body
import io.ktor.http.HttpStatusCode
import io.ktor.http.LinkHeader
import io.ktor.http.Url
import java.util.UUID
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.spy
import org.mockito.kotlin.mock
import org.mockito.kotlin.verifyNoInteractions

class QuestionsApiDelegateImplTest : DatabaseTestsBase() {
    private val questionsStore = spy(Stores.questionsStore)
    private val botAccountService = InstallationBotAccountService(urlBuilderProvider = StandardUrlBuilderProvider())
    private val apiKeyPersistence = ApiKeyPersistence()

    private lateinit var client: UnblockedPublicApiClient
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var orgMember: OrgMemberDAO
    private lateinit var teamMember: MemberDAO
    private lateinit var botMember: MemberDAO

    private suspend fun setup() {
        org = makeOrg()
        orgMember = makeOrgMember(org = org)

        val apiKey = apiKeyPersistence.create(
            orgId = org.idValue,
            orgMemberCreatorId = orgMember.idValue,
            name = "key",
            audience = ApiKeyAudience.PublicApi,
        ).second

        client = UnblockedPublicApiClient(
            authContext = PublicApiAuthContext.Authenticated(bearerToken = apiKey),
            database = currentCoroutineContext().getDatabase(),
            collectionDocumentService = mock(),
            questionsStore = questionsStore,
        )

        scmTeam = makeScmTeam(org = org)
        teamMember = makeMember(scmTeam = scmTeam, orgMember = orgMember)
        botMember = botAccountService.upsertBotAccountMember(orgId = org.idValue).let {
            suspendedTransaction { MemberDAO[it.id] }
        }
    }

    @Test
    fun `listQuestions -- unauthorized`() = suspendingDatabaseTest {
        setup()
        UnblockedPublicApiClient(
            authContext = PublicApiAuthContext.Authenticated(bearerToken = Secret(UUID.randomUUID().toString())),
            database = currentCoroutineContext().getDatabase(),
            collectionDocumentService = mock(),
            questionsStore = questionsStore,
        ).listQuestions {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
            verifyNoInteractions(questionsStore)
            assertThat(headers).satisfies(notHasLinks)
        }
    }

    @Test
    fun `listQuestions -- empty`() = suspendingDatabaseTest {
        setup()
        client.listQuestions {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).isEmpty()

            assertThat(headers)
                .satisfies(notHasLinks)
                .satisfies(notHasLinkWithRelToNext)
                .satisfies(notHasLinkWithRelToPrevious)
        }
    }

    @Test
    fun `listQuestions -- with limit and pagination`() = suspendingDatabaseTest {
        setup()

        val messageIdA = makeThread(org = org).let { thread ->
            val question = makeMessage(thread = thread, author = teamMember)
            val answer = makeMessage(thread = thread, author = botMember)
            makeMLInference(org = org, botMessage = answer, questionerOrgMember = orgMember)
            question
        }.id.value.value

        val messageIdB = makeThread(org = org).let { thread ->
            val question = makeMessage(thread = thread, author = teamMember)
            val answer = makeMessage(thread = thread, author = botMember)
            makeMLInference(org = org, botMessage = answer, questionerOrgMember = orgMember)
            question
        }.id.value.value

        client.listQuestions {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(2)
            assertThat(list.map { it.id }).containsExactly(messageIdB, messageIdA)

            assertThat(headers)
                .satisfies(notHasLinks)
                .satisfies(notHasLinkWithRelToNext)
                .satisfies(notHasLinkWithRelToPrevious)
        }

        var next: Url? = null

        client.listQuestions(limit = 1) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(1)
            assertThat(list.single().id).isEqualTo(messageIdB)

            assertThat(headers)
                .satisfies(hasLinks)
                .satisfies(hasLinkWithRelToNext)
                .satisfies(notHasLinkWithRelToPrevious)

            next = LinkHeaders.getRelation(response = this, LinkHeader.Rel.Next)
        }

        client.get(
            // Get next page
            path = requireNotNull(next).encodedPathAndQuery,
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(1)
            assertThat(list.single().id).isEqualTo(messageIdA)

            assertThat(headers)
                .satisfies(notHasLinks)
                .satisfies(notHasLinkWithRelToNext)
                .satisfies(notHasLinkWithRelToPrevious)
        }
    }

    @Test
    fun `listQuestions -- with feedback filter`() = suspendingDatabaseTest {
        setup()

        val messageIdA = makeThread(org = org).let { thread ->
            val question = makeMessage(thread = thread, author = teamMember)
            val answer = makeMessage(thread = thread, author = botMember)
            makeMessageFeedback(thread = thread, message = answer, feedbackType = FeedbackType.Negative)
            makeMLInference(org = org, botMessage = answer, questionerOrgMember = orgMember)
            question
        }.id.value.value

        val messageIdB = makeThread(org = org).let { thread ->
            val question = makeMessage(thread = thread, author = teamMember)
            val botMessage = makeMessage(thread = thread, author = botMember)
            makeMessageFeedback(thread = thread, message = botMessage, feedbackType = FeedbackType.Positive)
            makeMLInference(org = org, botMessage = botMessage, questionerOrgMember = orgMember)
            question
        }.id.value.value

        client.listQuestions(
            feedback = listOf(FeedbackFilterType.negative),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(1)
            assertThat(list.single().id).isEqualTo(messageIdA)
        }

        client.listQuestions(
            feedback = listOf(FeedbackFilterType.positive),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(1)
            assertThat(list.single().id).isEqualTo(messageIdB)
        }

        client.listQuestions(
            feedback = listOf(FeedbackFilterType.negative, FeedbackFilterType.positive),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(2)
            assertThat(list.map { it.id }).containsExactly(messageIdB, messageIdA)
        }
    }

    @Test
    fun `listQuestions -- since`() = suspendingDatabaseTest {
        setup()

        val now = Instant.nowWithMicrosecondPrecision()

        val messageIdA = makeThread(org = org).let { thread ->
            val question = makeMessage(thread = thread, author = teamMember)
            val answer = makeMessage(thread = thread, author = botMember)
            makeMLInference(org = org, botMessage = answer, questionerOrgMember = orgMember, createdAt = now)
            question
        }.id.value.value

        val messageIdB = makeThread(org = org).let { thread ->
            val question = makeMessage(thread = thread, author = teamMember)
            val answer = makeMessage(thread = thread, author = botMember)
            makeMLInference(org = org, botMessage = answer, questionerOrgMember = orgMember, createdAt = now.minus(60.seconds))
            question
        }.id.value.value

        client.listQuestions(
            since = now.minus(59.seconds).asApiDateTime(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(1)
            assertThat(list.single().id).isEqualTo(messageIdA)
        }

        client.listQuestions(
            since = now.minus(61.seconds).asApiDateTime(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(2)
            assertThat(list.map { it.id }).containsExactly(messageIdA, messageIdB)
        }
    }

    @Test
    fun `listQuestions -- with follow up questions`() = suspendingDatabaseTest {
        setup()

        val thread = makeThread(org = org)

        val question = makeMessage(thread = thread, author = teamMember, content = "question".asMessageBody().toByteArray())
        val answer = makeMessage(thread = thread, author = botMember, content = "answer".asMessageBody().toByteArray())
        makeMLInference(org = org, botMessage = answer, questionerOrgMember = orgMember)

        val followupQuestion = makeMessage(thread = thread, author = teamMember, content = "follow up question".asMessageBody().toByteArray())
        val followupAnswer = makeMessage(thread = thread, author = botMember, content = "follow up answer".asMessageBody().toByteArray())
        makeMLInference(org = org, botMessage = followupAnswer, questionerOrgMember = orgMember)
        makeMessageFeedback(thread = thread, message = followupAnswer, feedbackType = FeedbackType.Negative)

        client.listQuestions {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val list = body<List<Question>>()
            assertThat(list).hasSize(2)
            list.first().also {
                assertThat(it.id).isEqualTo(followupQuestion.id.value.value)
                assertThat(it.feedback).hasSize(1)
                assertThat(it.question.markdown).isEqualTo("follow up question")
                assertThat(it.answer.markdown).isEqualTo("follow up answer")
            }
            list.last().also {
                assertThat(it.id).isEqualTo(question.id.value.value)
                assertThat(it.feedback).isEmpty()
                assertThat(it.question.markdown).isEqualTo("question")
                assertThat(it.answer.markdown).isEqualTo("answer")
            }
        }
    }
}
