package com.nextchaptersoftware.publicapiservice.api

import com.fasterxml.jackson.databind.ObjectMapper
import com.nextchaptersoftware.publicapiservice.test.utils.PublicApiAuthContext
import com.nextchaptersoftware.publicapiservice.test.utils.UnblockedPublicApiClient
import io.ktor.client.call.body
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ApiSpecApiDelegateImplTest {
    private lateinit var publicApiClient: UnblockedPublicApiClient

    private suspend fun setup() {
        publicApiClient = UnblockedPublicApiClient(
            authContext = PublicApiAuthContext.Unauthenticated,
            database = null,
        )
    }

    @Test
    fun getApiSpec() = runTest {
        setup()

        publicApiClient.getApiSpec {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val body = this.body<String>()
            val json = ObjectMapper().readTree(body)

            // This is here to ensure we're not accidentally exposing internal endpoints in the docs.
            //
            // If an operation or path is not ready to be public, add `x-internal: true` to it in the OpenAPI spec.
            //
            // If an operation or path is ready to be released, remove `x-internal: true` and update the assertions here.
            val paths = json.get("paths").fields().asSequence().toList()

            assertThat(paths.map { it.key }).containsExactlyInAnyOrder(
                "/collections",
                "/collections/{collectionId}",
                "/documents",
                "/documents/{documentId}",
            )

            paths.first { it.key == "/collections" }.also {
                assertThat(it.value.fields().asSequence().toList().map { it.key }).containsExactlyInAnyOrder(
                    "get",
                    "post",
                )
            }

            paths.first { it.key == "/collections/{collectionId}" }.also {
                assertThat(it.value.fields().asSequence().toList().map { it.key }).containsExactlyInAnyOrder(
                    "get",
                    "patch",
                    "delete",
                )
            }

            paths.first { it.key == "/documents" }.also {
                assertThat(it.value.fields().asSequence().toList().map { it.key }).containsExactlyInAnyOrder(
                    "get",
                    "put",
                )
            }

            paths.first { it.key == "/documents/{documentId}" }.also {
                assertThat(it.value.fields().asSequence().toList().map { it.key }).containsExactlyInAnyOrder(
                    "delete",
                )
            }
        }
    }
}
