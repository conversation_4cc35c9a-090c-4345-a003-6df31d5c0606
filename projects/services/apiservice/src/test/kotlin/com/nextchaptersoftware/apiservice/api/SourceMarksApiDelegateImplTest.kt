package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.models.Mark
import com.nextchaptersoftware.apiservice.test.utils.ApiAuthContext
import com.nextchaptersoftware.apiservice.test.utils.UnblockedApiClient
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSourceMark
import com.nextchaptersoftware.db.ModelBuilders.makeSourcePoint
import com.nextchaptersoftware.db.common.getDatabase
import com.nextchaptersoftware.db.cursors.OpaqueCursor
import com.nextchaptersoftware.db.cursors.SourceMarkCursor
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.SourceMarkId
import com.nextchaptersoftware.db.models.personId
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.CustomHeaders
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.epoch
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.client.call.body
import io.ktor.http.HttpStatusCode
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.fail
import org.junit.jupiter.api.Test

class SourceMarksApiDelegateImplTest : DatabaseTestsBase() {
    private lateinit var client: UnblockedApiClient
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var repo: RepoDAO

    suspend fun setup() {
        val identity = makeIdentity(person = makePerson())
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        makeMember(scmTeam = scmTeam, identity = identity)
        repo = makeRepo(scmTeam = scmTeam)
        client = UnblockedApiClient(
            database = currentCoroutineContext().getDatabase(),
            authContext = ApiAuthContext.Authenticated(
                identityId = identity.id.value,
                personId = checkNotNull(identity.personId),
                orgIds = setOf(org.idValue),
            ),
        )
    }

    @Test
    fun getRepoSourceMarks() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        val sourceMark = makeSourceMark(scmTeam = scmTeam, repo = repo, modifiedAt = now.plus(10.seconds))
        val sourcePoint = makeSourcePoint(sourceMark = sourceMark, modifiedAt = now.plus(11.seconds), isOriginal = true)

        val deletedSourceMark = makeSourceMark(scmTeam = scmTeam, repo = repo, modifiedAt = now.plus(20.seconds), isDeleted = true).also {
            makeSourcePoint(sourceMark = it, modifiedAt = now.plus(21.seconds), isOriginal = true)
            makeSourcePoint(sourceMark = it, modifiedAt = now.plus(22.seconds))
        }

        val since = OpaqueCursor.toCursor(
            SourceMarkCursor(
                modifiedAt = Instant.nowWithMicrosecondPrecision().minus(10.days),
                markId = SourceMarkId.random(),
            ),
        ).value

        client.getRepoSourceMarks(orgId = scmTeam.orgId, repoId = repo.idValue) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)

            val actual = body<List<Mark>>()
            actual.firstOrNull { it.id == sourceMark.id.value.value }?.also { sm ->
                assertThat(sm.sourcePoints).isNotEmpty
                sm.sourcePoints?.also { points ->
                    assertThat(points.map { it.fileHash }).containsExactly(Hash(sourcePoint.fileHash).asString())
                }
            } ?: fail("expected source mark")

            assertThat(actual.firstOrNull { it.id == deletedSourceMark.id.value.value }).isNull()

            assertThat(headers[CustomHeaders.LAST_MODIFIED]).isEqualTo(
                OpaqueCursor.toCursor(
                    SourceMarkCursor(
                        modifiedAt = now.plus(10.seconds),
                        markId = sourceMark.id.value,
                    ),
                ).value,
            )
        }

        client.getRepoSourceMarks(orgId = scmTeam.orgId, repoId = repo.idValue, ifModifiedSince = since) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)

            val actual = body<List<Mark>>()
            actual.firstOrNull { it.id == sourceMark.id.value.value }?.also { sm ->
                assertThat(sm.sourcePoints).hasSize(1)
                sm.sourcePoints?.also { points ->
                    assertThat(points.map { it.fileHash }).containsExactly(Hash(sourcePoint.fileHash).asString())
                }
            } ?: fail("expected active source mark")

            actual.firstOrNull { it.id == deletedSourceMark.id.value.value }?.also { sm ->
                assertThat(sm.sourcePoints).hasSize(2)
            } ?: fail("expected deleted source mark")

            assertThat(headers[CustomHeaders.LAST_MODIFIED]).isEqualTo(
                OpaqueCursor.toCursor(
                    SourceMarkCursor(
                        modifiedAt = now.plus(20.seconds),
                        markId = deletedSourceMark.id.value,
                    ),
                ).value,
            )
        }
    }

    @Test
    fun `getRepoSourceMarks when empty`() = suspendingDatabaseTest {
        setup()

        client.getRepoSourceMarks(orgId = scmTeam.orgId, repoId = repo.idValue) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)

            val actual = body<List<Mark>>()
            assertThat(actual).isEmpty()

            assertThat(headers[CustomHeaders.LAST_MODIFIED]).isEqualTo(
                OpaqueCursor.toCursor(
                    SourceMarkCursor(
                        modifiedAt = Instant.epoch,
                        markId = null,
                    ),
                ).value,
            )
        }
    }

    @Test
    fun `getRepoSourceMarks does not return deleted or archived marks`() = suspendingDatabaseTest {
        setup()

        makeSourceMark(scmTeam = scmTeam, repo = repo, isDeleted = true).also {
            makeSourcePoint(sourceMark = it, isOriginal = true)
        }

        makeSourceMark(scmTeam = scmTeam, repo = repo, isArchived = true).also {
            makeSourcePoint(sourceMark = it, isOriginal = true)
        }

        client.getRepoSourceMarks(orgId = scmTeam.orgId, repoId = repo.idValue) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)

            val actual = body<List<Mark>>()
            assertThat(actual).isEmpty()

            assertThat(headers[CustomHeaders.LAST_MODIFIED]).isEqualTo(
                OpaqueCursor.toCursor(
                    SourceMarkCursor(
                        modifiedAt = Instant.epoch,
                        markId = null,
                    ),
                ).value,
            )
        }
    }

    @Test
    fun `getRepoSourceMarks when empty with cursor`() = suspendingDatabaseTest {
        setup()

        val since = OpaqueCursor.toCursor(
            SourceMarkCursor(
                modifiedAt = Instant.nowWithMicrosecondPrecision().minus(10.days),
                markId = SourceMarkId.random(),
            ),
        ).value

        client.getRepoSourceMarks(orgId = scmTeam.orgId, repoId = repo.idValue, ifModifiedSince = since) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)

            val actual = body<List<Mark>>()
            assertThat(actual).isEmpty()

            assertThat(headers[CustomHeaders.LAST_MODIFIED]).isEqualTo(since)
        }
    }
}
