package com.nextchaptersoftware.apiservice.rpc

import com.nextchaptersoftware.api.integration.extension.services.PlanConfigurationDelegateInterface
import com.nextchaptersoftware.api.models.BillingInvoice
import com.nextchaptersoftware.api.models.Plan
import com.nextchaptersoftware.api.models.PlanBillingInfo
import com.nextchaptersoftware.api.models.PlanTemplatesResponse
import com.nextchaptersoftware.api.models.StripeClientSecretForCardSetup
import com.nextchaptersoftware.api.models.UpdatePlanRequest
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.rpc.calls.PlanCalls

class PlanConfigurationDelegateViaRpc : PlanConfigurationDelegateInterface {
    override suspend fun getInvoices(orgId: OrgId): List<BillingInvoice> {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.planGetInvoices(
                    params = PlanCalls.PlanGetInvoicesParams(
                        orgId = orgId,
                    ),
                )
            }
    }

    override suspend fun getClientSecretForCardSetup(orgId: OrgId): StripeClientSecretForCardSetup {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.planGetClientSecretForCardSetup(
                    params = PlanCalls.PlanGetClientSecretForCardSetupParams(
                        orgId = orgId,
                    ),
                )
            }
    }

    override suspend fun getPlanBilling(orgId: OrgId): PlanBillingInfo {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.planGetPlanBilling(
                    params = PlanCalls.PlanGetPlanBillingParams(
                        orgId = orgId,
                    ),
                )
            }
    }

    override suspend fun getPlanTemplates(orgId: OrgId): PlanTemplatesResponse {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.planGetPlanTemplates(
                    params = PlanCalls.PlanGetPlanTemplatesParams(
                        orgId = orgId,
                    ),
                )
            }
    }

    override suspend fun getPlan(orgId: OrgId): Plan {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.planGetPlan(
                    params = PlanCalls.PlanGetPlanParams(
                        orgId = orgId,
                    ),
                )
            }
    }

    override suspend fun updatePlan(orgId: OrgId, updatePlanRequest: UpdatePlanRequest, updatingOrgMemberId: OrgMemberId): Plan {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.planUpdatePlan(
                    params = PlanCalls.PlanUpdatePlanParams(
                        orgId = orgId,
                        updatingOrgMemberId = updatingOrgMemberId,
                        body = updatePlanRequest,
                    ),
                )
            }
    }

    override suspend fun extendTrial(orgId: OrgId, extendingOrgMemberId: OrgMemberId): Plan {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.planExtendTrial(
                    params = PlanCalls.PlanExtendTrialParams(
                        orgId = orgId,
                        extendingOrgMemberId = extendingOrgMemberId,
                    ),
                )
            }
    }
}
