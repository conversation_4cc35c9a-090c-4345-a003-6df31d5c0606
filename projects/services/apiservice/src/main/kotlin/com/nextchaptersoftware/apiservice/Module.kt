package com.nextchaptersoftware.apiservice

import com.nextchaptersoftware.access.RestrictedAccessServiceFactory
import com.nextchaptersoftware.activation.ProviderActivation
import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.api.auth.services.client.AsanaAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.ConfluenceAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.GoogleAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.JiraAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.LinearAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.NotionAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.ProviderAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.SlackAuthClientFactory
import com.nextchaptersoftware.api.auth.services.url.login.LoginUrlService
import com.nextchaptersoftware.api.auth.services.url.redirect.ProviderRedirectUrlService
import com.nextchaptersoftware.api.auth.services.url.redirect.RedirectAuthOverrideService
import com.nextchaptersoftware.api.auth.services.url.redirect.ScmRedirectUrlService
import com.nextchaptersoftware.api.integration.connection.factory.StandardConnectionFactory
import com.nextchaptersoftware.api.integration.connection.services.ConnectionService
import com.nextchaptersoftware.api.integration.extension.services.PaymentInviteConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.PlanConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.asana.AsanaConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.coda.CodaConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.confluence.ConfluenceConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.google.GoogleDriveConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.slack.SlackConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.slack.SlackRecommendedChannelsService
import com.nextchaptersoftware.api.integration.extension.services.slack.SlackSearchService
import com.nextchaptersoftware.api.integration.extension.services.slack.SlackService
import com.nextchaptersoftware.api.integration.extension.services.stackoverflow.StackOverflowTeamsService
import com.nextchaptersoftware.api.integration.extension.services.web.WebIngestionConfigurationService
import com.nextchaptersoftware.api.integration.factory.AsanaIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.CIIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.CodaIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.ConfluenceIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.FeatureFlaggedIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.GoogleDriveIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.JiraIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.LinearIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.NonOauthIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.NotionIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.ProviderIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.ScmEnterpriseIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.ScmIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.SlackIntegrationFactory
import com.nextchaptersoftware.api.integration.factory.StackOverflowTeamsIntegrationFactory
import com.nextchaptersoftware.api.integration.installation.factory.AsanaInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.AtlassianInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.CIInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.CustomIntegrationInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.GoogleDriveInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.GoogleDriveInstallationService
import com.nextchaptersoftware.api.integration.installation.factory.GoogleDriveWorkspaceInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.LinearInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.NonOauthInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.NotionInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.ProviderInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.ScmInstallationFactory
import com.nextchaptersoftware.api.integration.installation.factory.SlackInstallationFactory
import com.nextchaptersoftware.api.integration.installation.progress.ScmInstallationProgress
import com.nextchaptersoftware.api.integration.installation.progress.SlackInstallationProgress
import com.nextchaptersoftware.api.integration.installation.services.InstallationProviderService
import com.nextchaptersoftware.api.integration.installation.services.InstallationService
import com.nextchaptersoftware.api.integration.oauth.AsanaUserOauthUrlProvider
import com.nextchaptersoftware.api.integration.oauth.GoogleUserOauthUrlProvider
import com.nextchaptersoftware.api.integration.oauth.LinearUserOauthUrlProvider
import com.nextchaptersoftware.api.integration.oauth.NotionUserOauthUrlProvider
import com.nextchaptersoftware.api.integration.services.IntegrationService
import com.nextchaptersoftware.api.public.key.ApiKeyPersistence
import com.nextchaptersoftware.api.public.key.ApiKeyServiceProvider
import com.nextchaptersoftware.api.services.ArchivedReferenceService
import com.nextchaptersoftware.api.services.DocumentReingestService
import com.nextchaptersoftware.api.services.EmailService
import com.nextchaptersoftware.api.services.PersonEmailPreferencesService
import com.nextchaptersoftware.api.services.PersonService
import com.nextchaptersoftware.api.services.PullRequestService
import com.nextchaptersoftware.api.services.SampleQuestionService
import com.nextchaptersoftware.api.services.SemanticSearchFileReferenceService
import com.nextchaptersoftware.api.services.SessionEventService
import com.nextchaptersoftware.api.services.install.ScmInstallationService
import com.nextchaptersoftware.api.threads.services.ThreadService
import com.nextchaptersoftware.apiservice.plugins.configureClientVersionMetrics
import com.nextchaptersoftware.apiservice.plugins.configureRouting
import com.nextchaptersoftware.apiservice.plugins.configureTracing
import com.nextchaptersoftware.apiservice.rpc.AsanaConfigurationDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.CodaConfigurationDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.ConfluenceConfigurationDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.DataSourcePresetsConfigurationDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.GoogleDriveConfigurationDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.JiraConfigurationDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.PlanConfigurationDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.ScmAppDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.ScmInstallationDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.ScmRepoDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.ScmUserDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.StackOverflowTeamsServiceViaRpc
import com.nextchaptersoftware.atlassian.auth.ConfluenceAuthStateProvider
import com.nextchaptersoftware.atlassian.auth.JiraAuthStateProvider
import com.nextchaptersoftware.auth.ci.CIContextAuthTokenConfigurator
import com.nextchaptersoftware.auth.provider.services.ProviderAuthenticationStateService
import com.nextchaptersoftware.auth.saml.config.SamlConfig
import com.nextchaptersoftware.auth.saml.config.SamlConfigs
import com.nextchaptersoftware.billing.services.PaymentInviteService
import com.nextchaptersoftware.billing.utils.OrgBillingSeatService
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.ci.events.CIProjectEventEnqueueService
import com.nextchaptersoftware.ci.services.CIRepoControlService
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.confluence.enqueue.ConfluenceEventEnqueueService
import com.nextchaptersoftware.data.preset.DataSourcePresetConfigurationServiceImpl
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.embedding.config.EmbeddingSecretConfig
import com.nextchaptersoftware.embedding.encoding.EmbeddingEncoding
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.embedding.service.store.EmbeddingStoreFacade
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.feedback.services.MessageFeedbackService
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insight.index.PullRequestInsightIndexContentService
import com.nextchaptersoftware.insight.index.ThreadInsightIndexContentService
import com.nextchaptersoftware.integration.queue.redis.cache.StandardIngestionProgressServiceProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.maintenance.OrgMaintenance
import com.nextchaptersoftware.maintenance.events.queue.enqueue.MaintenanceEventEnqueueService
import com.nextchaptersoftware.maintenance.installation.ProviderUninstallService
import com.nextchaptersoftware.maintenance.installation.StandardUninstallService
import com.nextchaptersoftware.maintenance.scm.ScmInstallationMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamLifecycleMaintenance
import com.nextchaptersoftware.membership.MemberService
import com.nextchaptersoftware.metrics.MetricsService
import com.nextchaptersoftware.ml.api.delegate.MachineLearningApiProviderDelegate
import com.nextchaptersoftware.ml.embedding.opensearch.store.OpenSearchEmbeddingStore
import com.nextchaptersoftware.ml.embedding.pinecone.store.PineconeEmbeddingStore
import com.nextchaptersoftware.ml.embedding.query.services.StandardEmbeddingQueryService
import com.nextchaptersoftware.ml.embedding.query.services.filter.EmbeddingQueryFilterBuilder
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionDecorator
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionService
import com.nextchaptersoftware.ml.embedding.services.EmbeddingService
import com.nextchaptersoftware.ml.embedding.services.RoundRobinEmbeddingService
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.events.redis.store.InviteRedisStore
import com.nextchaptersoftware.notification.events.services.InviteService
import com.nextchaptersoftware.notification.events.services.OrgDescriptionModelService
import com.nextchaptersoftware.notification.services.InactiveFollowupService
import com.nextchaptersoftware.notification.services.PeerInviteSuggestionService
import com.nextchaptersoftware.opensearch.api.OpenSearchApiConfiguration
import com.nextchaptersoftware.opensearch.api.OpenSearchApiProvider
import com.nextchaptersoftware.opensearch.config.OpenSearchConfig
import com.nextchaptersoftware.opensearch.index.OpenSearchIndexLoader
import com.nextchaptersoftware.opensearch.plugins.configureOpenSearch
import com.nextchaptersoftware.pinecone.api.PineconeApiConfiguration
import com.nextchaptersoftware.pinecone.api.PineconeApiProvider
import com.nextchaptersoftware.pinecone.api.PineconeControlPlaneApiProvider
import com.nextchaptersoftware.pinecone.config.PineconeConfig
import com.nextchaptersoftware.pinecone.index.PineconeIndexLoader
import com.nextchaptersoftware.pinecone.plugins.configurePinecone
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesServiceProvider
import com.nextchaptersoftware.redis.Redis
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.LockType
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.repo.RepoComputeService
import com.nextchaptersoftware.repo.rpc.RepoComputeServiceViaRpc
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.scm.ScmAuthClientFactory
import com.nextchaptersoftware.scm.ScmNoAuthApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.delegates.ScmAppDelegate
import com.nextchaptersoftware.scm.delegates.ScmRepoDelegate
import com.nextchaptersoftware.scm.delegates.ScmUserDelegate
import com.nextchaptersoftware.scm.queue.enqueue.ScmEventProducer
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.indexing.events.queue.enqueue.SearchIndexingEventEnqueueService
import com.nextchaptersoftware.search.semantic.services.SemanticSearchDocumentService
import com.nextchaptersoftware.search.semantic.services.documentation.NoopDocumentationValidationService
import com.nextchaptersoftware.search.semantic.services.file.SemanticSearchFileService
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import com.nextchaptersoftware.search.services.query.PostgresQueryService
import com.nextchaptersoftware.search.services.query.factory.DocumentInsightContentService
import com.nextchaptersoftware.search.services.query.factory.SearchDecorator
import com.nextchaptersoftware.search.services.query.filter.DocumentSearchResultDecisionServiceProvider
import com.nextchaptersoftware.search.services.query.filter.SlackDecisionService
import com.nextchaptersoftware.search.services.query.filter.StandardSearchResultsFilter
import com.nextchaptersoftware.search.services.query.filter.ThreadSearchResultDecisionServiceProvider
import com.nextchaptersoftware.security.HMACAuthenticator
import com.nextchaptersoftware.security.auth.AuthProviderConfigurator
import com.nextchaptersoftware.security.auth.AuthTokenConfigurator
import com.nextchaptersoftware.security.auth.ReadOnlyAuthTokenConfigurator
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.segment.SegmentProvider
import com.nextchaptersoftware.segment.api.SegmentApi
import com.nextchaptersoftware.semantic.bot.services.MessageMentionService
import com.nextchaptersoftware.semantic.bot.services.MessageService
import com.nextchaptersoftware.semantic.bot.services.NotifyBotService
import com.nextchaptersoftware.semantic.bot.services.ThreadParticipantService
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureCallId
import com.nextchaptersoftware.service.plugins.configureCompression
import com.nextchaptersoftware.service.plugins.configureCors
import com.nextchaptersoftware.service.plugins.configureDefaultHeaders
import com.nextchaptersoftware.service.plugins.configureForwardProxySupport
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSecurity
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.service.plugins.configureStatusPages
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.PendingSlackQuestionService
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.SlackBotEventEnqueueService
import com.nextchaptersoftware.slack.config.SlackConfigProvider
import com.nextchaptersoftware.slack.events.queue.enqueue.SlackEventEnqueueService
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.slack.services.SlackChannelAccessService
import com.nextchaptersoftware.slack.services.SlackChannelFilterService
import com.nextchaptersoftware.slack.services.SlackChannelPatternPreferencesService
import com.nextchaptersoftware.slack.services.SlackChannelPreferencesService
import com.nextchaptersoftware.version.VersionService
import com.nextchaptersoftware.web.events.queue.enqueue.WebEventEnqueueService
import com.nextchaptersoftware.web.ingestion.WebIngestionService
import com.nextchaptersoftware.web.ingestion.WebIngestionUrlValidationService
import com.nextchaptersoftware.web.ingestion.config.UrlValidationConfig
import com.nextchaptersoftware.web.ingestion.queue.enqueue.WebIngestionEventEnqueueService
import io.ktor.server.application.Application

@Suppress(
    "LongMethod",
    "CyclomaticComplexMethod",
)
fun Application.module(
    serviceLifecycle: ServiceLifecycle = ServiceLifecycle(healthCheckers = emptyList()),
    config: GlobalConfig = GlobalConfig.INSTANCE,
    openSearchConfig: OpenSearchConfig = OpenSearchConfig.INSTANCE,
    pineconeConfig: PineconeConfig = PineconeConfig.INSTANCE,
    scmConfig: ScmConfig = ScmConfig.INSTANCE,
    samlConfig: SamlConfig = SamlConfigs.INSTANCE.saml,
    overrideApiKeyPersistence: ApiKeyPersistence? = null,
    overridePlanCapabilityService: PlanCapabilitiesService? = null,
    overridePullRequestService: PullRequestService? = null,
    overrideSlackServiceImpl: SlackService? = null,
    overridePostgresQueryService: PostgresQueryService? = null,
    overrideMetricsService: MetricsService? = null,
    overrideSlackNotifier: SlackNotifier? = null,
    overrideScmInstallService: ScmInstallationService? = null,
    overrideEmbeddingService: EmbeddingService? = null,
    overrideRepoComputeService: RepoComputeService? = null,
    overrideScmAppDelegate: ScmAppDelegate? = null,
    overrideScmRepoDelegate: ScmRepoDelegate? = null,
    overrideScmUserDelegate: ScmUserDelegate? = null,
) {
    val openSearchApiProvider by lazy {
        OpenSearchApiProvider(
            config = OpenSearchApiConfiguration(
                baseApiUri = config.openSearch.baseApiUri.asUrl,
                timeout = config.openSearch.defaultTimeout,
                userName = config.openSearch.userName,
                password = config.openSearch.password,
            ),
        )
    }

    val openSearchEmbeddingStore by lazy {
        OpenSearchEmbeddingStore(
            indexName = openSearchConfig.openSearchIndex.indexName,
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchIndexLoader by lazy {
        OpenSearchIndexLoader(
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchLockProvider by lazy {
        LockProvider(type = LockType.OpenSearchLoader)
    }

    val pineconeApiProvider by lazy {
        PineconeApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeControlPlaneApiProvider by lazy {
        PineconeControlPlaneApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeEmbeddingStore by lazy {
        PineconeEmbeddingStore(pineconeApiProvider = pineconeApiProvider)
    }

    val pineconeIndexLoader by lazy {
        PineconeIndexLoader(
            pineconeControlPlaneApiProvider = pineconeControlPlaneApiProvider,
        )
    }

    val pineconeLockProvider by lazy {
        LockProvider(type = LockType.PineconeLoader)
    }

    val jwt by lazy {
        Jwt(authenticationConfig = config.authentication)
    }

    val urlBuilderProvider by lazy {
        StandardUrlBuilderProvider(config = config)
    }

    val botAccountService by lazy {
        InstallationBotAccountService(
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val maintenanceEventEnqueueService by lazy {
        MaintenanceEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.maintenanceEventsQueueName,
                ),
            ),
        )
    }

    val notificationEventEnqueueService by lazy {
        NotificationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.notificationEventsQueueName,
                ),
            ),
        )
    }

    val clientConfigService by lazy {
        ClientConfigService()
    }

    val threadParticipantService by lazy {
        ThreadParticipantService()
    }

    val providerAuthClientFactory by lazy {
        ProviderAuthClientFactory(
            asanaAuthClientFactory = AsanaAuthClientFactory(),
            slackAuthClientFactory = SlackAuthClientFactory(config = config),
            jiraAuthClientFactory = JiraAuthClientFactory(config = config),
            confluenceAuthClientFactory = ConfluenceAuthClientFactory(config = config),
            linearAuthClientFactory = LinearAuthClientFactory(config = config),
            notionAuthClientFactory = NotionAuthClientFactory(config = config),
            googleAuthClientFactory = GoogleAuthClientFactory(config = config),
        )
    }

    val providerRedirectUrlService by lazy {
        ProviderRedirectUrlService(
            providerAuthClientFactory = providerAuthClientFactory,
        )
    }

    val personEmailPreferencesService by lazy {
        PersonEmailPreferencesService()
    }

    val prCommentEventEnqueueService by lazy {
        StandardEventEnqueueService(
            messageProducer = ActiveMQProducer.producer(
                queueName = config.queue.unblockedPRCommentQueueName,
            ),
        )
    }

    val slackEventEnqueueService by lazy {
        SlackEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.slackEventsQueueName,
                ),
            ),
        )
    }

    val slackBotEventEnqueueService by lazy {
        SlackBotEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.slackBotEventsQueueName,
                ),
            ),
        )
    }

    val searchPriorityEventEnqueueService by lazy {
        SearchPriorityEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchPriorityEventsQueueName,
                ),
            ),
        )
    }

    val searchIndexingEventEnqueueService by lazy {
        SearchIndexingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchIndexingEventsQueueName,
                ),
            ),
        )
    }

    val embeddingEventEnqueueService by lazy {
        EmbeddingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.embeddingEventsQueueName,
                ),
            ),
        )
    }

    val indexingAndEmbeddingService by lazy {
        IndexingAndEmbeddingService(
            searchIndexingEventEnqueueService = searchIndexingEventEnqueueService,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val messageService by lazy {
        MessageService(
            prCommentEventEnqueueService = prCommentEventEnqueueService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            botAccountService = botAccountService,
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val segmentApi by lazy {
        SegmentApi()
    }

    val segmentProvider by lazy {
        SegmentProvider(segmentApi)
    }

    val orgBillingSeatService by lazy {
        OrgBillingSeatService(notificationEventEnqueueService = notificationEventEnqueueService)
    }

    val planCapabilitiesService by lazy {
        overridePlanCapabilityService ?: PlanCapabilitiesServiceProvider(config = config.billing).get()
    }

    val dataSourcePresetConfigurationService by lazy {
        DataSourcePresetConfigurationServiceImpl(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val notifyBotService by lazy {
        NotifyBotService(
            messageService = messageService,
            searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
            participantService = threadParticipantService,
            segmentProvider = segmentProvider,
            orgBillingSeatService = orgBillingSeatService,
            urlBuilderProvider = urlBuilderProvider,
            dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
        )
    }

    val apiKeyPersistence by lazy {
        overrideApiKeyPersistence
            ?: ApiKeyPersistence(store = Stores.orgApiKeyStore)
    }
    val apiKeyServiceProvider by lazy {
        ApiKeyServiceProvider(
            persistence = apiKeyPersistence,
        )
    }

    val insiderService by lazy {
        InsiderService()
    }

    val slackNotifier by lazy {
        overrideSlackNotifier ?: SlackNotifier(
            internalSlackConfig = config.internalSlack,
            adminWebConfig = config.adminWeb,
            insiderService = insiderService,
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val scmEventProducer by lazy {
        ScmEventProducer(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.scmEventsQueueName,
                ),
            ),
        )
    }

    val peerInviteSuggestionService by lazy {
        PeerInviteSuggestionService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val inactiveFollowupService by lazy {
        InactiveFollowupService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val metricsService by lazy {
        overrideMetricsService ?: MetricsService(
            peerInviteSuggestionService = peerInviteSuggestionService,
            inactiveFollowupService = inactiveFollowupService,
        )
    }

    val scmWebFactory by lazy {
        ScmWebFactory(
            scmConfig = scmConfig,
        )
    }

    val repoComputeService by lazy {
        overrideRepoComputeService
            ?: RepoComputeServiceViaRpc(
                withRpc = {
                    RpcFacade.withProxyProvider().forApiService()
                },
            )
    }

    val repoAccessService by lazy {
        RepoAccessService(
            repoComputeService = repoComputeService,
        )
    }

    val threadService by lazy {
        ThreadService(
            messageService = messageService,
            threadParticipantService = threadParticipantService,
            metricsService = metricsService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            botAccountService = botAccountService,
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val pullRequestService by lazy {
        overridePullRequestService ?: PullRequestService(
            prCommentEventEnqueueService = prCommentEventEnqueueService,
            urlBuilderProvider = urlBuilderProvider,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val memberService by lazy {
        MemberService()
    }

    val emailService by lazy {
        EmailService(
            notificationEventEnqueueService = notificationEventEnqueueService,
            personEmailPreferencesService = personEmailPreferencesService,
            memberService = memberService,
        )
    }

    val loginUrlService by lazy {
        LoginUrlService(
            deploymentConfig = config.service,
        )
    }

    val personService by lazy {
        PersonService()
    }
    val messageMentionService by lazy {
        MessageMentionService()
    }

    val inferenceService by lazy {
        MLInferenceService()
    }

    val messageFeedbackService by lazy {
        MessageFeedbackService(
            inferenceService = inferenceService,
        )
    }

    val versionService by lazy {
        VersionService(
            versionConfig = config.versioning,
        )
    }

    val providerAuthenticationStateService by lazy {
        ProviderAuthenticationStateService(
            jwt = jwt,
            expiry = config.providers.exchangeTokenExpiry,
        )
    }

    val orgDescriptionModelService by lazy {
        OrgDescriptionModelService()
    }

    val inviteRedisStore by lazy {
        InviteRedisStore(redisApi = Redis.API)
    }

    val inviteService by lazy {
        InviteService(
            orgDescriptionModelService = orgDescriptionModelService,
            inviteRedisStore = inviteRedisStore,
        )
    }

    val intercomHMACAuthenticator by lazy {
        HMACAuthenticator(
            authenticationSecret = config.intercom.identityVerificationSecret,
        )
    }

    val slackChannelAccessService by lazy {
        SlackChannelAccessService(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val slackChannelFilterService by lazy {
        SlackChannelFilterService(
            clientConfigService = clientConfigService,
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val slackChannelPreferencesService by lazy {
        SlackChannelPreferencesService(
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val slackChannelPatternPreferencesService by lazy {
        SlackChannelPatternPreferencesService()
    }

    val slackSearchService by lazy {
        SlackSearchService(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val slackRecommendedChannelsService by lazy {
        SlackRecommendedChannelsService(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val slackConfigurationService by lazy {
        SlackConfigurationService(
            slackEventEnqueueService = slackEventEnqueueService,
            slackBotEventEnqueueService = slackBotEventEnqueueService,
            slackChannelFilterService = slackChannelFilterService,
            slackChannelPreferencesService = slackChannelPreferencesService,
            slackChannelPatternPreferencesService = slackChannelPatternPreferencesService,
        )
    }

    val slackService by lazy {
        overrideSlackServiceImpl ?: SlackService(
            slackConfigurationService = slackConfigurationService,
            slackSearchService = slackSearchService,
            slackRecommendedChannelsService = slackRecommendedChannelsService,
        )
    }

    val scmNoAuthApiFactory by lazy {
        ScmNoAuthApiFactory(
            scmConfig = scmConfig,
        )
    }

    val slackConfigProvider by lazy {
        SlackConfigProvider()
    }

    val slackIntegrationFactory by lazy {
        SlackIntegrationFactory(
            slackConfigProvider = slackConfigProvider,
            providerAuthenticationStateService = providerAuthenticationStateService,
            providerRedirectUrlService = providerRedirectUrlService,
        )
    }

    val jiraIntegrationFactory by lazy {
        JiraIntegrationFactory(
            providerRedirectUrlService = providerRedirectUrlService,
        )
    }

    val confluenceIntegrationFactory by lazy {
        ConfluenceIntegrationFactory(
            providerRedirectUrlService = providerRedirectUrlService,
        )
    }

    val stackOverflowTeamsIntegrationFactory by lazy {
        StackOverflowTeamsIntegrationFactory()
    }

    val codaIntegrationFactory by lazy {
        CodaIntegrationFactory()
    }

    val nonOauthIntegrationFactory by lazy {
        NonOauthIntegrationFactory()
    }

    val asanaUserOauthUrlProvider by lazy {
        AsanaUserOauthUrlProvider(
            providerRedirectUrlService = providerRedirectUrlService,
            config = config.providers.asana ?: return@lazy null,
        )
    }

    val linearUserOauthUrlProvider by lazy {
        config.providers.linear?.let {
            LinearUserOauthUrlProvider(
                providerRedirectUrlService = providerRedirectUrlService,
                config = it,
            )
        }
    }

    val linearIntegrationFactory by lazy {
        linearUserOauthUrlProvider?.let {
            LinearIntegrationFactory(
                userOauthUrlProvider = it,
            )
        }
    }

    val notionOauthUrlProvider by lazy {
        NotionUserOauthUrlProvider(
            providerRedirectUrlService = providerRedirectUrlService,
            config = config.providers.notion ?: return@lazy null,
        )
    }

    val notionIntegrationFactory by lazy {
        NotionIntegrationFactory(
            userOauthUrlProvider = notionOauthUrlProvider ?: return@lazy null,
        )
    }

    val googleUserOauthUrlProvider by lazy {
        GoogleUserOauthUrlProvider(
            providerRedirectUrlService = providerRedirectUrlService,
            config = config.providers.googleDrive ?: return@lazy null,
        )
    }

    val googleDriveIntegrationFactory by lazy {
        GoogleDriveIntegrationFactory(
            clientConfigService = clientConfigService,
            userOauthUrlProvider = googleUserOauthUrlProvider ?: return@lazy null,
            showDataSharingWarning = true, // TODO disable for on-prem
        )
    }

    val googleDriveWorkspaceIntegrationFactory by lazy {
        FeatureFlaggedIntegrationFactory(
            integrationFactory = nonOauthIntegrationFactory,
            clientConfigService = clientConfigService,
            type = ClientCapabilityType.FeatureGoogleDriveWorkspace,
        )
    }

    val connectionService by lazy {
        ConnectionService(
            standardConnectionFactory = StandardConnectionFactory(),
        )
    }

    val asanaInstallationFactory by lazy {
        AsanaInstallationFactory(
            connectionService = connectionService,
            asanaUserOauthUrlProvider = asanaUserOauthUrlProvider ?: return@lazy null,
        )
    }

    val slackInstallationProgress by lazy {
        SlackInstallationProgress(
            slackChannelFilterService = slackChannelFilterService,
            featureEnabled = config.featureFlags.enableSlackInstallProgress,
        )
    }

    val slackInstallationFactory by lazy {
        SlackInstallationFactory(
            providerRedirectUrlService = providerRedirectUrlService,
            connectionService = connectionService,
            slackConfigProvider = slackConfigProvider,
            slackInstallationProgress = slackInstallationProgress,
        )
    }

    val jiraInstallationFactory by lazy {
        AtlassianInstallationFactory(
            providerRedirectUrlService = providerRedirectUrlService,
            connectionService = connectionService,
            atlassianAuthStateProvider = JiraAuthStateProvider(),
        )
    }

    val confluenceInstallationFactory by lazy {
        AtlassianInstallationFactory(
            providerRedirectUrlService = providerRedirectUrlService,
            connectionService = connectionService,
            atlassianAuthStateProvider = ConfluenceAuthStateProvider(),
        )
    }

    val nonOauthInstallationFactory by lazy {
        NonOauthInstallationFactory(
            connectionService = connectionService,
        )
    }

    val linearInstallationFactory by lazy {
        linearUserOauthUrlProvider?.let {
            LinearInstallationFactory(
                userOauthUrlProvider = it,
                connectionService = connectionService,
            )
        }
    }

    val notionInstallationFactory by lazy {
        NotionInstallationFactory(
            userOauthUrlProvider = notionOauthUrlProvider ?: return@lazy null,
            connectionService = connectionService,
        )
    }

    val googleDriveInstallationService by lazy {
        GoogleDriveInstallationService(
            connectionService = connectionService,
        )
    }

    val googleDriveInstallationFactory by lazy {
        GoogleDriveInstallationFactory(
            userOauthUrlProvider = googleUserOauthUrlProvider ?: return@lazy null,
            googleDriveInstallationService = googleDriveInstallationService,
        )
    }

    val googleDriveWorkspaceInstallationFactory by lazy {
        GoogleDriveWorkspaceInstallationFactory(
            googleDriveInstallationService = googleDriveInstallationService,
        )
    }

    val scmInstallationFactory by lazy {
        ScmInstallationFactory(
            loginUrlService = loginUrlService,
            connectionService = connectionService,
            scmInstallationProgress = ScmInstallationProgress(
                repoAccessService = repoAccessService,
            ),
        )
    }

    val customIntegrationInstallationFactory by lazy {
        CustomIntegrationInstallationFactory()
    }

    val orgMaintenance by lazy {
        OrgMaintenance()
    }

    val scmInstallationMaintenance by lazy {
        ScmInstallationMaintenance()
    }

    val scmAppDelegate by lazy {
        overrideScmAppDelegate
            ?: ScmAppDelegateViaRpc()
    }

    val scmTeamLifecycleMaintenance by lazy {
        ScmTeamLifecycleMaintenance(
            insiderService = insiderService,
            restrictedAccessService = RestrictedAccessServiceFactory.fromConfig(),
            scmAppDelegate = scmAppDelegate,
            orgMaintenance = orgMaintenance,
            scmInstallationMaintenance = scmInstallationMaintenance,
        )
    }

    val providerUninstallService by lazy {
        ProviderUninstallService(
            scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
        )
    }

    val uninstallService by lazy {
        StandardUninstallService(
            providerUninstallService = providerUninstallService,
            slackNotifier = slackNotifier,
        )
    }

    val ciRepoControlService by lazy {
        CIRepoControlService()
    }

    val ciInstallationFactory by lazy {
        CIInstallationFactory(
            ciRepoControlService = ciRepoControlService,
        )
    }

    val installationFactory by lazy {
        ProviderInstallationFactory(
            asanaInstallationFactory = asanaInstallationFactory,
            slackInstallationFactory = slackInstallationFactory,
            jiraInstallationFactory = jiraInstallationFactory,
            confluenceInstallationFactory = confluenceInstallationFactory,
            nonOauthInstallationFactory = nonOauthInstallationFactory,
            linearInstallationFactory = linearInstallationFactory,
            notionInstallationFactory = notionInstallationFactory,
            googleDriveInstallationFactory = googleDriveInstallationFactory,
            googleDriveWorkspaceInstallationFactory = googleDriveWorkspaceInstallationFactory,
            customIntegrationInstallationFactory = customIntegrationInstallationFactory,
            scmInstallationFactory = scmInstallationFactory,
            ciInstallationFactory = ciInstallationFactory,
        )
    }

    val installationProviderService by lazy {
        InstallationProviderService(
            installationStore = Stores.installationStore,
            installationFactory = installationFactory,
        )
    }

    val providerActivation by lazy {
        ProviderActivation(
            config = config,
            scmConfig = scmConfig,
        )
    }

    val installationService by lazy {
        InstallationService(
            installationProviderService = installationProviderService,
            providerActivation = providerActivation,
        )
    }

    val scmInstallService by lazy {
        overrideScmInstallService ?: ScmInstallationService(
            scmInstallationDelegate = ScmInstallationDelegateViaRpc(),
            scmNoAuthApiFactory = scmNoAuthApiFactory,
            scmWebFactory = scmWebFactory,
        )
    }

    val scmAuthClientFactory by lazy {
        ScmAuthClientFactory(
            scmConfig = scmConfig,
        )
    }

    val redirectAuthOverrideService by lazy {
        RedirectAuthOverrideService(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
        )
    }

    val redirectUrlService by lazy {
        ScmRedirectUrlService(
            providerActivation = providerActivation,
            redirectAuthOverrideService = redirectAuthOverrideService,
            scmAuthClientFactory = scmAuthClientFactory,
        )
    }

    val scmIntegrationFactory by lazy {
        ScmIntegrationFactory(
            redirectUrlService = redirectUrlService,
        )
    }

    val scmEnterpriseIntegrationFactory by lazy {
        ScmEnterpriseIntegrationFactory()
    }

    val ciIntegrationFactory by lazy {
        CIIntegrationFactory(
            clientConfigService = clientConfigService,
        )
    }

    val asanaIntegrationFactory by lazy {
        AsanaIntegrationFactory(
            clientConfigService = clientConfigService,
            userOauthUrlProvider = asanaUserOauthUrlProvider ?: return@lazy null,
        )
    }

    val integrationService by lazy {
        IntegrationService(
            integrationFactory = ProviderIntegrationFactory(
                asanaIntegrationFactory = asanaIntegrationFactory,
                ciIntegrationFactory = ciIntegrationFactory,
                codaIntegrationFactory = codaIntegrationFactory,
                confluenceIntegrationFactory = confluenceIntegrationFactory,
                googleDriveIntegrationFactory = googleDriveIntegrationFactory,
                googleDriveWorkspaceIntegrationFactory = googleDriveWorkspaceIntegrationFactory,
                jiraIntegrationFactory = jiraIntegrationFactory,
                linearIntegrationFactory = linearIntegrationFactory,
                nonOauthIntegrationFactory = nonOauthIntegrationFactory,
                notionIntegrationFactory = notionIntegrationFactory,
                scmEnterpriseIntegrationFactory = scmEnterpriseIntegrationFactory,
                scmIntegrationFactory = scmIntegrationFactory,
                slackIntegrationFactory = slackIntegrationFactory,
                stackOverflowTeamsIntegrationFactory = stackOverflowTeamsIntegrationFactory,
            ),
            providerActivation = providerActivation,
        )
    }

    val slackDecisionService by lazy {
        SlackDecisionService(slackChannelAccessService = slackChannelAccessService)
    }

    val threadSearchResultDecisionProvider by lazy {
        ThreadSearchResultDecisionServiceProvider(slackDecisionService = slackDecisionService)
    }

    val documentDecisionServiceProvider by lazy {
        DocumentSearchResultDecisionServiceProvider()
    }

    val searchResultsFilter by lazy {
        StandardSearchResultsFilter(
            threadSearchResultDecisionProvider = threadSearchResultDecisionProvider,
            documentDecisionServiceProvider = documentDecisionServiceProvider,
        )
    }

    val inferenceTemplateService by lazy {
        MLInferenceTemplateService()
    }

    val threadInsightIndexContentService by lazy {
        ThreadInsightIndexContentService(
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val pullRequestInsightIndexContentService by lazy {
        PullRequestInsightIndexContentService()
    }

    val documentInsightContentService by lazy {
        DocumentInsightContentService()
    }

    val searchDecorator by lazy {
        SearchDecorator(
            botAccountService = botAccountService,
            threadInsightIndexContentService = threadInsightIndexContentService,
            prInsightIndexContentService = pullRequestInsightIndexContentService,
            documentInsightContentService = documentInsightContentService,
            searchResultsFilter = searchResultsFilter,
        )
    }

    val postgresQueryService by lazy {
        overridePostgresQueryService ?: PostgresQueryService(
            searchDecorator = searchDecorator,
        )
    }

    val sampleQuestionService by lazy {
        SampleQuestionService(
            searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
            threadService = threadService,
            notifyBotService = notifyBotService,
            botAccountService = botAccountService,
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val stackOverflowTeamsService by lazy {
        StackOverflowTeamsService(
            stackOverflowTeamsDelegate = StackOverflowTeamsServiceViaRpc(),
        )
    }

    val googleDriveConfigurationService by lazy {
        GoogleDriveConfigurationService(
            googleDriveConfigurationDelegate = GoogleDriveConfigurationDelegateViaRpc(),
        )
    }

    val webEventEnqueueService by lazy {
        WebEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.webEventsQueueName,
                ),
            ),
            progressServiceProvider = StandardIngestionProgressServiceProvider(),
        )
    }

    val webIngestionEventEnqueueService by lazy {
        WebIngestionEventEnqueueService(
            webEventEnqueueService = webEventEnqueueService,
        )
    }

    val webIngestionService by lazy {
        WebIngestionService(
            webIngestionEventEnqueueService = webIngestionEventEnqueueService,
            webIngestionConfig = config.webIngestionConfig,
        )
    }

    val webIngestionConfigurationService by lazy {
        WebIngestionConfigurationService(
            webIngestionService = webIngestionService,
            slackNotifier = slackNotifier,
            segmentProvider = segmentProvider,
        )
    }

    val webIngestionUrlValidationService by lazy {
        WebIngestionUrlValidationService(
            config = UrlValidationConfig.INSTANCE,
        )
    }

    val machineLearningApiProviders by MachineLearningApiProviderDelegate(
        machineLearningConfig = config.machineLearning,
    )

    val embeddingService by lazy {
        overrideEmbeddingService ?: RoundRobinEmbeddingService(
            embeddingServices = machineLearningApiProviders.map { machineLearningApiProvider ->
                EmbeddingService(
                    machineLearningApiProvider = machineLearningApiProvider,
                )
            },
        )
    }

    val reciprocalRankFusionService by lazy {
        ReciprocalRankFusionService()
    }

    val reciprocalRankFusionDecorator by lazy {
        ReciprocalRankFusionDecorator(
            reciprocalRankFusionService = reciprocalRankFusionService,
        )
    }

    val embeddingSecretConfig by lazy {
        EmbeddingSecretConfig.INSTANCE
    }

    val embeddingEncoding by lazy {
        EmbeddingEncoding(
            embeddingContentConfig = embeddingSecretConfig.embedding,
        )
    }

    val embeddingStoreFacade by lazy {
        EmbeddingStoreFacade(
            openSearchEmbeddingStore = openSearchEmbeddingStore,
            pineconeEmbeddingStore = pineconeEmbeddingStore,
        )
    }

    val embeddingQueryFilterBuilder by lazy {
        EmbeddingQueryFilterBuilder()
    }

    val standardEmbeddingQueryService by lazy {
        StandardEmbeddingQueryService(
            embeddingStoreFacade = embeddingStoreFacade,
            embedder = embeddingService,
            embeddingEncoding = embeddingEncoding,
            embeddingQueryFilterBuilder = embeddingQueryFilterBuilder,
            reciprocalRankFusionDecorator = reciprocalRankFusionDecorator,
            searchDecorator = searchDecorator,
        )
    }

    val documentationValidationService by lazy {
        NoopDocumentationValidationService()
    }

    val semanticSearchDocumentService by lazy {
        SemanticSearchDocumentService(
            embeddingQueryService = standardEmbeddingQueryService,
            documentationValidationService = documentationValidationService,
        )
    }

    val semanticSearchFileService by lazy {
        SemanticSearchFileService(
            semanticSearchDocumentService = semanticSearchDocumentService,
            inferenceTemplateService = inferenceTemplateService,
            reciprocalRankFusionService = reciprocalRankFusionService,
            repoAccessService = repoAccessService,
            embedder = embeddingService,
        )
    }

    val semanticSearchFileReferenceService by lazy {
        SemanticSearchFileReferenceService(
            semanticSearchFileService = semanticSearchFileService,
            scmWebFactory = scmWebFactory,
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val confluenceConfigurationService by lazy {
        ConfluenceConfigurationService(
            confluenceConfigurationDelegate = ConfluenceConfigurationDelegateViaRpc(),
        )
    }

    val dataSourcePresetsConfigurationDelegate by lazy {
        DataSourcePresetsConfigurationDelegateViaRpc()
    }

    val asanaConfigurationService by lazy {
        AsanaConfigurationService(
            asanaConfigurationDelegate = AsanaConfigurationDelegateViaRpc(),
        )
    }

    val jiraConfigurationDelegate by lazy {
        JiraConfigurationDelegateViaRpc()
    }

    val confluenceEventEnqueueService by lazy {
        ConfluenceEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.confluenceEventsQueueName,
                ),
            ),
        )
    }

    val documentReingestService by lazy {
        DocumentReingestService(
            confluenceEventEnqueueService = confluenceEventEnqueueService,
        )
    }

    val archivedReferenceService by lazy {
        ArchivedReferenceService(
            urlBuilderProvider = urlBuilderProvider,
            scmWebFactory = scmWebFactory,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
            documentReingestService = documentReingestService,
        )
    }

    val sessionEventService by lazy {
        SessionEventService()
    }

    val scmRepoDelegate by lazy {
        overrideScmRepoDelegate
            ?: ScmRepoDelegateViaRpc()
    }

    val scmUserDelegate by lazy {
        overrideScmUserDelegate
            ?: ScmUserDelegateViaRpc()
    }

    val planConfigurationDelegate by lazy {
        PlanConfigurationDelegateViaRpc()
    }

    val planConfigurationService by lazy {
        PlanConfigurationService(
            planConfigurationDelegate = planConfigurationDelegate,
        )
    }

    val paymentInviteService by lazy {
        PaymentInviteService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val paymentInviteConfigurationService by lazy {
        PaymentInviteConfigurationService(
            planConfigurationDelegate = planConfigurationDelegate,
            paymentInviteService = paymentInviteService,
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val codaConfigurationService by lazy {
        CodaConfigurationService(
            codaConfigurationDelegate = CodaConfigurationDelegateViaRpc(),
        )
    }

    val ciConfig by lazy {
        CIConfig.INSTANCE
    }

    val configurators: List<AuthProviderConfigurator> = listOf(
        AuthTokenConfigurator(jwt),
        CIContextAuthTokenConfigurator(jwt = jwt, ciConfig = ciConfig),
        ReadOnlyAuthTokenConfigurator(jwt),
    )

    val pendingSlackQuestionService by lazy {
        PendingSlackQuestionService(
            slackBotEventEnqueueService = slackBotEventEnqueueService,
        )
    }

    val ciProjectEventEnqueueService by lazy {
        CIProjectEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.ciProjectEventsQueueName,
                ),
            ),
        )
    }

    configureCallId()
    configureSecurity(
        configurators = configurators,
    )
    configureForwardProxySupport()
    configureDefaultHeaders()
    configureRouting(
        apiKeyServiceProvider = apiKeyServiceProvider,
        asanaConfigurationService = asanaConfigurationService,
        archivedReferenceService = archivedReferenceService,
        botAccountService = botAccountService,
        ciProjectEventEnqueueService = ciProjectEventEnqueueService,
        codaConfigurationService = codaConfigurationService,
        confluenceConfigurationService = confluenceConfigurationService,
        dataSourcePresetsConfigurationDelegate = dataSourcePresetsConfigurationDelegate,
        emailService = emailService,
        embeddingStoreFacade = embeddingStoreFacade,
        googleDriveConfigurationService = googleDriveConfigurationService,
        inferenceService = inferenceService,
        installationService = installationService,
        integrationService = integrationService,
        intercomHMACAuthenticator = intercomHMACAuthenticator,
        inviteService = inviteService,
        jiraConfigurationDelegate = jiraConfigurationDelegate,
        loginUrlService = loginUrlService,
        maintenanceEventEnqueueService = maintenanceEventEnqueueService,
        messageFeedbackService = messageFeedbackService,
        messageMentionService = messageMentionService,
        messageService = messageService,
        metricsService = metricsService,
        notificationEventEnqueueService = notificationEventEnqueueService,
        notifyBotService = notifyBotService,
        paymentInviteConfigurationService = paymentInviteConfigurationService,
        pendingSlackQuestionService = pendingSlackQuestionService,
        personEmailPreferencesService = personEmailPreferencesService,
        personService = personService,
        planCapabilitiesService = planCapabilitiesService,
        planConfigurationService = planConfigurationService,
        postgresQueryService = postgresQueryService,
        pullRequestService = pullRequestService,
        repoAccessService = repoAccessService,
        samlConfig = samlConfig,
        sampleQuestionService = sampleQuestionService,
        scmEventProducer = scmEventProducer,
        scmInstallService = scmInstallService,
        scmRepoDelegate = scmRepoDelegate,
        scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
        scmUserDelegate = scmUserDelegate,
        scmWebFactory = scmWebFactory,
        searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
        segmentProvider = segmentProvider,
        semanticSearchFileReferenceService = semanticSearchFileReferenceService,
        serviceLifecycle = serviceLifecycle,
        sessionEventService = sessionEventService,
        slackNotifier = slackNotifier,
        slackService = slackService,
        stackOverflowTeamsService = stackOverflowTeamsService,
        threadService = threadService,
        uninstallService = uninstallService,
        urlBuilderProvider = urlBuilderProvider,
        versionService = versionService,
        versionConfig = config.versioning,
        webIngestionConfigurationService = webIngestionConfigurationService,
        webIngestionUrlValidationService = webIngestionUrlValidationService,
    )
    configureCors(config.cors)
    configureMonitoring(insiderService = insiderService)
    configureSerialization()
    configureCompression()
    configureStatusPages()
    configureTracing()
    configureClientVersionMetrics()
    configureJvmMetrics()
    configureOpenSearch(
        lockProvider = openSearchLockProvider,
        config = config,
        openSearchIndexLoader = openSearchIndexLoader,
        openSearchConfig = openSearchConfig,
    )
    configurePinecone(
        lockProvider = pineconeLockProvider,
        pineconeConfig = pineconeConfig,
        pineconeIndexLoader = pineconeIndexLoader,
    )
}
