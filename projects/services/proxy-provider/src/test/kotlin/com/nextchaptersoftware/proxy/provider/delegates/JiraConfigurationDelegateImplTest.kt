package com.nextchaptersoftware.proxy.provider.delegates

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeIngestion
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeJiraProject
import com.nextchaptersoftware.db.ModelBuilders.makeJiraSite
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.EmbeddingDeleteDAO
import com.nextchaptersoftware.db.models.EmbeddingDeleteModel
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IngestionDAO
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.JiraProjectDAO
import com.nextchaptersoftware.db.models.JiraProjectIngestionType
import com.nextchaptersoftware.db.models.JiraProjectModel
import com.nextchaptersoftware.db.models.JiraSiteDAO
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock

class JiraConfigurationDelegateImplTest : DatabaseTestsBase() {
    private val service = JiraConfigurationDelegateImpl(
        slackNotifier = mock(),
        jiraApiProvider = mock(),
        jiraDataCenterApiProvider = mock(),
        jiraEventEnqueueService = mock(),
        jiraMemberModelService = mock(),
        segmentProvider = mock(),
        tokenSecretDecryption = mock(),
    )

    private lateinit var org: OrgDAO
    private lateinit var orgMember: OrgMemberDAO
    private lateinit var installation: InstallationDAO
    private lateinit var ingestion: IngestionDAO
    private lateinit var identity: IdentityDAO
    private lateinit var member: MemberDAO
    private lateinit var jiraSite: JiraSiteDAO
    private lateinit var jiraProjectA: JiraProjectDAO
    private lateinit var jiraProjectB: JiraProjectDAO
    private lateinit var jiraProjectC: JiraProjectDAO

    private val siteId = UUID.randomUUID().toString()
    private val lastSynced = Instant.nowWithMicrosecondPrecision()

    private suspend fun setup() {
        org = makeOrg()
        orgMember = makeOrgMember(org = org)
        installation = makeInstallation(org = org, provider = Provider.Jira, installationExternalId = siteId)
        identity = makeIdentity(provider = Provider.Jira)
        member = makeMember(orgMember = orgMember, identity = identity, installation = installation)
        ingestion = makeIngestion(provider = Provider.Jira, installation = installation, lastSynced = lastSynced)
        jiraSite = makeJiraSite(
            installation = installation,
            siteId = siteId,
            jiraProjectIngestionType = JiraProjectIngestionType.AllProjects,
        )
        jiraProjectA = makeJiraProject(site = jiraSite)
        jiraProjectB = makeJiraProject(site = jiraSite)
        jiraProjectC = makeJiraProject(site = jiraSite)

        Stores.jiraProjectAccessStore.upsert(projectId = jiraProjectA.idValue, identityId = identity.idValue)
        Stores.jiraProjectAccessStore.upsert(projectId = jiraProjectB.idValue, identityId = identity.idValue)
        Stores.jiraProjectAccessStore.upsert(projectId = jiraProjectC.idValue, identityId = identity.idValue)
    }

    @Test
    fun updateJiraConfiguration() = suspendingDatabaseTest {
        setup()

        service.updateJiraConfiguration(
            orgId = org.idValue,
            jiraSiteId = jiraSite.id.value,
            jiraProjectIngestionType = JiraProjectIngestionType.SelectedProjectsOnly,
            jiraProjectIds = listOf(jiraProjectA.id.value),
            orgMemberId = orgMember.idValue,
        )

        suspendedTransaction {
            JiraSiteDAO[jiraSite.id].asDataModel()
        }.also { site ->
            assertThat(site.jiraProjectIngestionType).isEqualTo(JiraProjectIngestionType.SelectedProjectsOnly)
        }

        suspendedTransaction {
            JiraProjectDAO.find { JiraProjectModel.jiraSite eq jiraSite.id }.map { it.asDataModel() }
        }.also { projects ->
            assertThat(projects.first { it.id == jiraProjectA.id.value }.shouldIngest).isTrue
            assertThat(projects.first { it.id == jiraProjectB.id.value }.shouldIngest).isFalse
            assertThat(projects.first { it.id == jiraProjectB.id.value }.shouldIngest).isFalse
        }

        suspendedTransaction {
            IngestionDAO[ingestion.id].asDataModel()
        }.also {
            assertThat(it.lastSynced).isEqualTo(ingestion.lastSynced) // Should not have updated
        }

        suspendedTransaction {
            EmbeddingDeleteDAO.find { EmbeddingDeleteModel.namespaceId eq org.idValue }.map { it.asDataModel() }
        }.also { deletes ->
            assertThat(deletes).hasSize(2)
            assertThat(deletes).allMatch { it.installationId == installation.idValue }
            assertThat(deletes.map { it.groupId }).containsExactlyInAnyOrder(jiraProjectB.id.value.value, jiraProjectC.id.value.value)
        }
    }

    private suspend fun assertEmbeddingDeletes(projectsIds: List<UUID>) {
        val embeddingDeletes = suspendedTransaction {
            EmbeddingDeleteDAO.find { EmbeddingDeleteModel.namespaceId eq org.idValue }.map { it.asDataModel() }
        }
        assertThat(embeddingDeletes).allMatch { it.installationId == installation.idValue }
        when (projectsIds.isEmpty()) {
            true -> assertThat(embeddingDeletes).isEmpty()
            else -> assertThat(embeddingDeletes.map { it.groupId }).containsExactlyInAnyOrderElementsOf(projectsIds)
        }
    }

    @Test
    fun `deleteEmbeddings -- should not create models`() = suspendingDatabaseTest {
        setup()

        service.deleteEmbeddings(
            installation = installation.asDataModel(),
            siteAfterUpdate = jiraSite.asDataModel().copy(jiraProjectIngestionType = JiraProjectIngestionType.AllProjects),
            projectsIngestedBeforeUpdate = listOf(
                jiraProjectA.asDataModel(),
                jiraProjectB.asDataModel(),
                jiraProjectC.asDataModel(),
            ),
        )

        assertEmbeddingDeletes(emptyList())
    }

    @Test
    fun `deleteEmbeddings -- should create models`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            JiraProjectDAO[jiraProjectB.id].shouldIngest = true
        }

        service.deleteEmbeddings(
            installation = installation.asDataModel(),
            siteAfterUpdate = jiraSite.asDataModel().copy(jiraProjectIngestionType = JiraProjectIngestionType.SelectedProjectsOnly),
            projectsIngestedBeforeUpdate = listOf(
                jiraProjectA.asDataModel(),
                jiraProjectB.asDataModel(),
                jiraProjectC.asDataModel(),
            ),
        )

        assertEmbeddingDeletes(listOf(jiraProjectA.id.value.value, jiraProjectC.id.value.value))
    }

    @Test
    fun `deleteEmbeddings -- should only create necessary models`() = suspendingDatabaseTest {
        setup()

        service.deleteEmbeddings(
            installation = installation.asDataModel(),
            siteAfterUpdate = jiraSite.asDataModel().copy(jiraProjectIngestionType = JiraProjectIngestionType.SelectedProjectsOnly),
            projectsIngestedBeforeUpdate = listOf(
                jiraProjectB.asDataModel(),
            ),
        )

        assertEmbeddingDeletes(listOf(jiraProjectB.id.value.value))
    }
}
