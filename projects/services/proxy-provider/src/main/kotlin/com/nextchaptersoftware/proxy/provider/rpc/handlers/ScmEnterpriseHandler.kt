package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.services.enterprise.ScmEnterpriseDelegateInterface
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderParams
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderResult
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateGitHubEnterpriseProviderParams
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateGitHubEnterpriseProviderResult
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateGitLabEnterpriseProviderParams
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateGitLabEnterpriseProviderResult

class ScmEnterpriseHandler(
    private val scmEnterpriseDelegate: ScmEnterpriseDelegateInterface,
) : ScmEnterpriseCalls {

    override suspend fun scmEnterpriseCreateBitbucketDataCenterEnterpriseProvider(
        params: ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderParams,
    ): ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderResult {
        return scmEnterpriseDelegate.createBitbucketDataCenterEnterpriseProvider(
            orgId = params.orgId,
            applicationId = params.applicationId,
            displayName = params.displayName,
            encryptedApplicationSecret = params.encryptedApplicationSecret,
        ).let {
            ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderResult(
                enterpriseProviderOutcome = it,
            )
        }
    }

    override suspend fun scmEnterpriseCreateGitHubEnterpriseProvider(
        params: ScmEnterpriseCreateGitHubEnterpriseProviderParams,
    ): ScmEnterpriseCreateGitHubEnterpriseProviderResult {
        return scmEnterpriseDelegate.createGitHubEnterpriseProvider(
            orgId = params.orgId,
            code = params.code,
            negotiation = params.negotiation,
        ).let {
            ScmEnterpriseCreateGitHubEnterpriseProviderResult(
                enterpriseProvider = it,
            )
        }
    }

    override suspend fun scmEnterpriseCreateGitLabEnterpriseProvider(
        params: ScmEnterpriseCreateGitLabEnterpriseProviderParams,
    ): ScmEnterpriseCreateGitLabEnterpriseProviderResult {
        return scmEnterpriseDelegate.createGitLabEnterpriseProvider(
            orgId = params.orgId,
            applicationId = params.applicationId,
            displayName = params.displayName,
            encryptedApplicationSecret = params.encryptedApplicationSecret,
        ).let {
            ScmEnterpriseCreateGitLabEnterpriseProviderResult(
                enterpriseProviderOutcome = it,
            )
        }
    }
}
