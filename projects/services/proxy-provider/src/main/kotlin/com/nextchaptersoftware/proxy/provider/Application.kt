package com.nextchaptersoftware.proxy.provider

import com.nextchaptersoftware.db.health.PostgresHealthChecker
import com.nextchaptersoftware.redis.health.RedisHealthChecker
import com.nextchaptersoftware.service.bootstrap.ServiceBootstrapBuilder

fun main() {
    ServiceBootstrapBuilder.bootstrap {
        withSslEngineConnector()
        withHealthCheckers(listOf(PostgresHealthChecker(), RedisHealthChecker()))
        withLogs()
        withHoneycomb()
        withDatabase()
    }.startHttpServer { serviceLifecycle ->
        module(serviceLifecycle = serviceLifecycle)
    }
}
