package com.nextchaptersoftware.proxy.provider.delegates

import com.nextchaptersoftware.api.integration.extension.services.confluence.ConfluenceConfigurationDelegateInterface
import com.nextchaptersoftware.api.models.ConfluenceConfiguration
import com.nextchaptersoftware.api.models.ConfluenceDataCenter
import com.nextchaptersoftware.api.models.ConfluenceSite as ApiConfluenceSite
import com.nextchaptersoftware.api.models.ConfluenceSpace as ApiConfluenceSpace
import com.nextchaptersoftware.api.models.GetConfluenceSpacesResponse
import com.nextchaptersoftware.confluence.api.ConfluenceDataCenterApiProvider
import com.nextchaptersoftware.confluence.api.ConfluenceDataCenterUsersApi
import com.nextchaptersoftware.confluence.enqueue.ConfluenceEventEnqueueService
import com.nextchaptersoftware.crypto.Decryption
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ConfluenceSite
import com.nextchaptersoftware.db.models.ConfluenceSiteId
import com.nextchaptersoftware.db.models.ConfluenceSpace
import com.nextchaptersoftware.db.models.ConfluenceSpaceId
import com.nextchaptersoftware.db.models.ConfluenceSpaceIngestionType
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.ConfluenceContentStore
import com.nextchaptersoftware.db.stores.ConfluenceSiteStore
import com.nextchaptersoftware.db.stores.ConfluenceSpaceStore
import com.nextchaptersoftware.db.stores.EmbeddingDeleteStore
import com.nextchaptersoftware.db.stores.IngestionStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.HttpException
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.proxy.provider.delegates.providers.ConfluenceSpaceProvider
import com.nextchaptersoftware.segment.SegmentProvider
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.utils.KotlinUtils.required
import io.ktor.http.HttpStatusCode
import io.ktor.http.Url
import io.ktor.http.authority
import io.ktor.http.protocolWithAuthority
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class ConfluenceConfigurationDelegateImpl(
    private val slackNotifier: SlackNotifier,
    private val confluenceDataCenterApiProvider: ConfluenceDataCenterApiProvider,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val ingestionStore: IngestionStore = Stores.ingestionStore,
    private val confluenceSiteStore: ConfluenceSiteStore = Stores.confluenceSiteStore,
    private val confluenceSpaceStore: ConfluenceSpaceStore = Stores.confluenceSpaceStore,
    private val confluenceContentStore: ConfluenceContentStore = Stores.confluenceContentStore,
    private val embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
    private val segmentProvider: SegmentProvider,
    private val confluenceSpaceProvider: ConfluenceSpaceProvider,
    private val confluenceEventEnqueueService: ConfluenceEventEnqueueService,
    private val tokenSecretDecryption: Decryption,
) : ConfluenceConfigurationDelegateInterface {
    companion object {
        private const val CONFLUENCE_SITE_NOT_FOUND = "Confluence site not found"
        private const val ERROR_INVALID_URL = "The host URL provided is invalid. Please verify and try again."
        private const val ERROR_INVALID_TOKEN = "The token provided is invalid. Verify that the token entered is for the specified host."
    }

    private fun notFoundException(): UserVisibleException {
        return UserVisibleException(
            statusCode = HttpStatusCode.NotFound,
            title = CONFLUENCE_SITE_NOT_FOUND,
            detail = null,
            url = null,
        )
    }

    private suspend fun getConfluenceSite(
        orgId: OrgId,
        confluenceSiteId: ConfluenceSiteId,
    ): Pair<Installation, ConfluenceSite> {
        val installationAndSite = confluenceSiteStore.find(id = confluenceSiteId)

        return when (installationAndSite != null && installationAndSite.first.orgId == orgId) {
            true -> installationAndSite
            else -> throw notFoundException()
        }
    }

    override suspend fun getConfluenceConfiguration(
        orgId: OrgId,
        confluenceSiteId: ConfluenceSiteId,
        orgMemberId: OrgMemberId,
    ): ConfluenceConfiguration {
        val (installation, site) = getConfluenceSite(orgId = orgId, confluenceSiteId = confluenceSiteId)

        return when (site.confluenceSpaceIngestionType) {
            ConfluenceSpaceIngestionType.AllSpaces -> {
                ConfluenceConfiguration(all = true, spaces = emptyList())
            }

            ConfluenceSpaceIngestionType.SelectedSpacesOnly -> {
                val accessibleSpacesForIngestion = confluenceSpaceProvider.accessibleSpaces(
                    installation = installation,
                    site = site,
                    orgMemberId = orgMemberId,
                ).filter {
                    site.shouldIngest(it)
                }

                ConfluenceConfiguration(all = false, spaces = accessibleSpacesForIngestion.map { it.id.value })
            }

            null -> {
                // A null site.confluenceSpaceIngestionType means this has not been configured,
                // so we should throw a 404 instead of returning a ConfluenceConfiguration object
                // where ConfluenceConfiguration.all is false
                throw notFoundException()
            }
        }
    }

    override suspend fun getConfluenceSpaces(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        confluenceSiteId: ConfluenceSiteId,
    ): GetConfluenceSpacesResponse {
        val (installation, site) = getConfluenceSite(orgId = orgId, confluenceSiteId = confluenceSiteId)

        val accessibleSpaces = confluenceSpaceProvider.accessibleSpaces(
            installation = installation,
            site = site,
            orgMemberId = orgMemberId,
        ).map {
            ApiConfluenceSpace(
                id = it.id.value,
                teamId = orgId.value, // this assignment of org to team is correct
                confluenceSiteId = it.confluenceSiteId.value,
                name = it.spaceName,
                key = it.spaceKey ?: "",
            )
        }

        // If accessibleSpaces is empty, check to see there exists at least one space in the DB to determine
        // whether ingestion is complete. This flag signals to the client that it can stop polling for spaces.
        val isIngestionComplete = accessibleSpaces.isNotEmpty() || confluenceSpaceStore.list(confluenceSiteId = site.id, limit = 1).isNotEmpty()

        return GetConfluenceSpacesResponse(
            isIngestionComplete = isIngestionComplete,
            confluenceSpaces = accessibleSpaces,
        )
    }

    override suspend fun getConfluenceSites(
        orgId: OrgId,
    ): List<ApiConfluenceSite> {
        return installationStore.getIntegrationInstallations(
            providers = listOf(Provider.Confluence, Provider.ConfluenceDataCenter),
            orgId = orgId,
        ).mapNotNull { installation ->
            confluenceSiteStore.findForInstallation(installationId = installation.id)?.let { site ->
                installation to site
            }
        }.map { (installation, site) ->
            val isDataCenter = installation.provider == Provider.ConfluenceDataCenter

            ApiConfluenceSite(
                id = site.id.value,
                teamId = installation.orgId.value, // this assignment of org to team is correct
                name = site.name,
                isInstalled = true,
                avatarUrl = if (isDataCenter) "" else site.avatarUrl.asString, // Client to show confluence icon for now
                isDataCenter = isDataCenter,
            )
        }
    }

    override suspend fun updateConfluenceConfiguration(
        orgId: OrgId,
        confluenceSiteId: ConfluenceSiteId,
        confluenceSpaceIngestionType: ConfluenceSpaceIngestionType,
        confluenceSpaceIds: List<ConfluenceSpaceId>,
        orgMemberId: OrgMemberId,
    ) {
        val (installation, siteBeforeUpdate) = confluenceSiteStore.find(id = confluenceSiteId) ?: throw NotFoundException()

        val spacesIngestedBeforeUpdate = confluenceSpaceStore.list(confluenceSiteId = confluenceSiteId).filter {
            siteBeforeUpdate.shouldIngest(it)
        }

        require(installation.id == siteBeforeUpdate.installationId) { "installationId does not match" }
        require(installation.orgId == orgId) { "orgId does not match" }

        confluenceSiteStore.updateConfluenceSpaceIngestionType(
            id = siteBeforeUpdate.id,
            confluenceSpaceIngestionType = confluenceSpaceIngestionType,
        )

        val siteAfterUpdate = confluenceSiteStore.findForInstallation(installationId = installation.id)
            ?: error("site not found") // should never happen

        val (shouldIngest, shouldNotIngest) = confluenceSpaceProvider.accessibleSpaces(
            installation = installation,
            site = siteAfterUpdate,
            orgMemberId = orgMemberId,
        ).partition {
            it.id in confluenceSpaceIds
        }

        confluenceSpaceStore.updateShouldIngest(confluenceSpaceIds = shouldIngest.map { it.id }, shouldIngest = true)
        confluenceSpaceStore.updateShouldIngest(confluenceSpaceIds = shouldNotIngest.map { it.id }, shouldIngest = false)

        deleteEmbeddings(
            installation = installation,
            siteAfterUpdate = siteAfterUpdate,
            spacesIngestedBeforeUpdate = spacesIngestedBeforeUpdate,
        )

        // Re-ingest if weren't we weren't ingesting everything before
        if (siteBeforeUpdate.confluenceSpaceIngestionType != ConfluenceSpaceIngestionType.AllSpaces) {
            ingestionStore.setLastSynced(installationId = installation.id, lastSynced = null)
        }
    }

    internal suspend fun deleteEmbeddings(
        installation: Installation,
        siteAfterUpdate: ConfluenceSite,
        spacesIngestedBeforeUpdate: List<ConfluenceSpace>,
    ) {
        // No need to delete embeddings if we're now ingesting everything
        if (siteAfterUpdate.confluenceSpaceIngestionType == ConfluenceSpaceIngestionType.AllSpaces) {
            return
        }

        // Delete embeddings for spaces that were ingested before and now shouldn't be
        confluenceSpaceStore.list(confluenceSiteId = siteAfterUpdate.id)
            .filter { !siteAfterUpdate.shouldIngest(it) }
            .filter { space -> spacesIngestedBeforeUpdate.any { it.id == space.id } }
            .forEach {
                embeddingDeleteStore.markGroupForDeletion(
                    namespaceId = installation.orgId,
                    installationId = installation.id,
                    groupId = it.id.value,
                )

                confluenceContentStore.markForDeletion(
                    installationId = installation.id,
                    spaceId = it.id,
                )
            }
    }

    override suspend fun upsertConfluenceDataCenter(
        installationId: InstallationId,
        orgId: OrgId,
        personId: PersonId,
        orgMemberId: OrgMemberId,
        hostUrl: String?,
        encryptedToken: Ciphertext,
    ): ConfluenceDataCenter {
        return suspendedTransaction {
            when (val existing = installationStore.findById(trx = this, installationId = installationId)) {
                null -> {
                    val sanitizedHostUrl = hostUrl.required().asUrl.protocolWithAuthority.asUrl

                    validateDataCenterToken(baseUrl = sanitizedHostUrl, encryptedToken = encryptedToken)

                    val installation = installationStore.insert(
                        trx = this,
                        id = installationId,
                        orgId = orgId,
                        provider = Provider.ConfluenceDataCenter,
                        installationExternalId = sanitizedHostUrl.authority,
                        displayName = sanitizedHostUrl.authority,
                        htmlUrl = sanitizedHostUrl,
                        rawAccessToken = encryptedToken,
                    ).also {
                        slackNotifier.announceIntegrationAdded(installation = it, personId = personId)
                        segmentProvider.integrationEnabledByOrgMember(orgMemberId = orgMemberId, orgId = orgId, provider = it.provider)
                    }

                    val site = confluenceSiteStore.create(
                        trx = this,
                        installationId = installation.id,
                        siteId = sanitizedHostUrl.authority,
                        baseUrl = sanitizedHostUrl,
                        avatarUrl = sanitizedHostUrl, // TODO Get the actual avatar URL
                        name = sanitizedHostUrl.toString(),
                    )

                    Pair(installation, site)
                }

                else -> {
                    val site = confluenceSiteStore.findForInstallation(
                        trx = this,
                        installationId = existing.id,
                    ) ?: error("Failed to find Confluence site for installation: ${existing.id}") // Should not be possible

                    validateDataCenterToken(baseUrl = site.baseUrl, encryptedToken = encryptedToken)

                    installationStore.updateRawAccessToken(
                        trx = this,
                        id = existing.id,
                        orgId = orgId,
                        rawAccessToken = encryptedToken,
                    )

                    Pair(existing, site)
                }
            }
        }.let { (installation, confluenceSite) ->
            confluenceEventEnqueueService.enqueueIngestSiteSpacesEvent(installationId = installation.id)
            confluenceEventEnqueueService.enqueueIngestSiteUsersAndGroupsEvent(installationId = installation.id)

            ConfluenceDataCenter(
                id = installation.id.value,
                teamId = installation.orgId.value, // this assignment of org to team is correct
                confluenceSiteId = confluenceSite.id.value,
                hostUrl = confluenceSite.baseUrl.asString,
            )
        }
    }

    private suspend fun validateDataCenterToken(
        baseUrl: Url,
        encryptedToken: Ciphertext,
    ) {
        runSuspendCatching {
            confluenceDataCenterApiProvider.usersApi.getUser(
                url = ConfluenceDataCenterUsersApi.getCurrentUserUrl(baseUrl = baseUrl),
                accessToken = tokenSecretDecryption.decrypt(encryptedToken),
            )
        }.getOrElse {
            LOGGER.errorAsync(it, "baseUrl" to baseUrl) { "Failed to validate data center token" }

            throw when {
                it is HttpException && it.statusCode == HttpStatusCode.Unauthorized -> {
                    UserVisibleException(
                        statusCode = HttpStatusCode.BadRequest,
                        title = ERROR_INVALID_TOKEN,
                        detail = null,
                        url = null,
                    )
                }

                else -> {
                    UserVisibleException(
                        statusCode = HttpStatusCode.BadRequest,
                        title = ERROR_INVALID_URL,
                        detail = null,
                        url = null,
                    )
                }
            }
        }
    }
}
