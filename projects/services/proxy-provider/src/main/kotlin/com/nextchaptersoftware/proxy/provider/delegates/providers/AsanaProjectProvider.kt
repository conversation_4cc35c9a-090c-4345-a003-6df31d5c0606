package com.nextchaptersoftware.proxy.provider.delegates.providers

import com.nextchaptersoftware.db.models.AsanaProject
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.stores.AsanaProjectStore
import com.nextchaptersoftware.db.stores.Stores

class AsanaProjectProvider(
    private val asanaProjectStore: AsanaProjectStore = Stores.asanaProjectStore,
) {
    suspend fun assessableProjects(
        installationId: InstallationId,
    ): List<AsanaProject> {
        val projects = asanaProjectStore.findByInstallation(installationId)
        // TODO ASANA: DSac

        return projects
    }
}
