baseservice:
  resources:
    limits:
      memory: 1280Mi
    requests:
      cpu: 800m
      memory: 1024Mi
  service:
    environment: "dev"
    baseUrl: "dev.getunblocked.com"
    envFrom:
      secretRefs:
        - unblocked-ci-secrets-env
        - unblocked-content-service-secrets-env
        - unblocked-document-service-secrets-env
        - unblocked-scm-service-secrets-env
        - unblocked-service-secrets-env
        - unblocked-service-user-secrets-env
        - unblocked-stripe-secrets-env
    env:
      - name: "REDIS_PASSWORD_FILE_PATH"
        value: "/secrets/redis-password"
      - name: "ACTIVEMQ_PASSWORD_FILE_PATH"
        value: "/secrets/activemq-password"
  kedaautoscaling:
    enabled: true
    minReplicaCount: 1
    maxReplicaCount: 3
    triggers:
      cpu:
        enabled: true
