package com.nextchaptersoftware.ingest.web.plugins

import com.nextchaptersoftware.api.HealthApi
import com.nextchaptersoftware.api.serialization.SerializationExtensions.installSerializer
import com.nextchaptersoftware.ingest.web.api.HealthApiDelegateImpl
import com.nextchaptersoftware.service.ServiceHealthApi
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.resources.Resources
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

fun Application.configureRouting(serviceLifecycle: ServiceLifecycle) {
    install(Resources) {
        installSerializer()
    }

    val healthApiDelegateImpl by lazy {
        HealthApiDelegateImpl(ServiceHealthApi(serviceLifecycle = serviceLifecycle))
    }

    routing {
        route("/api") {
            HealthApi(healthApiDelegateImpl)
        }

        // Path used for exposing health endpoint to Kubernetes
        route("/api/health/ingest-web") {
            HealthApi(healthApiDelegateImpl)
        }
    }
}
