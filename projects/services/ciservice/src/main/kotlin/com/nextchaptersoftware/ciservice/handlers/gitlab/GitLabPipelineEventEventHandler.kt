package com.nextchaptersoftware.ciservice.handlers.gitlab

import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.scm.gitlab.models.GitLabPipelineEvent

private val LOGGER = mu.KotlinLogging.logger {}

@Suppress("UnusedPrivateMember")
class GitLabPipelineEventEventHandler(
    private val buildIngestionService: BuildIngestionService,
) : TypedEventHandler<GitLabPipelineEvent> {

    override suspend fun handle(
        event: GitLabPipelineEvent,
    ): Boolean = withLoggingContextAsync(
        "gitlab.project.id" to event.project.id,
        "gitlab.project.namespace" to event.project.namespace,
        "gitlab.project.name" to event.project.name,
        "gitlab.pipeline.id" to event.pipeline.id,
    ) {
        doHandle(event)
        true
    }

    private suspend fun doHandle(
        event: GitLabPipelineEvent,
    ) {
        LOGGER.debugSensitiveAsync(
            sensitiveFields = mapOf(
                "event" to event,
            ),
        ) { "GitLabPipelineEvent" }

        // TODO: buildIngestionService.ingestBuild
    }
}
