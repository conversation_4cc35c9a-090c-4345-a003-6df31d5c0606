package com.nextchaptersoftware.ciservice.handlers.gitlab

import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.scm.gitlab.models.GitLabJobEvent

private val LOGGER = mu.KotlinLogging.logger {}

@Suppress("UnusedPrivateMember")
class GitLabJobEventEventHandler(
    private val buildIngestionService: BuildIngestionService,
) : TypedEventHandler<GitLabJobEvent> {
    override suspend fun handle(
        event: GitLabJobEvent,
    ): Boolean = withLoggingContextAsync(
        "gitlab.project.id" to event.project.id,
        "gitlab.project.namespace" to event.project.namespace,
        "gitlab.project.name" to event.project.name,
        "gitlab.pipeline.id" to event.pipelineId,
        "gitlab.job.id" to event.id,
    ) {
        doHandle(event)
        true
    }

    private suspend fun doHandle(
        event: GitLabJobEvent,
    ) {
        LOGGER.debugSensitiveAsync(
            sensitiveFields = mapOf(
                "event" to event,
            ),
        ) { "GitLabJobEvent" }

        // TODO: buildIngestionService.ingestBuildJob
    }
}
