package com.nextchaptersoftware.ciservice.handlers.github

import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.CITriageController
import com.nextchaptersoftware.ci.create
import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckSuiteResolver.resolveCiBuild
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.github.GitHubActionsClientProvider
import com.nextchaptersoftware.scm.github.models.GitHubCheckSuiteEvent
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import com.nextchaptersoftware.utils.KotlinUtils.required
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class GitHubCheckSuiteEventHandler(
    private val buildIngestionService: BuildIngestionService,
    private val ciTriageController: CITriageController,
    private val gitHubActionsClientProvider: GitHubActionsClientProvider,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val teamAndRepoProvider: TeamAndRepoProvider,
) : TypedEventHandler<GitHubCheckSuiteEvent> {

    override suspend fun handle(
        event: GitHubCheckSuiteEvent,
    ): Boolean = withLoggingContextAsync(
        "github.checkSuite.id" to event.checkSuite.id,
        "github.pullRequestNumbers" to event.checkSuite.pullRequests.map { it.number },
        "github.ownerExternalId" to event.ownerExternalId,
        "github.repoExternalId" to event.repoExternalId,
        "github.action" to event.action,
    ) fn@{
        doProcess(event)
        true
    }

    private suspend fun doProcess(
        event: GitHubCheckSuiteEvent,
    ) {
        val (scmTeam, repo) = teamAndRepoProvider.get(
            scm = Scm.GitHub,
            ownerExternalId = event.ownerExternalId,
            repoExternalId = event.repoExternalId,
        ) ?: run {
            LOGGER.debugAsync { "Team and repo not found" }
            return
        }

        val ciInstallation = installationStore.findByProvider(
            orgId = scmTeam.orgId,
            providers = setOf(Provider.GitHubActions),
        ).firstOrNull() ?: return

        val prNumber = event.checkSuite.pullRequestOrNull
        if (prNumber == null) {
            LOGGER.debugAsync { "Missing PR number" }
            return
        }

        val pullRequest = pullRequestStore.findByNumber(repoId = repo.id, number = prNumber)
            ?.takeIf { it.state == PullRequestState.Open }
            ?: run {
                LOGGER.debugAsync { "PR is missing or stale" }
                return
            }

        if (!ciTriageController.ciEnabled(
                orgId = scmTeam.orgId,
                repo = repo,
                orgMemberId = pullRequest.creatorOrgMemberId,
                ciInstallationId = ciInstallation.id,
                scmInstallationId = scmTeam.installationId,
            )
        ) {
            LOGGER.debugAsync { "Not enabled" }
            return
        }

        event.doIngestBuild(
            pullRequest = pullRequest,
            scmTeam = scmTeam.asDataModel(),
            ciInstallation = ciInstallation,
            repo = repo,
        )
    }

    private suspend fun GitHubCheckSuiteEvent.doIngestBuild(
        pullRequest: PullRequest,
        scmTeam: ScmTeam,
        ciInstallation: Installation,
        repo: Repo,
    ) {
        val projectContext = CIProjectContext.GitHubActions(
            enterpriseAppConfigId = scmTeam.providerEnterpriseId,
            externalInstallationId = scmTeam.providerExternalInstallationId.required(),
            repoExternalId = repo.externalId,
        )

        val ciBuild = gitHubActionsClientProvider.create(
            projectContext = projectContext,
        ).use {
            it.resolveCiBuild(
                repo = repo,
                checkSuite = checkSuite,
            )
        }

        buildIngestionService.ingestBuild(
            ciInstallation = ciInstallation,
            scmTeamId = scmTeam.id,
            repoId = repo.id,
            pullRequest = pullRequest,
            projectContext = projectContext,
            ciBuild = ciBuild,
        )
    }
}
