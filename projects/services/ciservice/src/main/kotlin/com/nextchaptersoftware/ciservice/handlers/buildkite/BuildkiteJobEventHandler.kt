package com.nextchaptersoftware.ciservice.handlers.buildkite

import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.buildkite.models.BuildkiteJobEvent
import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.ktor.utils.RepoUrl
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class BuildkiteJobEventHandler(
    private val buildIngestionService: BuildIngestionService,
) : TypedEventHandler<BuildkiteJobEvent> {
    override suspend fun handle(
        event: BuildkiteJobEvent,
    ): Boolean = withLoggingContextAsync(
        "buildkite.pipeline.id" to event.pipeline.id,
        "buildkite.build.id" to event.build.id,
        "buildkite.job.id" to event.job.id,
    ) fn@{
        val repoUrl = RepoUrl.parseOrThrow(
            event.build.pullRequest?.repository ?: event.pipeline.repository,
        )

        val repo = repoStore.findByUrl(
            orgIds = null,
            repoUrl = repoUrl,
        ) ?: run {
            LOGGER.traceAsync { "Repo not found" }
            return@fn true
        }

        val scmTeam = scmTeamStore.findById(teamId = repo.teamId) ?: run {
            LOGGER.traceAsync { "SCM team not found" }
            return@fn true
        }

        val ciInstallation = installationStore.findByProvider(
            orgId = scmTeam.orgId,
            providers = setOf(Provider.Buildkite),
        ).firstOrNull() ?: run {
            LOGGER.traceAsync { "CI installation not found" }
            return@fn true
        }

        Stores.ciWebhookEventStore.upsertWebhookEvent(
            ciInstallationId = ciInstallation.id,
            label = event.build.orgSlug,
        )

        val pullRequest =
            event.build.pullRequest?.id?.toIntOrNull()?.let {
                pullRequestStore.findByNumber(
                    repoId = repo.id,
                    number = it,
                )
            }
                ?: pullRequestStore.findByHead(
                    repoId = repo.id,
                    headSha = event.build.commit,
                    headBranch = event.build.branch,
                )
                ?: run {
                    LOGGER.traceAsync { "Pull Request not found" }
                    return@fn true
                }

        buildIngestionService.ingestBuildJob(
            ciInstallation = ciInstallation,
            scmTeamId = repo.teamId,
            repoId = repo.id,
            pullRequest = pullRequest,
            ciBuild = event.asCiBuild(),
            ciJob = event.job.asCiJob(),
            projectContext = CIProjectContext.Buildkite(
                orgSlug = event.build.orgSlug,
                projectSlug = event.pipeline.slug,
            ),
        )

        true
    }
}
