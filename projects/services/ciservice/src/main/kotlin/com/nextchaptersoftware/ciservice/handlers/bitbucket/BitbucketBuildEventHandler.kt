package com.nextchaptersoftware.ciservice.handlers.bitbucket

import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.CITriageController
import com.nextchaptersoftware.ci.bitbucket.models.asCiBuild
import com.nextchaptersoftware.ci.bitbucket.models.asCiJob
import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.bitbucket.BitbucketPipelinesClient
import com.nextchaptersoftware.scm.bitbucket.BitbucketPipelinesClientProvider
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketBuildStatusEvent
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketPipeline
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import com.nextchaptersoftware.utils.KotlinUtils.required
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList

private val LOGGER = mu.KotlinLogging.logger {}

class BitbucketBuildEventHandler(
    private val buildIngestionService: BuildIngestionService,
    private val ciTriageController: CITriageController,
    private val teamAndRepoProvider: TeamAndRepoProvider,
    private val bitbucketPipelinesClientProvider: BitbucketPipelinesClientProvider,
) : TypedEventHandler<BitbucketBuildStatusEvent> {

    override suspend fun handle(
        event: BitbucketBuildStatusEvent,
    ): Boolean = withLoggingContextAsync(
        "bitbucket.repo.uuid" to event.repository.uuid,
        "bitbucket.repo.fullName" to event.repository.fullName,
        "bitbucket.workspace.uui" to event.repository.workspace.uuid,
        "bitbucket.workspace.name" to event.repository.fullName.substringBefore("/"),
        "bitbucket.commit.key" to event.commitStatus.key,
        "bitbucket.commit.state" to event.commitStatus.state,
    ) {
        doProcess(
            event = event,
        )
        true
    }

    private suspend fun doProcess(
        event: BitbucketBuildStatusEvent,
    ) {
        val (scmTeamDAO, repo) = teamAndRepoProvider.get(
            scm = Scm.Bitbucket,
            ownerExternalId = event.repository.workspace.uuid,
            repoExternalId = event.repository.uuid,
        ) ?: run {
            LOGGER.debugAsync { "Team and repo not found" }
            return
        }

        val scmTeam = scmTeamDAO.asDataModel()

        val ciInstallation = installationStore.findByProvider(
            orgId = scmTeam.orgId,
            provider = Provider.BitbucketPipelines,
        )
            .firstOrNull()
            ?: run {
                LOGGER.debugAsync { "CI installation not found" }
                return
            }

        val pullRequest = findPullRequest(
            repo = repo,
            event = event,
        )
            ?.takeIf { it.state == PullRequestState.Open }
            ?: run {
                LOGGER.debugAsync { "PR is missing or stale" }
                return
            }

        if (!ciTriageController.ciEnabled(
                orgId = scmTeam.orgId,
                repo = repo,
                orgMemberId = pullRequest.creatorOrgMemberId,
                ciInstallationId = ciInstallation.id,
                scmInstallationId = scmTeam.installationId,
            )
        ) {
            LOGGER.debugAsync { "Not enabled" }
            return
        }

        val bitbucketPipelinesClient = bitbucketPipelinesClientProvider.create(
            scmTeam = scmTeam,
            repo = repo,
        )

        val pipeline = resolvePipeline(
            bitbucketPipelinesClient = bitbucketPipelinesClient,
            event = event,
            repo = repo,
        )
            ?: run {
                LOGGER.debugAsync { "Pipeline not found" }
                return
            }

        val steps = bitbucketPipelinesClient.pipelineSteps(
            workspace = repo.externalOwner,
            repository = repo.externalName,
            pipeline = pipeline.uuid,
        )

        buildIngestionService.ingestBuild(
            ciInstallation = ciInstallation,
            projectContext = CIProjectContext.BitbucketPipelines(
                externalInstallationId = scmTeam.providerExternalInstallationId.required(),
                externalRepositoryId = repo.externalId,
            ),
            scmTeamId = scmTeam.id,
            repoId = repo.id,
            pullRequest = pullRequest,
            ciBuild = pipeline.asCiBuild().copy(
                displayName = event.commitStatus.name,
            ),
            ciJobs = steps.map { it.asCiJob() }.toList(),
        )
    }

    private suspend fun findPullRequest(
        repo: Repo,
        event: BitbucketBuildStatusEvent,
    ): PullRequest? {
        //
        suspend fun findPullRequestByNumber() = event.pullRequestNumber()?.let { pullRequestNumber ->
            pullRequestStore.findByNumber(
                repoId = repo.id,
                number = pullRequestNumber,
            )
        }

        suspend fun findPullRequestByCommit() = event.commitStatus.refname?.let { refname ->
            pullRequestStore.findByHead(
                repoId = repo.id,
                headBranch = refname,
                headSha = event.commitStatus.commit.hash,
            )
        }

        // Bitbucket does not provide the pull request number in plain format
        // Some pipelines have an auto-generated title, to extract the number from,
        // when the extraction fails, we will fallback to use branch/commit information

        return findPullRequestByNumber()
            ?: findPullRequestByCommit()
    }

    private suspend fun resolvePipeline(
        bitbucketPipelinesClient: BitbucketPipelinesClient,
        event: BitbucketBuildStatusEvent,
        repo: Repo,
    ): BitbucketPipeline? {
        return bitbucketPipelinesClient.pipelines(
            workspace = repo.externalOwner,
            repository = repo.externalName,
            targetSelectorType = "PULLREQUESTS",
            targetSelectorPattern = "**",
            targetBranch = event.commitStatus.refname,
            targetCommitHash = event.commitStatus.commit.hash,
        )
            .filter { it.target.pullrequest != null }
            .let { pipelines ->
                when (val pipelineNumber = event.pipelineNumber()) {
                    null -> pipelines
                    else -> pipelines.filter { it.buildNumber == pipelineNumber }
                }
            }
            .toList()
            .maxByOrNull {
                it.completedOn
                ?: it.runCreationDate
                ?: it.createdOn
            }
    }
}
