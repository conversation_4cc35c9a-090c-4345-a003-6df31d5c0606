package com.nextchaptersoftware.ciservice.handlers.github

import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.CITriageController
import com.nextchaptersoftware.ci.create
import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckRunResolver.resolveCiJob
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckSuiteResolver.resolveCiBuild
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveSync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.github.GitHubActionsClientProvider
import com.nextchaptersoftware.scm.github.models.GitHubCheckRunEvent
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import com.nextchaptersoftware.utils.KotlinUtils.required
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class GitHubCheckRunEventHandler(
    private val buildIngestionService: BuildIngestionService,
    private val ciTriageController: CITriageController,
    private val gitHubActionsClientProvider: GitHubActionsClientProvider,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val teamAndRepoProvider: TeamAndRepoProvider,
) : TypedEventHandler<GitHubCheckRunEvent> {

    override suspend fun handle(
        event: GitHubCheckRunEvent,
    ): Boolean = withLoggingContextAsync(
        "github.checkRun.id" to event.checkRun.id,
        "github.checkSuite.id" to event.checkRun.checkSuite.id,
        "github.pullRequestNumbers" to event.checkRun.checkSuite.pullRequests.map { it.number },
        "github.ownerExternalId" to event.ownerExternalId,
        "github.repoExternalId" to event.repoExternalId,
        "github.action" to event.action,
    ) fn@{
        doProcess(
            event = event,
        )
        true
    }

    private suspend fun doProcess(
        event: GitHubCheckRunEvent,
    ) = run fn@{
        val (scmTeam, repo) = teamAndRepoProvider.get(
            scm = Scm.GitHub,
            ownerExternalId = event.ownerExternalId,
            repoExternalId = event.repoExternalId,
        ) ?: run {
            LOGGER.debugAsync { "Team and repo not found" }
            return@fn
        }

        val prNumber = event.checkRun.checkSuite.pullRequestOrNull
        if (prNumber == null) {
            LOGGER.debugAsync { "Missing PR number" }
            return@fn
        }

        val pullRequest = pullRequestStore.findByNumber(repoId = repo.id, number = prNumber)
            ?.takeIf { it.state == PullRequestState.Open }
            ?: run {
                LOGGER.debugAsync { "PR is missing or stale" }
                return@fn
            }

        val ciInstallation = installationStore.findByProvider(
            orgId = scmTeam.orgId,
            providers = setOf(Provider.GitHubActions),
        ).firstOrNull() ?: return@fn

        if (!ciTriageController.ciEnabled(
                orgId = scmTeam.orgId,
                repo = repo,
                orgMemberId = pullRequest.creatorOrgMemberId,
                ciInstallationId = ciInstallation.id,
                scmInstallationId = scmTeam.installationId,
            )
        ) {
            LOGGER.debugAsync { "Not enabled" }
            return@fn
        }

        event.doIngestBuildJob(
            ciInstallation = ciInstallation,
            pullRequest = pullRequest,
            scmTeam = scmTeam.asDataModel(),
            repo = repo,
        )
    }

    private suspend fun GitHubCheckRunEvent.doIngestBuildJob(
        ciInstallation: Installation,
        pullRequest: PullRequest,
        scmTeam: ScmTeam,
        repo: Repo,
    ) {
        val projectContext = CIProjectContext.GitHubActions(
            enterpriseAppConfigId = scmTeam.providerEnterpriseId,
            externalInstallationId = scmTeam.providerExternalInstallationId.required(),
            repoExternalId = repoExternalId,
        )

        val gitHubActionsClient = gitHubActionsClientProvider.create(projectContext)

        val ciBuild = gitHubActionsClient.resolveCiBuild(
            checkSuite = checkRun.checkSuite,
            repo = repo,
        )

        val ciJob = gitHubActionsClient.resolveCiJob(
            repo = repo,
            ciBuild = ciBuild,
            checkRun = checkRun,
        )

        if (ciBuild.externalId != ciJob.externalParentId) {
            LOGGER.debugSensitiveSync(
                sensitiveFields = mapOf(
                    "github.checkRun" to checkRun.toString(),
                ),
            ) { "Parent Child issue" }
        }

        buildIngestionService.ingestBuildJob(
            ciInstallation = ciInstallation,
            scmTeamId = scmTeam.id,
            repoId = repo.id,
            pullRequest = pullRequest,
            ciBuild = ciBuild,
            ciJob = ciJob,
            projectContext = projectContext,
        )
    }
}
