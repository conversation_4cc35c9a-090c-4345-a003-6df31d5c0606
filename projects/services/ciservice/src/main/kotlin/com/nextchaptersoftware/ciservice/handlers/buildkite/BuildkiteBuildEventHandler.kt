package com.nextchaptersoftware.ciservice.handlers.buildkite

import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.buildkite.models.BuildkiteBuildEvent
import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.ktor.utils.RepoUrl
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

@Suppress("unused")
class BuildkiteBuildEventHandler(
    private val buildIngestionService: BuildIngestionService,
) : TypedEventHandler<BuildkiteBuildEvent> {

    override suspend fun handle(
        event: BuildkiteBuildEvent,
    ): Boolean = withLoggingContextAsync(
        "buildkite.build.id" to event.build.id,
        "buildkite.pipeline.id" to event.pipeline.id,
    ) fn@{
        val repoUrl = RepoUrl.parseOrThrow(
            event.build.pullRequest?.repository ?: event.pipeline.repository,
        )

        val repo = repoStore.findByUrl(
            orgIds = null,
            repoUrl = repoUrl,
        ) ?: run {
            LOGGER.debugAsync { "Repo not found" }
            return@fn true
        }

        val scmTeam = scmTeamStore.findById(teamId = repo.teamId) ?: run {
            LOGGER.debugAsync { "SCM team not found" }
            return@fn true
        }

        val ciInstallation = installationStore.findByProvider(
            orgId = scmTeam.orgId,
            providers = setOf(Provider.Buildkite),
        ).firstOrNull() ?: run {
            LOGGER.debugAsync { "CI installation not found" }
            return@fn true
        }

        Stores.ciWebhookEventStore.upsertWebhookEvent(
            ciInstallationId = ciInstallation.id,
            label = event.build.orgSlug,
        )

        val pullRequest =
            event.build.pullRequest?.id?.toIntOrNull()?.let {
                pullRequestStore.findByNumber(
                    repoId = repo.id,
                    number = it,
                )
            }
                ?: pullRequestStore.findByHead(
                    repoId = repo.id,
                    headSha = event.build.commit,
                    headBranch = event.build.branch,
                )
                ?: run {
                    LOGGER.debugAsync { "Pull Request not found" }
                    return@fn true
                }

        buildIngestionService.ingestBuild(
            ciInstallation = ciInstallation,
            scmTeamId = scmTeam.id,
            repoId = repo.id,
            pullRequest = pullRequest,
            projectContext = CIProjectContext.Buildkite(
                orgSlug = event.build.orgSlug,
                projectSlug = event.pipeline.slug,
            ),
            ciBuild = event.asCiBuild(),
        )

        true
    }
}
