@file:Suppress("SameReturnValue")

package com.nextchaptersoftware.ciservice.handlers.builds

import com.nextchaptersoftware.ci.CITriageController
import com.nextchaptersoftware.ci.enqueue.TriageEventEnqueueService
import com.nextchaptersoftware.ci.payloads.CiEvent
import com.nextchaptersoftware.ci.payloads.CiTriageOptions
import com.nextchaptersoftware.ci.payloads.params.PullRequests.checkIsTriageEnabled
import com.nextchaptersoftware.db.models.Build
import com.nextchaptersoftware.db.models.BuildJob
import com.nextchaptersoftware.db.models.BuildResult
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.stores.Stores.buildTriageStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.utils.KotlinUtils.doNothing
import com.nextchaptersoftware.utils.KotlinUtils.required

private val LOGGER = mu.KotlinLogging.logger { }

class BuildJobEventHandler(
    private val ciTriageController: CITriageController,
    private val triageEventEnqueueService: TriageEventEnqueueService,
) : TypedEventHandler<CiEvent.BuildJobCompletedEvent> {

    override suspend fun handle(
        event: CiEvent.BuildJobCompletedEvent,
    ): Boolean = withLoggingContextAsync(
        "ciInstallationId" to event.ciInstallationId,
        "scmTeamId" to event.scmTeamId,
        "repoId" to event.repoId,
        "pullRequestId" to event.pullRequestId,
        "buildId" to event.buildId,
        "jobId" to event.jobId,
    ) fn@{
        val pullRequest = event.pullRequest()

        pullRequest.checkIsTriageEnabled(
            isTriageEnabled = ciTriageController::isTriageEnabled,
        ) || return@fn true

        val repo = pullRequest.repo().required()
        val build = event.build()
        val job = event.job()

        withLoggingContextAsync(
            "build.runner" to build.runner,
            "build.type" to build.type,
            "build.externalId" to build.externalId,
            "build.displayNumber" to build.displayNumber,
            "job.type" to job.type,
            "job.externalId" to job.externalId,
            "job.externalParentId" to job.externalParentId,
        ) {
            doProcess(
                event = event,
                repo = repo,
                pullRequest = pullRequest,
                build = build,
                job = job,
            )
        }
        return@fn true
    }

    private suspend fun doProcess(
        event: CiEvent.BuildJobCompletedEvent,
        repo: Repo,
        pullRequest: PullRequest,
        build: Build,
        job: BuildJob,
    ) {
        when (job.result) {
            BuildResult.Pending -> doNothing()

            BuildResult.Success -> onSuccess(
                repo = repo,
                pullRequest = pullRequest,
                build = build,
                job = job,
                options = event.options,
            )

            BuildResult.Failure -> doNothing()

            BuildResult.Ignored -> doNothing()
        }
    }

    private suspend fun onSuccess(
        repo: Repo,
        pullRequest: PullRequest,
        build: Build,
        job: BuildJob,
        options: CiTriageOptions?,
    ) {
        LOGGER.traceAsync { "Job result is Success" }

        val updated = buildTriageStore.markTriagesOnSuccess(
            pullRequestId = pullRequest.id,
            jobDisplayName = job.displayName,
            commitFixedSha = build.headSha,
        )
        if (updated == 0) {
            LOGGER.traceAsync { "Job has no previous triage found" }
            return // no triage
        }

        triageEventEnqueueService.enqueueTriagePublishEvent(
            ciInstallationId = build.ciInstallationId,
            scmTeamId = repo.teamId,
            repoId = repo.id,
            pullRequestId = pullRequest.id,
            options = options,
        )
    }
}
