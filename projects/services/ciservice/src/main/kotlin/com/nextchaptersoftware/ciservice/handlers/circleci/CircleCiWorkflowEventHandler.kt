package com.nextchaptersoftware.ciservice.handlers.circleci

import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.circleci.models.CircleCiWorkflowEvent
import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.ktor.utils.RepoUrl
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class CircleCiWorkflowEventHandler(
    private val buildIngestionService: BuildIngestionService,
) : TypedEventHandler<CircleCiWorkflowEvent> {

    override suspend fun handle(
        event: CircleCiWorkflowEvent,
    ): Boolean = withLoggingContextAsync(
        "circleci.org.id" to event.organization.id,
        "circleci.project.slug" to event.project.projectSlug,
        "circleci.pipeline.id" to event.pipeline.id,
        "circleci.workflow.id" to event.workflow.id,
    ) fn@{
        val repo = repoStore.findByUrl(
            orgIds = null,
            repoUrl = event.pipeline.repoRef.repoUrl.let {
                RepoUrl.parseOrThrow(it)
            },
        ) ?: run {
            LOGGER.traceAsync { "Repo not found" }
            return@fn true
        }

        val scmTeam = scmTeamStore.findById(teamId = repo.teamId) ?: run {
            LOGGER.traceAsync { "SCM team not found" }
            return@fn true
        }

        val ciInstallation = installationStore.findByProvider(
            orgId = scmTeam.orgId,
            providers = setOf(Provider.CircleCI),
        ).firstOrNull() ?: run {
            LOGGER.traceAsync { "CI installation not found" }
            return@fn true
        }

        val pullRequest = pullRequestStore.findByHead(
            repoId = repo.id,
            headSha = event.pipeline.repoRef.headSha,
            headBranch = event.pipeline.repoRef.headBranch,
        ) ?: run {
            LOGGER.traceAsync { "Pull Request not found" }
            return@fn true
        }

        buildIngestionService.ingestBuild(
            ciInstallation = ciInstallation,
            scmTeamId = scmTeam.id,
            repoId = repo.id,
            pullRequest = pullRequest,
            projectContext = CIProjectContext.CircleCI(
                projectSlug = event.project.projectSlug,
            ),
            ciBuild = event.asCiBuild(),
        )

        true
    }
}
