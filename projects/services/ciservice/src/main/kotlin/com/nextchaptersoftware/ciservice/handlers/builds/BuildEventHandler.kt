package com.nextchaptersoftware.ciservice.handlers.builds

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.ci.CITriageController
import com.nextchaptersoftware.ci.enqueue.TriageEventEnqueueService
import com.nextchaptersoftware.ci.payloads.CiEvent
import com.nextchaptersoftware.ci.payloads.CiTriageOptions
import com.nextchaptersoftware.ci.payloads.params.PullRequests.checkIsTriageEnabled
import com.nextchaptersoftware.db.models.Build
import com.nextchaptersoftware.db.models.BuildResult
import com.nextchaptersoftware.db.models.BuildTriageState
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.stores.Stores.buildTriageStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.utils.KotlinUtils.doNothing
import com.nextchaptersoftware.utils.KotlinUtils.required
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class BuildEventHandler(
    private val ciTriageController: CITriageController,
    private val triageEventEnqueueService: TriageEventEnqueueService,
) : TypedEventHandler<CiEvent.BuildCompletedEvent> {

    override suspend fun handle(
        event: CiEvent.BuildCompletedEvent,
    ): Boolean = withLoggingContextAsync(
        "ciInstallationId" to event.ciInstallationId,
        "scmTeamId" to event.scmTeamId,
        "repoId" to event.repoId,
        "pullRequestId" to event.pullRequestId,
        "buildId" to event.buildId,
        "options" to event.options,
    ) fn@{
        val build = event.build()
        val pullRequest = event.pullRequest()

        pullRequest.checkIsTriageEnabled(
            isTriageEnabled = ciTriageController::isTriageEnabled,
        ) || return@fn true

        val repo = pullRequest.repo().required()

        withLoggingContextAsync(
            "build.runner" to build.runner,
            "build.type" to build.type,
            "build.externalId" to build.externalId,
            "build.displayNumber" to build.displayNumber,
        ) {
            doProcess(
                event = event,
                repo = repo,
                pullRequest = pullRequest,
                build = build,
            )
        }
        return@fn true
    }

    private suspend fun doProcess(
        event: CiEvent.BuildCompletedEvent,
        repo: Repo,
        pullRequest: PullRequest,
        build: Build,
    ) {
        when (build.result) {
            BuildResult.Pending -> doNothing()

            BuildResult.Success -> onSuccess(
                repo = repo,
                pullRequest = pullRequest,
                build = build,
                options = event.options,
            )

            BuildResult.Failure -> onFailure(
                repo = repo,
                pullRequest = pullRequest,
                build = build,
                options = event.options,
            )

            BuildResult.Ignored -> doNothing()
        }
    }

    private suspend fun onSuccess(
        repo: Repo,
        pullRequest: PullRequest,
        build: Build,
        options: CiTriageOptions?,
    ) {
        LOGGER.traceAsync { "Build result is Success" }

        val triageState = buildTriageStore.findTriageStateFor(
            pullRequestId = pullRequest.id,
        )

        if (triageState == null) {
            LOGGER.traceAsync { "Build has no previous triage found" }
            return // no triage
        }

        pullRequestStore.updateCiTriageState(
            pullRequestId = pullRequest.id,
            ciTriageState = triageState,
        )

        triageEventEnqueueService.enqueueTriagePublishEvent(
            ciInstallationId = build.ciInstallationId,
            scmTeamId = repo.teamId,
            repoId = repo.id,
            pullRequestId = pullRequest.id,
            options = options,
            priority = when (triageState) {
                BuildTriageState.Fixed -> MessagePriority.HIGH
                else -> null
            },
        )
    }

    private suspend fun onFailure(
        repo: Repo,
        pullRequest: PullRequest,
        build: Build,
        options: CiTriageOptions?,
    ) {
        LOGGER.traceAsync { "Build result is Failure" }

        if (pullRequest.isTriageMuted && options?.disablePullRequestMuteCheck != true) {
            LOGGER.traceAsync { "Pull Request is Muted" }
            return
        }

        triageEventEnqueueService.enqueueTriageRequestEvent(
            ciInstallationId = build.ciInstallationId,
            scmTeamId = repo.teamId,
            repoId = repo.id,
            pullRequestId = pullRequest.id,
            buildId = build.id,
            options = options,
        )
    }
}
