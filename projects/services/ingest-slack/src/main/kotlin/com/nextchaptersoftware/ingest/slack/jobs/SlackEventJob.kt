package com.nextchaptersoftware.ingest.slack.jobs

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.service.BackgroundJob

class SlackEventJob(
    private val eventDequeueService: EventDequeueService,
) : BackgroundJob {
    override val name: String
        get() = "Slack Event Job"

    override suspend fun run() {
        eventDequeueService.process()
    }
}
