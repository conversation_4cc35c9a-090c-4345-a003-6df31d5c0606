package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.models.NotionObjectType
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class NotionObjectStoreTest : DatabaseTestsBase() {
    private val store = Stores.notionObjectStore

    private val pageIdA1 = UUID.randomUUID()
    private val pageIdA2 = UUID.randomUUID()
    private val pageIdA3 = UUID.randomUUID()
    private val pageIdB1 = UUID.randomUUID()
    private val pageIdB2 = UUID.randomUUID()
    private val pageIdB3 = UUID.randomUUID()

    @Test
    fun getNextObjectsToIngest() = suspendingDatabaseTest {
        val installationAId = makeInstallation().idValue
        val installationBId = makeInstallation().idValue

        // Sanity check that there are no pages to ingest
        assertThat(store.getNextObjectsToIngest(installationId = installationAId, limit = 1)).isEmpty()
        assertThat(store.getNextObjectsToIngest(installationId = installationBId, limit = 1)).isEmpty()

        // Set up the pages, then check that the correct pages that need to be ingested are returned in the correct order
        store.markForIngestion(installationId = installationAId, externalNotionId = pageIdA1, type = NotionObjectType.Page, priority = null)
        store.markForIngestion(installationId = installationAId, externalNotionId = pageIdA2, type = NotionObjectType.Page, priority = 10)
        store.markForIngestion(installationId = installationAId, externalNotionId = pageIdA3, type = NotionObjectType.Page, priority = 20)
        store.markForIngestion(installationId = installationBId, externalNotionId = pageIdB1, type = NotionObjectType.Page, priority = null)
        store.markForIngestion(installationId = installationBId, externalNotionId = pageIdB2, type = NotionObjectType.Page, priority = null)
        store.markForIngestion(installationId = installationBId, externalNotionId = pageIdB3, type = NotionObjectType.Page, priority = null)
        store.markAsErrored(installationId = installationBId, externalNotionId = pageIdB1)
        store.markAsIngested(
            installationId = installationBId,
            externalNotionId = pageIdB3,
            type = NotionObjectType.Page,
            lastEditedTime = Instant.nowWithMicrosecondPrecision(),
        )
        assertThat(store.getNextObjectsToIngest(installationId = installationAId, limit = 1).single().externalNotionId).isEqualTo(pageIdA3)
        assertThat(store.getNextObjectsToIngest(installationId = installationBId, limit = 1).single().externalNotionId).isEqualTo(pageIdB2)

        // Mark a page as errored during ingestion, then check the correct next page to be ingested is returned
        store.markAsErrored(installationId = installationAId, externalNotionId = pageIdA3)
        assertThat(store.getNextObjectsToIngest(installationId = installationAId, limit = 1).single().externalNotionId).isEqualTo(pageIdA2)
        assertThat(store.getNextObjectsToIngest(installationId = installationBId, limit = 1).single().externalNotionId).isEqualTo(pageIdB2)

        // Mark a page as ingested, then check that we're done ingesting for that installation
        store.markAsIngested(
            installationId = installationBId,
            externalNotionId = pageIdB2,
            type = NotionObjectType.Page,
            lastEditedTime = Instant.nowWithMicrosecondPrecision(),
        )
        assertThat(store.getNextObjectsToIngest(installationId = installationAId, limit = 1).single().externalNotionId).isEqualTo(pageIdA2)
        assertThat(store.getNextObjectsToIngest(installationId = installationBId, limit = 1)).isEmpty()

        // Mark another page as ingested, then check that the correct next page to be ingested is returned
        store.markAsIngested(
            installationId = installationAId,
            externalNotionId = pageIdA2,
            type = NotionObjectType.Page,
            lastEditedTime = Instant.nowWithMicrosecondPrecision(),
        )
        assertThat(store.getNextObjectsToIngest(installationId = installationAId, limit = 1).single().externalNotionId).isEqualTo(pageIdA1)
        assertThat(store.getNextObjectsToIngest(installationId = installationBId, limit = 1)).isEmpty()

        // Finally, mark the last page as ingested, then check that we're done
        store.markAsIngested(
            installationId = installationAId,
            externalNotionId = pageIdA1,
            type = NotionObjectType.Page,
            lastEditedTime = Instant.nowWithMicrosecondPrecision(),
        )
        assertThat(store.getNextObjectsToIngest(installationId = installationAId, limit = 1)).isEmpty()
        assertThat(store.getNextObjectsToIngest(installationId = installationBId, limit = 1)).isEmpty()
    }

    @Test
    fun lastEditedTime() = suspendingDatabaseTest {
        val installationId = makeInstallation().idValue
        val externalNotionId = UUID.randomUUID()

        assertThat(store.lastEditedTime(installationId = installationId, externalNotionId = externalNotionId)).isNull()

        store.markForIngestion(
            installationId = installationId,
            externalNotionId = externalNotionId,
            type = NotionObjectType.Page,
            priority = null,
        )
        assertThat(store.lastEditedTime(installationId = installationId, externalNotionId = externalNotionId)).isNull()

        val lastEditedTime = Instant.nowWithMicrosecondPrecision()

        store.markAsIngested(
            installationId = installationId,
            externalNotionId = externalNotionId,
            type = NotionObjectType.Page,
            lastEditedTime = lastEditedTime,
        )
        assertThat(store.lastEditedTime(installationId = installationId, externalNotionId = externalNotionId)).isEqualTo(lastEditedTime)
    }

    @Test
    fun `filterExists, markForDeletion, listObjectsMarkedForDeletion`() = suspendingDatabaseTest {
        val installationAId = makeInstallation().idValue
        val installationBId = makeInstallation().idValue

        // Sanity check that there are no pages to ingest
        assertThat(store.getNextObjectsToIngest(installationId = installationAId, limit = 1)).isEmpty()
        assertThat(store.getNextObjectsToIngest(installationId = installationBId, limit = 1)).isEmpty()

        // Set up the pages, then check that the correct pages that need to be ingested are returned in the correct order
        store.markForIngestion(installationId = installationAId, externalNotionId = pageIdA1, type = NotionObjectType.Page, priority = null)
        store.markForIngestion(installationId = installationAId, externalNotionId = pageIdA2, type = NotionObjectType.Page, priority = null)
        store.markForIngestion(installationId = installationAId, externalNotionId = pageIdA3, type = NotionObjectType.Page, priority = null)
        store.markForIngestion(installationId = installationBId, externalNotionId = pageIdB1, type = NotionObjectType.Page, priority = null)
        store.markForIngestion(installationId = installationBId, externalNotionId = pageIdB2, type = NotionObjectType.Page, priority = null)
        store.markForIngestion(installationId = installationBId, externalNotionId = pageIdB3, type = NotionObjectType.Page, priority = null)

        val allNotionPages = setOf(pageIdA1, pageIdA2, pageIdA3, pageIdB1, pageIdB2, pageIdB3)

        assertThat(store.filterExists(installationIds = listOf(installationAId, installationBId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrderElementsOf(allNotionPages)
        assertThat(store.filterExists(installationIds = listOf(installationAId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdA1, pageIdA2, pageIdA3)
        assertThat(store.filterExists(installationIds = listOf(installationBId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdB1, pageIdB2, pageIdB3)
        assertThat(store.listObjectsMarkedForDeletion()).isEmpty()

        store.markForDeletion(installationId = installationAId, externalNotionIds = setOf(pageIdA1))

        assertThat(store.filterExists(installationIds = listOf(installationAId, installationBId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdA2, pageIdA3, pageIdB1, pageIdB2, pageIdB3)
        assertThat(store.filterExists(installationIds = listOf(installationAId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdA2, pageIdA3)
        assertThat(store.filterExists(installationIds = listOf(installationBId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdB1, pageIdB2, pageIdB3)
        assertThat(store.listObjectsMarkedForDeletion().map { it.externalNotionId }).containsExactlyInAnyOrder(pageIdA1)

        // Undo
        store.markForIngestion(installationId = installationAId, externalNotionId = pageIdA1, type = NotionObjectType.Page, priority = null)

        assertThat(store.filterExists(installationIds = listOf(installationAId, installationBId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrderElementsOf(allNotionPages)
        assertThat(store.filterExists(installationIds = listOf(installationAId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdA1, pageIdA2, pageIdA3)
        assertThat(store.filterExists(installationIds = listOf(installationBId), externalNotionIds = allNotionPages))
            .containsExactlyInAnyOrder(pageIdB1, pageIdB2, pageIdB3)
        assertThat(store.listObjectsMarkedForDeletion()).isEmpty()
    }

    @Test
    fun exists() = suspendingDatabaseTest {
        val org = makeOrg()
        val installationId = makeInstallation(org = org).idValue
        val externalNotionId = UUID.randomUUID()

        assertThat(store.exists(installationId = installationId, externalNotionId = externalNotionId)).isFalse()
        store.markForIngestion(
            installationId = installationId,
            externalNotionId = externalNotionId,
            type = NotionObjectType.Page,
            priority = null,
        )
        assertThat(store.exists(installationId = installationId, externalNotionId = externalNotionId)).isTrue()
    }

    @Test
    fun setStatusToNullForAllErroredPages() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org).idValue
        val externalNotionId1 = UUID.randomUUID()
        val externalNotionId2 = UUID.randomUUID()

        store.markForIngestion(
            installationId = installation,
            externalNotionId = externalNotionId1,
            type = NotionObjectType.Page,
            priority = null,
        )
        store.markForIngestion(
            installationId = installation,
            externalNotionId = externalNotionId2,
            type = NotionObjectType.Page,
            priority = null,
        )

        assertThat(store.getNextObjectsToIngest(installationId = installation, limit = 2).map { it.externalNotionId })
            .containsExactlyInAnyOrder(externalNotionId1, externalNotionId2)

        store.markAsErrored(installationId = installation, externalNotionId = externalNotionId1)

        assertThat(store.getNextObjectsToIngest(installationId = installation, limit = 2).map { it.externalNotionId })
            .containsExactlyInAnyOrder(externalNotionId2)

        store.markAsIngested(
            installationId = installation,
            externalNotionId = externalNotionId2,
            type = NotionObjectType.Page,
            lastEditedTime = Instant.nowWithMicrosecondPrecision(),
        )

        assertThat(store.getNextObjectsToIngest(installationId = installation, limit = 2).map { it.externalNotionId })
            .isEmpty()

        store.setStatusToNullForAllErroredPages()

        assertThat(store.getNextObjectsToIngest(installationId = installation, limit = 2).map { it.externalNotionId })
            .containsExactlyInAnyOrder(externalNotionId1)
    }

    @Test
    fun listNotionPagesNotAccessibleToIdentity() = suspendingDatabaseTest {
        val identityA = makeIdentity()
        val identityB = makeIdentity()
        val identityC = makeIdentity()

        val installationA = makeInstallation()
        val installationB = makeInstallation()

        val externalNotionId1 = UUID.randomUUID()
        val externalNotionId2 = UUID.randomUUID()
        val externalNotionId3 = UUID.randomUUID()

        store.markForIngestion(
            installationId = installationA.idValue,
            externalNotionId = externalNotionId1,
            type = NotionObjectType.Page,
            priority = null,
        )
        store.markForIngestion(
            installationId = installationA.idValue,
            externalNotionId = externalNotionId2,
            type = NotionObjectType.Page,
            priority = null,
        )
        store.markForIngestion(
            installationId = installationB.idValue,
            externalNotionId = externalNotionId3,
            type = NotionObjectType.Page,
            priority = null,
        )

        Stores.notionObjectAccessStore.upsert(identityId = identityA.idValue, externalNotionId = externalNotionId1)
        Stores.notionObjectAccessStore.upsert(identityId = identityB.idValue, externalNotionId = externalNotionId2)
        Stores.notionObjectAccessStore.upsert(identityId = identityC.idValue, externalNotionId = externalNotionId3)

        assertThat(
            store.listNotionObjectsNotAccessibleToIdentity(installationId = installationA.idValue, identityId = identityA.idValue)
                .map { it.externalNotionId },
        ).containsExactly(externalNotionId2)

        assertThat(
            store.listNotionObjectsNotAccessibleToIdentity(installationId = installationA.idValue, identityId = identityB.idValue)
                .map { it.externalNotionId },
        ).containsExactly(externalNotionId1)

        assertThat(
            store.listNotionObjectsNotAccessibleToIdentity(installationId = installationB.idValue, identityId = identityC.idValue),
        ).isEmpty()

        Stores.notionObjectAccessStore.upsert(identityId = identityA.idValue, externalNotionId = externalNotionId2)

        assertThat(
            store.listNotionObjectsNotAccessibleToIdentity(installationId = installationA.idValue, identityId = identityA.idValue),
        ).isEmpty()
    }
}
