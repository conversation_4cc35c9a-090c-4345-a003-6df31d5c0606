package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeLinearOrganization
import com.nextchaptersoftware.db.ModelBuilders.makeLinearTeam
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.LinearOrganizationDAO
import com.nextchaptersoftware.db.models.LinearTeam
import com.nextchaptersoftware.db.models.LinearTeamIngestionDAO
import com.nextchaptersoftware.db.models.LinearTeamIngestionModel
import com.nextchaptersoftware.db.models.LinearTeamIngestionStatus
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class LinearTeamIngestionStoreTest : DatabaseTestsBase() {

    private val store = Stores.linearTeamIngestionStore
    private lateinit var org: OrgDAO
    private lateinit var installation: InstallationDAO
    private lateinit var linearOrganization: LinearOrganizationDAO
    private lateinit var linearTeam: LinearTeam

    suspend fun setup() {
        org = makeOrg()
        installation = makeInstallation(org = org)
        linearOrganization = makeLinearOrganization(
            installation = installation,
        )
        linearTeam = makeLinearTeam(
            linearOrganization = linearOrganization,
        ).asDataModel()
    }

    @Test
    fun `test upsert -- insert new entry`() = suspendingDatabaseTest {
        setup()

        val latestCursor = "cursor_initial"
        val status = LinearTeamIngestionStatus.Busy

        // Call upsert to insert a new entry
        val result = store.upsert(
            linearTeamId = linearTeam.id,
            latestCursor = latestCursor,
            status = status,
        )

        // Assert the fields of the newly inserted entry
        assertThat(result.linearTeam).isEqualTo(linearTeam.id)
        assertThat(result.latestCursor).isEqualTo(latestCursor)
        assertThat(result.status).isEqualTo(status)

        // Verify the entry exists in the database
        val insertedEntry = suspendedTransaction {
            LinearTeamIngestionDAO.find { LinearTeamIngestionModel.linearTeam eq linearTeam.id }.firstOrNull()
        }
        assertThat(insertedEntry).isNotNull
        assertThat(insertedEntry?.latestCursor).isEqualTo(latestCursor)
        assertThat(insertedEntry?.status).isEqualTo(status)
    }

    @Test
    fun `test upsert -- update existing entry`() = suspendingDatabaseTest {
        setup()

        // First, insert an entry using ModelBuilders
        store.upsert(
            linearTeamId = linearTeam.id,
            latestCursor = "cursor_initial",
            status = LinearTeamIngestionStatus.Busy,
        )

        // Now, update the existing entry with a new cursor and status
        val updatedCursor = "cursor_updated"
        val updatedStatus = LinearTeamIngestionStatus.Finished

        val result = store.upsert(
            linearTeamId = linearTeam.id,
            latestCursor = updatedCursor,
            status = updatedStatus,
        )

        // Assert that the entry was updated correctly
        assertThat(result.latestCursor).isEqualTo(updatedCursor)
        assertThat(result.status).isEqualTo(updatedStatus)

        // Verify that the updated entry exists in the database
        val updatedEntry = suspendedTransaction {
            LinearTeamIngestionDAO.find { LinearTeamIngestionModel.linearTeam eq linearTeam.id }.firstOrNull()
        }
        assertThat(updatedEntry).isNotNull
        assertThat(updatedEntry?.latestCursor).isEqualTo(updatedCursor)
        assertThat(updatedEntry?.status).isEqualTo(updatedStatus)
    }

    @Test
    fun `test clearLatestCursor`() = suspendingDatabaseTest {
        setup()

        // First, insert an entry with a cursor using ModelBuilders
        store.upsert(
            linearTeamId = linearTeam.id,
            latestCursor = "cursor_initial",
            status = LinearTeamIngestionStatus.Busy,
        )

        // Now clear the latest cursor
        store.clearLatestCursor(linearTeamId = linearTeam.id)

        // Verify that the latest cursor was cleared in the database
        val clearedEntry = suspendedTransaction {
            LinearTeamIngestionDAO.find { LinearTeamIngestionModel.linearTeam eq linearTeam.id }.firstOrNull()
        }
        assertThat(clearedEntry?.latestCursor).isNull()
    }
}
