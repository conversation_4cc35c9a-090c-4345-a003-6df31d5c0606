package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class OrgMemberDecoratorTest : DatabaseTestsBase() {

    @Test
    fun `decorateOrgMember - happy path from non-primary OrgMember`() = suspendingDatabaseTest {
        val org = makeOrg()
        val person = makePerson()
        val slackOrgMember = makeOrgMember(org = org, createPerson = false)
        val slackInstallation = makeInstallation(org = org, provider = Provider.Slack)
        val githubInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val bitbucketInstallation = makeInstallation(org = org, provider = Provider.Bitbucket)
        val associatedPrimaryOrgMember = makeOrgMember(org = org, person = person)
        val bitbucketOrgMember = makeOrgMember(org = org, createPerson = false)
        val associatedPrimaryMember = makeMember(
            isPrimaryMember = true,
            isCurrentMember = true,
            orgMember = associatedPrimaryOrgMember,
            installation = githubInstallation,
        )
        val bitbucketMember = makeMember(
            orgMember = bitbucketOrgMember,
            isPrimaryMember = false,
            isCurrentMember = true,
            installation = bitbucketInstallation,
            association = associatedPrimaryMember.idValue,
        )
        val slackMember = makeMember(
            orgMember = slackOrgMember,
            installation = slackInstallation,
            isPrimaryMember = false,
            isCurrentMember = true,
            association = associatedPrimaryMember.idValue,
        )

        val orgMemberBundle = Stores.orgMemberDecorator.decorateOrgMember(orgMemberId = slackOrgMember.idValue)
        assertThat(orgMemberBundle?.orgMemberAndPerson?.orgMember?.id).isEqualTo(slackOrgMember.idValue)
        assertThat(orgMemberBundle?.orgMemberAndPerson?.person?.id).isNull()
        assertThat(orgMemberBundle?.associatedOrgMembersAndIdentities?.map { it.orgMember.id }).containsExactlyInAnyOrder(
            associatedPrimaryOrgMember.idValue,
            bitbucketOrgMember.idValue,
        )
        assertThat(orgMemberBundle?.orgMembersAndIdentities?.map { it.member.id }).containsExactlyInAnyOrder(
            slackMember.idValue,
            bitbucketMember.idValue,
            associatedPrimaryMember.idValue,
        )
        assertThat(orgMemberBundle?.displayName).isEqualTo(slackMember.identity.displayName)
    }

    @Test
    fun `decorateOrgMember - happy path from primary OrgMember`() = suspendingDatabaseTest {
        val org = makeOrg()
        val person = makePerson()
        val orgMember = makeOrgMember(org = org, person = person)
        val githubInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val slackInstallation = makeInstallation(org = org, provider = Provider.Slack)
        val bitbucketInstallation = makeInstallation(org = org, provider = Provider.Bitbucket)
        val slackOrgMember = makeOrgMember(org = org, createPerson = false)
        val bitbucketOrgMember = makeOrgMember(org = org, createPerson = false)
        val githubMember = makeMember(
            isPrimaryMember = true,
            isCurrentMember = true,
            orgMember = orgMember,
            installation = githubInstallation,
        )
        val bitbucketMember = makeMember(
            orgMember = bitbucketOrgMember,
            isPrimaryMember = false, // FIXME: in reality SCM members are always primary
            isCurrentMember = true,
            installation = bitbucketInstallation,
            association = githubMember.idValue,
        )
        val slackMember = makeMember(
            orgMember = slackOrgMember,
            isPrimaryMember = false,
            isCurrentMember = true,
            association = githubMember.idValue,
            installation = slackInstallation,
        )

        val orgMemberBundle = Stores.orgMemberDecorator.decorateOrgMember(orgMemberId = orgMember.idValue)
        assertThat(orgMemberBundle?.orgMemberAndPerson?.orgMember?.id).isEqualTo(orgMember.idValue)
        assertThat(orgMemberBundle?.orgMemberAndPerson?.person?.id).isEqualTo(person.idValue)
        assertThat(orgMemberBundle?.associatedOrgMembersAndIdentities?.map { it.orgMember.id }).containsExactlyInAnyOrder(
            bitbucketOrgMember.idValue,
            slackOrgMember.idValue,
        )
        assertThat(orgMemberBundle?.orgMembersAndIdentities?.map { it.member.id }).containsExactlyInAnyOrder(
            githubMember.idValue,
            bitbucketMember.idValue,
        )
    }

    @Test
    fun `decorateOrgMember - no associated members`() = suspendingDatabaseTest {
        val org = makeOrg()
        val person = makePerson()
        val orgMember = makeOrgMember(org = org, createPerson = false)
        val nonAssociatedOrgMember = makeOrgMember(org = org, person = person)
        makeMember(
            isPrimaryMember = true,
            isCurrentMember = true,
            orgMember = nonAssociatedOrgMember,
        )
        makeMember(
            orgMember = orgMember,
            isPrimaryMember = false,
            isCurrentMember = true,
        )

        val orgMemberBundle = Stores.orgMemberDecorator.decorateOrgMember(orgMemberId = orgMember.idValue)
        assertThat(orgMemberBundle?.orgMemberAndPerson?.orgMember?.id).isEqualTo(orgMember.idValue)
        assertThat(orgMemberBundle?.orgMemberAndPerson?.person?.id).isNull()
        assertThat(orgMemberBundle?.associatedOrgMembersAndIdentities).isEmpty()
    }

    @Test
    fun `findAssociatedScmOrgMemberIds - happy path`() = suspendingDatabaseTest {
        val org = makeOrg()
        val person = makePerson()
        val orgMember = makeOrgMember(org = org, person = person)
        val githubInstallation = makeInstallation(org = org, provider = Provider.GitHub)
        val slackInstallation = makeInstallation(org = org, provider = Provider.Slack)
        val bitbucketInstallation = makeInstallation(org = org, provider = Provider.Bitbucket)
        val slackOrgMember = makeOrgMember(org = org, createPerson = false)
        val bitbucketOrgMember = makeOrgMember(org = org, createPerson = false)
        val githubMember = makeMember(
            isPrimaryMember = true,
            isCurrentMember = true,
            orgMember = orgMember,
            installation = githubInstallation,
        )
        makeMember(
            orgMember = bitbucketOrgMember,
            isPrimaryMember = false, // FIXME: in reality SCM members are always primary
            isCurrentMember = true,
            installation = bitbucketInstallation,
            association = githubMember.idValue,
        )
        makeMember(
            orgMember = slackOrgMember,
            isPrimaryMember = false,
            isCurrentMember = true,
            association = githubMember.idValue,
            installation = slackInstallation,
        )

        val associatedOrgMemberIds = Stores.orgMemberDecorator.findAssociatedScmOrgMemberIds(orgId = org.idValue, orgMemberId = orgMember.idValue)
        assertThat(associatedOrgMemberIds).containsExactlyInAnyOrder(orgMember.idValue, bitbucketOrgMember.idValue)
    }
}
