package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeJiraSite
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class JiraGroupStoreTest : DatabaseTestsBase() {
    private val store = Stores.jiraGroupStore

    @Test
    fun `upsert, list, listGroupsForUser, and delete`() = suspendingDatabaseTest {
        val installation = makeInstallation(provider = Provider.JiraDataCenter)
        val site = makeJiraSite(installation = installation)

        assertThat(store.list(jiraSiteId = site.idValue)).isEmpty()

        val groupNameA = "test-group-A"
        val groupNameB = "test-group-B"

        store.upsert(jiraSiteId = site.idValue, groupId = groupNameA, applicationRoles = listOf("jira-software"))
        store.upsert(jiraSiteId = site.idValue, groupId = groupNameB, applicationRoles = emptyList())

        val results = store.list(jiraSiteId = site.idValue)
        assertThat(results.map { it.groupId }).containsExactlyInAnyOrder(groupNameA, groupNameB)

        // Check idempotency
        store.upsert(jiraSiteId = site.idValue, groupId = groupNameB, applicationRoles = emptyList())
        assertThat(store.list(jiraSiteId = site.idValue)).isEqualTo(results)

        val groupA = results.first { it.groupId == groupNameA }
        val groupB = results.first { it.groupId == groupNameB }

        val jiraUserId = "userA"

        assertThat(store.listGroupsForUser(installationId = installation.idValue, userId = jiraUserId)).isEmpty()

        Stores.jiraGroupMemberStore.upsert(jiraGroupId = groupA.id, userId = jiraUserId)

        assertThat(store.listGroupsForUser(installationId = installation.idValue, userId = jiraUserId)).containsExactlyInAnyOrder(groupA)

        store.delete(jiraSiteId = site.idValue, ids = listOf(groupB.id))

        assertThat(store.list(jiraSiteId = site.idValue)).containsExactlyInAnyOrder(groupA)
    }
}
