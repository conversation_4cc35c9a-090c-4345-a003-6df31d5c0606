package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeMLInference
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.interceptors.explain.SlowQueryConfig
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.McpQueryId
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class McpInferenceStoreTest : DatabaseTestsBase() {
    private val store = Stores.mcpInferenceStore

    @Test
    fun `test upsert and find`() = suspendingDatabaseTest {
        val org = makeOrg()
        val repo = makeRepo()
        val questioner = makeOrgMember(org = org)
        val mcpQueryId = McpQueryId.random()
        val mcpToolName = "tool"
        val query = "test query"
        val parameters = mapOf(
            "query" to query,
            "repo_id" to repo.idValue.toString(),
        )

        val inference = store.upsert(
            orgId = org.idValue,
            repoId = repo.idValue,
            mcpQueryId = mcpQueryId,
            mcpToolName = mcpToolName,
            orgMemberId = questioner.idValue,
            parameters = parameters,
        )

        assertThat(inference).isNotNull
        assertThat(inference.orgId).isEqualTo(org.idValue)
        assertThat(inference.repoId).isEqualTo(repo.idValue)
        assertThat(inference.questionerOrgMemberId).isEqualTo(questioner.idValue)
        assertThat(inference.mcpQueryId).isEqualTo(mcpQueryId)
        assertThat(inference.mcpToolName).isEqualTo(mcpToolName)
        assertThat(inference.parameters).isEqualTo(parameters)

        val found = store.find(org.idValue, questioner.idValue, mcpQueryId)
        assertThat(found).isNotNull
        assertThat(found).isEqualTo(inference)

        val foundWithResult = store.findWithResult(org.idValue, questioner.idValue, mcpQueryId)
        assertThat(foundWithResult?.mcpInference).isEqualTo(inference)
        assertThat(foundWithResult?.resultInference).isNull()
    }

    @Test
    fun `test upsert updates existing record`() = suspendingDatabaseTest {
        val org = makeOrg()
        val repo = makeRepo()
        val firstQuestioner = makeOrgMember(org = org)
        val mcpQueryId = McpQueryId.random()

        val first = store.upsert(
            orgId = org.idValue,
            repoId = repo.idValue,
            mcpQueryId = mcpQueryId,
            mcpToolName = "tool",
            orgMemberId = firstQuestioner.idValue,
        )

        val second = store.upsert(
            orgId = org.idValue,
            repoId = repo.idValue,
            mcpQueryId = mcpQueryId,
            mcpToolName = "updated",
            orgMemberId = firstQuestioner.idValue,
        )

        assertThat(second.id).isEqualTo(first.id)
        assertThat(second.mcpToolName).isEqualTo("updated")
    }

    @Test
    fun `test findWithResult returns resultInference when set`() = suspendingDatabaseTest {
        val org = makeOrg()
        val repo = makeRepo()
        val questioner = makeOrgMember(org = org)
        val mcpQueryId = McpQueryId.random()
        val mcpToolName = "tool"

        val mcp = store.upsert(
            orgId = org.idValue,
            repoId = repo.idValue,
            mcpQueryId = mcpQueryId,
            mcpToolName = mcpToolName,
            orgMemberId = questioner.idValue,
        )

        val ml = makeMLInference(org = org)

        store.upsert(
            orgId = org.idValue,
            repoId = repo.idValue,
            mcpQueryId = mcpQueryId,
            mcpToolName = mcpToolName,
            orgMemberId = questioner.idValue,
            resultInferenceId = ml.id.value,
        )

        val withResult = store.findWithResult(org.idValue, questioner.idValue, mcpQueryId)
        assertThat(withResult?.mcpInference?.id).isEqualTo(mcp.id)
        assertThat(withResult?.resultInference).isEqualTo(ml.asDataModel())
    }

    @Test
    fun `test findByResultInferenceId`() = suspendingDatabaseTest {
        val org = makeOrg()
        val repo = makeRepo()
        val questioner = makeOrgMember(org = org)
        val mcpQueryId = McpQueryId.random()
        val mcpToolName = "tool"
        val ml = makeMLInference(org = org)

        val mcp = store.upsert(
            orgId = org.idValue,
            repoId = repo.idValue,
            mcpQueryId = mcpQueryId,
            mcpToolName = mcpToolName,
            orgMemberId = questioner.idValue,
            resultInferenceId = ml.id.value,
        )

        val found = store.findByResultInferenceId(ml.id.value)
        assertThat(found).isNotNull
        assertThat(found).isEqualTo(mcp)

        // Test with non-existent result inference ID
        val notFound = store.findByResultInferenceId(MLInferenceId.random())
        assertThat(notFound).isNull()
    }

    @Test
    fun `test delete`() = suspendingDatabaseTest {
        val org = makeOrg()
        val repo = makeRepo()
        val questioner = makeOrgMember(org = org)
        val mcpQueryId = McpQueryId.random()
        val mcpToolName = "tool"
        store.upsert(
            orgId = org.idValue,
            repoId = repo.idValue,
            mcpQueryId = mcpQueryId,
            mcpToolName = mcpToolName,
            orgMemberId = questioner.idValue,
        )
        store.delete(
            orgId = org.idValue,
            orgMemberId = questioner.idValue,
            mcpQueryId = mcpQueryId,
        )
        assertThat(store.find(org.idValue, questioner.idValue, mcpQueryId)).isNull()
    }

    @Test
    fun `test all returns all McpInference records`() = suspendingDatabaseTest({
        slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
    }) {
        val org1 = makeOrg()
        val org2 = makeOrg()
        val repo = makeRepo()
        val questioner1 = makeOrgMember(org = org1)
        val questioner2 = makeOrgMember(org = org2)

        // Create multiple MCP inferences across different orgs
        val mcp1 = store.upsert(
            orgId = org1.idValue,
            repoId = repo.idValue,
            mcpQueryId = McpQueryId.random(),
            mcpToolName = "tool1",
            orgMemberId = questioner1.idValue,
        )

        val mcp2 = store.upsert(
            orgId = org1.idValue,
            repoId = repo.idValue,
            mcpQueryId = McpQueryId.random(),
            mcpToolName = "tool2",
            orgMemberId = questioner1.idValue,
        )

        val mcp3 = store.upsert(
            orgId = org2.idValue,
            repoId = repo.idValue,
            mcpQueryId = McpQueryId.random(),
            mcpToolName = "tool3",
            orgMemberId = questioner2.idValue,
        )

        val allInferences = store.all()

        assertThat(allInferences).hasSize(3)
        assertThat(allInferences.map { it.id }).containsExactlyInAnyOrder(mcp1.id, mcp2.id, mcp3.id)
        assertThat(allInferences.map { it.mcpToolName }).containsExactlyInAnyOrder("tool1", "tool2", "tool3")
    }
}
