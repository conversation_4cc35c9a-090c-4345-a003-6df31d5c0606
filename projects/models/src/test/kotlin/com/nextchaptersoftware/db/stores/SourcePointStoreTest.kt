package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeFilePoint
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSha1
import com.nextchaptersoftware.db.ModelBuilders.makeSourceMark
import com.nextchaptersoftware.db.ModelBuilders.makeSourcePoint
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.FilePoint
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Point
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.SourceMarkDAO
import com.nextchaptersoftware.db.models.SourcePoint
import com.nextchaptersoftware.db.models.SourcePointDAO
import com.nextchaptersoftware.db.models.SourcePointId
import com.nextchaptersoftware.db.models.SourcePointModel
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.models.SourceSnippet
import com.nextchaptersoftware.models.SourceSnippetBlob
import com.nextchaptersoftware.types.Hash
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SourcePointStoreTest : DatabaseTestsBase() {
    private val store = Stores.sourcePointStore

    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var repo: RepoDAO
    private lateinit var member: MemberDAO
    private lateinit var thread: ThreadDAO
    private lateinit var sourceMark: SourceMarkDAO

    private suspend fun setup() {
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        repo = makeRepo(scmTeam = scmTeam)
        member = makeMember(scmTeam = scmTeam)
        thread = makeThread(org = org, author = member)
        sourceMark = makeSourceMark(scmTeam = scmTeam, repo = repo, thread = thread)
    }

    @Test
    fun getOriginalSourcePointForThread() = suspendingDatabaseTest {
        setup()
        val threadId = thread.idValue

        makeSourcePoint(sourceMark = sourceMark, isOriginal = false, isTrusted = true)
        assertThat(store.getOriginalSourcePointForThread(threadId)).isNull()

        val sourcePointB = makeSourcePoint(sourceMark = sourceMark, isOriginal = true)
        makeSourcePoint(sourceMark = sourceMark, isOriginal = true, isTrusted = false)
        assertThat(store.getOriginalSourcePointForThread(threadId)?.id).isEqualTo(sourcePointB.id.value)

        val sourcePointC = makeSourcePoint(sourceMark = sourceMark, isOriginal = true, isTrusted = true)
        assertThat(store.getOriginalSourcePointForThread(threadId)?.id).isEqualTo(sourcePointC.id.value)
    }

    @Test
    fun `createSourcePoints does not create duplicate points`() = suspendingDatabaseTest {
        setup()
        val point1 = makeSourcePoint(sourceMark = sourceMark).asDataModel() as SourcePoint

        store.createSourcePoints(
            trx = null,
            listOf(
                point1.copy(id = SourcePointId.random()),
                point1.copy(id = SourcePointId.random(), lineRange = point1.lineRange.first until point1.lineRange.last),
                point1.copy(id = SourcePointId.random(), isOriginal = !point1.isOriginal),
                point1.copy(id = SourcePointId.random(), snippetBlob = SourceSnippetBlob.fromSourceSnippet(SourceSnippet.emptySnippet())),
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(1)
        assertThat(allPoints.first().id).isEqualTo(point1.id)
    }

    @Test
    fun `createSourcePoints does not create duplicate file points`() = suspendingDatabaseTest {
        setup()
        val point1 = makeFilePoint(sourceMark = sourceMark).asDataModel() as FilePoint

        store.createSourcePoints(
            trx = null,
            listOf(
                point1.copy(id = SourcePointId.random()),
                point1.copy(id = SourcePointId.random(), isOriginal = !point1.isOriginal),
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(1)
        assertThat(allPoints.first().id).isEqualTo(point1.id)
    }

    @Test
    fun `createSourcePoints persists points with different commitHash`() = suspendingDatabaseTest {
        setup()
        val point1 = makeSourcePoint(sourceMark = sourceMark).asDataModel() as SourcePoint
        val newUniquePointId = SourcePointId.random()

        suspendedTransaction {
            store.createSourcePoints(
                trx = this,
                listOf(
                    point1.copy(id = SourcePointId.random()),
                    point1.copy(id = newUniquePointId, commitHash = Hash(makeSha1())),
                ),
            )
        }

        val allPoints = suspendedTransaction {
            SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        }
        assertThat(allPoints).hasSize(2)
        assertThat(allPoints.map { it.id }).containsExactlyInAnyOrder(point1.id, newUniquePointId)
    }

    @Test
    fun `createSourcePoints persists points with different fileHash`() = suspendingDatabaseTest {
        setup()
        val point1 = makeSourcePoint(sourceMark = sourceMark).asDataModel() as SourcePoint
        val newUniquePointId = SourcePointId.random()

        store.createSourcePoints(
            trx = null,
            listOf(
                point1.copy(id = SourcePointId.random()),
                point1.copy(id = newUniquePointId, fileHash = Hash(makeSha1())),
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(2)
        assertThat(allPoints.map { it.id }).containsExactlyInAnyOrder(point1.id, newUniquePointId)
    }

    @Test
    fun `createSourcePoints persists points with different filePath`() = suspendingDatabaseTest {
        setup()
        val point1 = makeSourcePoint(sourceMark = sourceMark).asDataModel() as SourcePoint
        val newUniquePointId = SourcePointId.random()

        store.createSourcePoints(
            trx = null,
            listOf(
                point1.copy(id = SourcePointId.random()),
                point1.copy(id = newUniquePointId, filePath = "src/${point1.filePath}"),
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(2)
        assertThat(allPoints.map { it.id }).containsExactlyInAnyOrder(point1.id, newUniquePointId)
    }

    @Test
    fun `createSourcePoints persists points with different isTrusted`() = suspendingDatabaseTest {
        setup()
        val point1 = makeSourcePoint(sourceMark = sourceMark).asDataModel() as SourcePoint
        val newUniquePointId = SourcePointId.random()

        store.createSourcePoints(
            trx = null,
            listOf(
                point1.copy(id = SourcePointId.random()),
                point1.copy(id = newUniquePointId, isTrusted = !point1.isTrusted),
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(2)
        assertThat(allPoints.map { it.id }).containsExactlyInAnyOrder(point1.id, newUniquePointId)
    }

    @Test
    fun `createSourcePoints with no snippet`() = suspendingDatabaseTest {
        setup()
        val pointWithNoSnippet = SourcePoint(
            id = SourcePointId.random(),
            markId = sourceMark.id.value,
            commitHash = Hash(makeSha1()),
            fileHash = Hash(makeSha1()),
            filePath = "src/module/file.kt",
            lineRange = 103..109,
            isOriginal = false,
            isTrusted = true,
            snippetBlob = null,
        )

        store.createSourcePoints(
            trx = null,
            listOf(
                pointWithNoSnippet,
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(1)
        assertThat((allPoints.first() as SourcePoint).snippetBlob).isNull()
        assertThat((allPoints.first() as SourcePoint).snippet).isNull()
    }

    @Test
    fun `createSourcePoints with snippet`() = suspendingDatabaseTest {
        setup()
        val pointWithSnippet = SourcePoint(
            id = SourcePointId.random(),
            markId = sourceMark.id.value,
            commitHash = Hash(makeSha1()),
            fileHash = Hash(makeSha1()),
            filePath = "src/module/file.kt",
            lineRange = 103..103,
            isOriginal = false,
            isTrusted = true,
            snippetBlob = SourceSnippetBlob.fromSourceSnippet(
                SourceSnippet(
                    firstLine = 102,
                    listOf("foo", "bar", "baz"),
                ),
            ),
        )

        store.createSourcePoints(
            trx = null,
            listOf(
                pointWithSnippet,
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(1)
        assertThat((allPoints.first() as SourcePoint).snippetBlob).isNotNull
        assertThat((allPoints.first() as SourcePoint).snippet).isEqualTo(
            SourceSnippet(
                firstLine = 102,
                listOf("foo", "bar", "baz"),
            ),
        )
    }

    @Test
    fun `createSourcePoints with filepoint`() = suspendingDatabaseTest {
        setup()
        val filePoint = FilePoint(
            id = SourcePointId.random(),
            markId = sourceMark.id.value,
            commitHash = Hash(makeSha1()),
            fileHash = Hash(makeSha1()),
            filePath = "src/module/file.kt",
            isOriginal = false,
        )

        store.createSourcePoints(
            trx = null,
            listOf(
                filePoint,
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(1)
        assertThat(allPoints).allMatch { it is FilePoint }
    }

    @Test
    fun `createSourcePointsByMark with no snippet`() = suspendingDatabaseTest {
        setup()
        val pointWithNoSnippet = SourcePoint(
            id = SourcePointId.random(),
            markId = sourceMark.id.value,
            commitHash = Hash(makeSha1()),
            fileHash = Hash(makeSha1()),
            filePath = "src/module/file.kt",
            lineRange = 103..109,
            isOriginal = false,
            isTrusted = true,
            snippetBlob = null,
        )

        store.createSourcePointsByMark(
            trx = null,
            pointsByMark = listOf(
                sourceMark.id.value to listOf(pointWithNoSnippet as Point),
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(1)
        assertThat((allPoints.first() as SourcePoint).snippetBlob).isNull()
        assertThat((allPoints.first() as SourcePoint).snippet).isNull()
    }

    @Test
    fun `createSourcePointsByMark with snippet`() = suspendingDatabaseTest {
        setup()
        val pointWithNoSnippet = SourcePoint(
            id = SourcePointId.random(),
            markId = sourceMark.id.value,
            commitHash = Hash(makeSha1()),
            fileHash = Hash(makeSha1()),
            filePath = "src/module/file.kt",
            lineRange = 102..104,
            isOriginal = false,
            isTrusted = true,
            snippetBlob = SourceSnippetBlob.fromSourceSnippet(
                SourceSnippet(
                    firstLine = 102,
                    listOf("foo", "bar", "baz"),
                ),
            ),
        )

        store.createSourcePointsByMark(
            trx = null,
            pointsByMark = listOf(
                sourceMark.id.value to listOf(pointWithNoSnippet as Point),
            ),
        )

        val allPoints = SourcePointDAO.find { SourcePointModel.mark eq sourceMark.id.value }.map { it.asDataModel() }
        assertThat(allPoints).hasSize(1)
        assertThat((allPoints.first() as SourcePoint).snippetBlob).isNotNull
        assertThat((allPoints.first() as SourcePoint).snippet).isEqualTo(
            SourceSnippet(
                firstLine = 102,
                listOf("foo", "bar", "baz"),
            ),
        )
    }
}
