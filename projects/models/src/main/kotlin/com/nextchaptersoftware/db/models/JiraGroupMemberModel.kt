@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object JiraGroupMemberModel : ServiceModel<JiraGroupMemberId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val jiraGroup = reference(
        name = "jira_group",
        foreign = JiraGroupModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val userId = text(name = "userId")

    init {
        uniqueIndex(jiraGroup, userId)
    }
}

fun ResultRow.toJiraGroupMember(alias: String? = null) = JiraGroupMemberDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toJiraGroupMemberOrNull(alias: String? = null) = JiraGroupMemberDAO.wrapRowOrNull(this, alias)?.asDataModel()

class JiraGroupMemberDAO(id: EntityID<JiraGroupMemberId>) : EntityExtensions<JiraGroupMember, JiraGroupMemberId>(id) {
    companion object : EntityClassExtensions<JiraGroupMemberId, JiraGroupMemberDAO>(JiraGroupMemberModel)

    override var createdAt by JiraGroupMemberModel.createdAt
    override var modifiedAt by JiraGroupMemberModel.modifiedAt

    var jiraGroup by JiraGroupDAO referencedOn JiraGroupMemberModel.jiraGroup
    var userId by JiraGroupMemberModel.userId

    override fun asDataModel() = readValues.let { row ->
        JiraGroupMember(
            id = row[JiraGroupMemberModel.id].value,
            createdAt = row[JiraGroupMemberModel.createdAt],
            modifiedAt = row[JiraGroupMemberModel.modifiedAt],
            jiraGroupId = row[JiraGroupMemberModel.jiraGroup].value,
            userId = row[JiraGroupMemberModel.userId],
        )
    }
}

object JiraGroupMemberIdConverter : ValueIdConverter<JiraGroupMemberId> {
    override val factory = ::JiraGroupMemberId
    override val extract = JiraGroupMemberId::value
}

internal object JiraGroupMemberIdSerializer : ValueIdSerializer<JiraGroupMemberId>(
    serialName = "JiraGroupMemberId",
    converter = JiraGroupMemberIdConverter,
)

@Serializable(with = JiraGroupMemberIdSerializer::class)
data class JiraGroupMemberId(val value: UUID) : ValueId {

    companion object : ValueIdClass<JiraGroupMemberId>(
        converter = JiraGroupMemberIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is JiraGroupMemberId)
        return value.compareTo(other.value)
    }
}

data class JiraGroupMember(
    val id: JiraGroupMemberId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val jiraGroupId: JiraGroupId,
    val userId: String,
)
