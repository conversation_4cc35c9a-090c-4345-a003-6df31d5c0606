package com.nextchaptersoftware.db.common.schema

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.common.Database.transactionSync
import com.nextchaptersoftware.db.common.SchemaManager
import com.nextchaptersoftware.db.common.graph.TableDepthGraph
import com.nextchaptersoftware.db.migration.FlywayMigrator
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.Table

data class SchemaUpdateConfig(
    /**
     * Create custom stored procedures:
     * 1. Sets modifiedAt on row updates
     */
    var createStoredProcedures: Boolean = true,
    /**
     * Creates custom DB extension
     * 1. Creates pgtrm extension for trigram search matches
     */
    var createExtensions: Boolean = true,
    /**
     * Creates custom triggers
     * 1. Creates a trigger to set modifiedAt on row updates
     */
    var createTriggers: Boolean = true,
)

interface SchemaUpdateService {
    fun updateSchema(
        vararg tables: Table,
        db: org.jetbrains.exposed.sql.Database? = null,
        configure: SchemaUpdateConfig.() -> Unit = {},
    )
}

class SerialSchemaUpdateService : SchemaUpdateService {
    @Suppress("LoopWithTooManyJumpStatements")
    override fun updateSchema(vararg tables: Table, db: Database?, configure: SchemaUpdateConfig.() -> Unit) {
        val schemaUpdateConfig = SchemaUpdateConfig().apply(configure)
        if (schemaUpdateConfig.createStoredProcedures) {
            SchemaLockServiceProvider.SCHEMA_LOCK_SERVICE.withSchemaLock(db = db, operation = "CREATE STORED PROCEDURES") {
                SchemaManager.createStoredProcedures(this)
            }
        }
        if (schemaUpdateConfig.createExtensions) {
            SchemaLockServiceProvider.SCHEMA_LOCK_SERVICE.withSchemaLock(db = db, operation = "CREATE EXTENSIONS") {
                SchemaManager.createExtensions(this)
            }
        }

        // Need to ensure table dependency graph is respected
        val sortedTables = SchemaUtils.sortTablesByReferences(tables.toList())

        // WARNING: Do NOT use a single transaction for updating all table schemas as it can lead to deadlocks.
        // https://tech.polyconseil.fr/database-schema-migrations-lock.html#id5
        for (table in sortedTables) {
            val status = SchemaLockServiceProvider.SCHEMA_LOCK_SERVICE.withSchemaLock(
                db = db,
                operation = "UPDATE SCHEMA ${table.tableName}",
            ) {
                SchemaManager.createMissingTablesAndColumns(table, inBatch = false)
                SchemaManager.createPgtrmIndices(table)
                if (schemaUpdateConfig.createTriggers) {
                    SchemaManager.createTriggers(trx = this, table)
                }
            }
            when (status) {
                SchemaStatus.Busy -> break
                SchemaStatus.Finished -> continue
            }
        }

        // Validate schema alterations after schema updates
        transactionSync(db = db) {
            SchemaManager.validateSchemaUpdates(trx = this, *tables)
        }
    }
}

class SerialDepthSchemaUpdateService : SchemaUpdateService {
    companion object {
        const val DEPTH_BATCH_SIZE = 50
    }

    @Suppress("LoopWithTooManyJumpStatements")
    override fun updateSchema(vararg tables: Table, db: Database?, configure: SchemaUpdateConfig.() -> Unit) {
        val schemaUpdateConfig = SchemaUpdateConfig().apply(configure)
        if (schemaUpdateConfig.createStoredProcedures) {
            SchemaLockServiceProvider.SCHEMA_LOCK_SERVICE.withSchemaLock(db = db, operation = "CREATE STORED PROCEDURES") {
                SchemaManager.createStoredProcedures(this)
            }
        }
        if (schemaUpdateConfig.createExtensions) {
            SchemaLockServiceProvider.SCHEMA_LOCK_SERVICE.withSchemaLock(db = db, operation = "CREATE EXTENSIONS") {
                SchemaManager.createExtensions(this)
            }
        }

        // Need to ensure table dependency graph is respected
        val tableGraph = TableDepthGraph(tables.asList())
        val tablesByDepth = tableGraph.depthMap()

        // WARNING: Do NOT use a single transaction for updating all table schemas as it can lead to deadlocks.
        // https://tech.polyconseil.fr/database-schema-migrations-lock.html#id5
        for ((depth, depthTables) in tablesByDepth) { // Process depth levels in order
            val tableBatches = depthTables.chunked(DEPTH_BATCH_SIZE) // Split tables into batches of 10

            for ((batchIndex, batch) in tableBatches.withIndex()) {
                val status = SchemaLockServiceProvider.SCHEMA_LOCK_SERVICE.withSchemaLock(
                    db = db,
                    operation = "UPDATE SCHEMA AT DEPTH $depth - BATCH $batchIndex",
                ) {
                    SchemaManager.createMissingTablesAndColumns(tables = batch.toTypedArray(), inBatch = false)
                    SchemaManager.createPgtrmIndices(tables = batch.toTypedArray())
                    if (schemaUpdateConfig.createTriggers) {
                        SchemaManager.createTriggers(trx = this, tables = batch.toTypedArray())
                    }
                }
                when (status) {
                    SchemaStatus.Busy -> break
                    SchemaStatus.Finished -> continue
                }
            }
        }

        // Validate schema alterations after schema updates
        transactionSync(db = db) {
            SchemaManager.validateSchemaUpdates(trx = this, *tables)
        }
    }
}

class BatchSchemaUpdateService : SchemaUpdateService {
    override fun updateSchema(vararg tables: Table, db: Database?, configure: SchemaUpdateConfig.() -> Unit) {
        SchemaLockServiceProvider.SCHEMA_LOCK_SERVICE.withSchemaLock(operation = "Update Schemas") {
            val schemaUpdateConfig = SchemaUpdateConfig().apply(configure)
            if (schemaUpdateConfig.createStoredProcedures) {
                SchemaManager.createStoredProcedures(this)
            }
            if (schemaUpdateConfig.createExtensions) {
                SchemaManager.createExtensions(this)
            }
            SchemaManager.createMissingTablesAndColumns(tables = tables, inBatch = false)
            SchemaManager.createPgtrmIndices(tables = tables)
            if (schemaUpdateConfig.createTriggers) {
                SchemaManager.createTriggers(trx = this, tables = tables)
            }

            // Validate schema alterations after schema updates
            SchemaManager.validateSchemaUpdates(trx = this, *tables)
        }
    }
}

class FlywaySchemaUpdateService : SchemaUpdateService {
    private val flywayMigrator by lazy {
        FlywayMigrator(dbConfigProvider = com.nextchaptersoftware.db.common.Database.DB_CONFIG_PROVIDER)
    }

    private val flywayMigrationConfig by lazy {
        GlobalConfig.INSTANCE.database.flywayMigration
    }

    override fun updateSchema(vararg tables: Table, db: Database?, configure: SchemaUpdateConfig.() -> Unit) {
        flywayMigrator.runMigration(flywayMigrationConfig = flywayMigrationConfig)
    }
}
