@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.DbOrdinal
import com.nextchaptersoftware.utils.CollectionsUtils.combinations
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable(with = EmbeddingModelSerializer::class)
enum class EmbeddingModel(
    override val dbOrdinal: Int,
    val dimension: Int,
) : DbOrdinal {
    E5Mistral(
        dbOrdinal = 1,
        dimension = 4096,
    ),
    ;

    companion object {
        val DEFAULT = E5Mistral

        val COMBINATIONS by lazy {
            entries.flatMapIndexed { index, _ ->
                entries.combinations(index + 1)
            }
        }
    }
}

internal object EmbeddingModelSerializer : KSerializer<EmbeddingModel> {
    override val descriptor: SerialDescriptor
        get() = PrimitiveSerialDescriptor("EmbeddingModel", PrimitiveKind.INT)

    override fun serialize(encoder: Encoder, value: EmbeddingModel) {
        encoder.encodeInt(value.dbOrdinal)
    }

    override fun deserialize(decoder: Decoder): EmbeddingModel {
        val dbOrdinal = decoder.decodeInt()
        return EmbeddingModel.entries.first { it.dbOrdinal == dbOrdinal }
    }
}
