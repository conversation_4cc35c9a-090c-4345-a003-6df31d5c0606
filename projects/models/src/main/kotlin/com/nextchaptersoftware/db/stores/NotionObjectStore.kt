package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.OperatorExtensions.isNullOrFalse
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.NotionObject
import com.nextchaptersoftware.db.models.NotionObjectAccessModel
import com.nextchaptersoftware.db.models.NotionObjectDAO
import com.nextchaptersoftware.db.models.NotionObjectId
import com.nextchaptersoftware.db.models.NotionObjectIngestionStatus
import com.nextchaptersoftware.db.models.NotionObjectModel
import com.nextchaptersoftware.db.models.NotionObjectType
import com.nextchaptersoftware.db.models.toNotionObject
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import java.util.UUID
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.notInSubQuery
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.update
import org.jetbrains.exposed.sql.upsert

class NotionObjectStore internal constructor() {
    companion object {
        private const val BATCH_SIZE = 20
    }

    private suspend fun upsert(
        installationId: InstallationId,
        externalNotionId: UUID,
        type: NotionObjectType,
        status: NotionObjectIngestionStatus?,
        priority: Int?,
        lastEditedTime: Instant? = null,
    ) {
        suspendedTransaction {
            NotionObjectModel.upsert(
                keys = arrayOf(NotionObjectModel.installation, NotionObjectModel.externalNotionId),
                onUpdateExclude = NotionObjectModel.columns - setOfNotNull(
                    NotionObjectModel.status, // always set this value even if null
                    NotionObjectModel.priority, // always set this value even if null
                    NotionObjectModel.markForDeletion,
                    lastEditedTime?.let { NotionObjectModel.lastEditedTime },
                ),
            ) { insertStatement ->
                insertStatement[this.installation] = installationId
                insertStatement[this.externalNotionId] = externalNotionId
                insertStatement[this.type] = type
                insertStatement[this.status] = status
                insertStatement[this.priority] = priority
                insertStatement[this.markForDeletion] = null // clear if set
                lastEditedTime?.let { insertStatement[this.lastEditedTime] = it }
            }
        }
    }

    suspend fun markForIngestion(
        installationId: InstallationId,
        externalNotionId: UUID,
        type: NotionObjectType,
        priority: Int?,
    ) {
        upsert(
            installationId = installationId,
            externalNotionId = externalNotionId,
            type = type,
            status = null,
            priority = priority,
        )
    }

    suspend fun markAsIngested(
        installationId: InstallationId,
        externalNotionId: UUID,
        type: NotionObjectType,
        lastEditedTime: Instant,
    ) {
        upsert(
            installationId = installationId,
            externalNotionId = externalNotionId,
            type = type,
            status = NotionObjectIngestionStatus.Ingested,
            priority = null,
            lastEditedTime = lastEditedTime,
        )
    }

    suspend fun markAsErrored(
        installationId: InstallationId,
        externalNotionId: UUID,
    ) {
        suspendedTransaction {
            NotionObjectDAO.find {
                AllOp(
                    NotionObjectModel.installation eq installationId,
                    NotionObjectModel.externalNotionId eq externalNotionId,
                )
            }.limit(1).firstOrNull()?.also {
                it.status = NotionObjectIngestionStatus.Error
                it.priority = null
            }
        }
    }

    suspend fun getNextObjectsToIngest(
        installationId: InstallationId,
        limit: Int = BATCH_SIZE,
    ): List<NotionObject> {
        return suspendedTransaction {
            NotionObjectDAO.find {
                AllOp(
                    NotionObjectModel.installation eq installationId,
                    NotionObjectModel.status.isNull(),
                    NotionObjectModel.markForDeletion.isNullOrFalse(),
                )
            }
                .limit(limit)
                .orderBy(NotionObjectModel.priority to SortOrder.DESC_NULLS_LAST)
                .map { it.asDataModel() }
        }
    }

    suspend fun delete(
        installationId: InstallationId,
        ids: List<NotionObjectId>,
    ) {
        if (ids.isEmpty()) {
            return
        }

        suspendedTransaction {
            NotionObjectModel.deleteWhere {
                AllOp(
                    this.id inList ids,
                    this.installation eq installationId,
                )
            }
        }
    }

    suspend fun lastEditedTime(
        installationId: InstallationId,
        externalNotionId: UUID,
    ): Instant? {
        return suspendedTransaction {
            NotionObjectModel
                .select(NotionObjectModel.lastEditedTime)
                .whereAll(
                    NotionObjectModel.installation eq installationId,
                    NotionObjectModel.externalNotionId eq externalNotionId,
                )
                .limit(1)
                .firstOrNull()
                ?.let { it[NotionObjectModel.lastEditedTime] }
        }
    }

    suspend fun exists(
        installationId: InstallationId,
        externalNotionId: UUID,
    ): Boolean {
        return suspendedTransaction {
            NotionObjectModel
                .select(NotionObjectModel.id)
                .whereAll(
                    NotionObjectModel.installation eq installationId,
                    NotionObjectModel.externalNotionId eq externalNotionId,
                )
                .firstOrNull() != null
        }
    }

    suspend fun setStatusToNullForAllErroredPages() {
        suspendedTransaction {
            NotionObjectModel.update(
                {
                    NotionObjectModel.status eq NotionObjectIngestionStatus.Error
                },
            ) {
                it[this.status] = null
            }
        }
    }

    suspend fun filterExists(
        installationIds: List<InstallationId>,
        externalNotionIds: Set<UUID>,
    ): Set<UUID> {
        if (installationIds.isEmpty() || externalNotionIds.isEmpty()) {
            return emptySet()
        }

        return suspendedTransaction {
            NotionObjectModel
                .select(NotionObjectModel.externalNotionId)
                .whereAll(
                    NotionObjectModel.installation inList installationIds,
                    NotionObjectModel.externalNotionId inList externalNotionIds,
                    NotionObjectModel.markForDeletion.isNullOrFalse(),
                )
                .map { it[NotionObjectModel.externalNotionId] }
                .toSet()
        }
    }

    suspend fun markForDeletion(
        installationId: InstallationId,
        externalNotionIds: Set<UUID>,
    ) {
        suspendedTransaction {
            NotionObjectModel.update(
                {
                    AllOp(
                            NotionObjectModel.installation eq installationId,
                            NotionObjectModel.externalNotionId inList externalNotionIds,
                    )
                },
            ) {
                it[this.markForDeletion] = true
            }
        }
    }

    suspend fun listObjectsMarkedForDeletion(
        limit: Int = 1000,
    ): List<NotionObject> {
        return suspendedTransaction {
            NotionObjectDAO.find {
                NotionObjectModel.markForDeletion eq true
            }.limit(limit).map { it.asDataModel() }
        }
    }

    suspend fun listNotionObjectsNotAccessibleToIdentity(
        installationId: InstallationId,
        identityId: IdentityId,
    ): List<NotionObject> {
        val subquery = NotionObjectAccessModel
            .select(NotionObjectAccessModel.externalNotionId)
            .where(NotionObjectAccessModel.identity eq identityId)

        return suspendedTransaction {
            NotionObjectModel
                .select(NotionObjectModel.columns)
                .whereAll(
                    NotionObjectModel.installation eq installationId,
                    NotionObjectModel.externalNotionId notInSubQuery subquery,
                )
                .map { it.toNotionObject() }
        }
    }
}
