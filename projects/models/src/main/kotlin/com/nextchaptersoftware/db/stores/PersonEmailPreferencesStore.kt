@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.PersonEmailPreferences
import com.nextchaptersoftware.db.models.PersonEmailPreferencesDAO
import com.nextchaptersoftware.db.models.PersonEmailPreferencesModel
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.Transaction

class PersonEmailPreferencesStore internal constructor() {

    suspend fun upsert(
        trx: Transaction? = null,
        person: PersonDAO,
        digestEmails: Boolean? = null,
        threadInviteEmails: Boolean? = null,
        recommendedInviteeEmails: Boolean? = null,
    ): PersonEmailPreferencesDAO = suspendedTransaction(trx = trx) {
        PersonEmailPreferencesDAO.find {
            AllOp(
                PersonEmailPreferencesModel.person eq person.id,
            )
        }.firstOrNull()?.let { dao ->
            threadInviteEmails?.let { dao.threadInviteEmails = it }
            digestEmails?.let { dao.digestEmails = it }
            recommendedInviteeEmails?.let { dao.recommendedInviteeEmails = it }
            dao
        } ?: PersonEmailPreferencesDAO.new {
            this.person = person

            threadInviteEmails?.let { this.threadInviteEmails = it }
            digestEmails?.let { this.digestEmails = it }
            recommendedInviteeEmails?.let { this.recommendedInviteeEmails = it }
        }
    }

    suspend fun getPreferencesByPersonId(
        trx: Transaction? = null,
        personId: PersonId,
    ): PersonEmailPreferences = suspendedTransaction(trx = trx) {
        PersonEmailPreferencesDAO.find {
            AllOp(
                PersonEmailPreferencesModel.person eq personId,
            )
        }.firstOrNull()?.asDataModel() ?: PersonEmailPreferences.getDefault(personId)
    }
}
