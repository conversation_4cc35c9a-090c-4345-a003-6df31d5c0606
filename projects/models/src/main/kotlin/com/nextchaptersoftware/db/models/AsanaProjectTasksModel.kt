@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object AsanaProjectTasksModel : ServiceModel<AsanaProjectTaskId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val project = reference(
        name = "project",
        foreign = AsanaProjectModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val task = reference(
        name = "task",
        foreign = AsanaTaskModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    init {
        uniqueIndex(project, task)
        index(isUnique = false, task)
    }
}

fun ResultRow.toAsanaProjectTask(alias: String? = null) = AsanaProjectTaskDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toAsanaProjectTaskOrNull(alias: String? = null) =
    AsanaProjectTaskDAO.wrapRowOrNull(this, alias)?.asDataModel()

class AsanaProjectTaskDAO(id: EntityID<AsanaProjectTaskId>) :
    EntityExtensions<AsanaProjectTask, AsanaProjectTaskId>(id) {

    companion object : EntityClassExtensions<AsanaProjectTaskId, AsanaProjectTaskDAO>(AsanaProjectTasksModel)

    override var createdAt by AsanaProjectTasksModel.createdAt
    override var modifiedAt by AsanaProjectTasksModel.modifiedAt

    var project by AsanaProjectDAO referencedOn AsanaProjectTasksModel.project
    var task by AsanaTaskDAO referencedOn AsanaProjectTasksModel.task

    override fun asDataModel() = readValues.let { row ->
        AsanaProjectTask(
            id = row[AsanaProjectTasksModel.id].value,
            projectId = row[AsanaProjectTasksModel.project].value,
            taskId = row[AsanaProjectTasksModel.task].value,
            createdAt = row[AsanaProjectTasksModel.createdAt],
            modifiedAt = row[AsanaProjectTasksModel.modifiedAt],
        )
    }
}

object AsanaProjectTaskIdConverter : ValueIdConverter<AsanaProjectTaskId> {
    override val factory = ::AsanaProjectTaskId
    override val extract = AsanaProjectTaskId::value
}

internal object AsanaProjectTaskIdSerializer : ValueIdSerializer<AsanaProjectTaskId>(
    serialName = "AsanaProjectTaskId",
    converter = AsanaProjectTaskIdConverter,
)

@Serializable(with = AsanaProjectTaskIdSerializer::class)
data class AsanaProjectTaskId(val value: UUID) : ValueId {

    companion object : ValueIdClass<AsanaProjectTaskId>(
        converter = AsanaProjectTaskIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is AsanaProjectTaskId)
        return value.compareTo(other.value)
    }
}

data class AsanaProjectTask(
    val id: AsanaProjectTaskId,
    val projectId: AsanaProjectId,
    val taskId: AsanaTaskId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
)
