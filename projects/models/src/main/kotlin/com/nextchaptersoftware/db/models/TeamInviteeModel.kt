@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.DbOrdinal
import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object TeamInviteeModel : ServiceModel<TeamInviteeId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val senderOrgMember = reference(
        name = "senderOrgMember",
        foreign = OrgMemberModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()
    val inviteeOrgMember = reference(
        name = "inviteeOrgMember",
        foreign = OrgMemberModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()
    val eventType = enumerationByDbOrdinal(
        name = "eventType",
        klass = TeamInviteeEventType::class,
    )

    init {
        uniqueIndex(senderOrgMember, inviteeOrgMember)
    }
}

enum class TeamInviteeEventType(
    override val dbOrdinal: Int,
) : DbOrdinal {
    Sent(dbOrdinal = 1),
    Dismissed(dbOrdinal = 2),
}

fun ResultRow.toTeamInvitee(alias: String? = null) = TeamInviteeDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toTeamInviteeOrNull(alias: String? = null) = TeamInviteeDAO.wrapRowOrNull(this, alias)?.asDataModel()

class TeamInviteeDAO(id: EntityID<TeamInviteeId>) : EntityExtensions<TeamInvitee, TeamInviteeId>(id) {
    companion object : EntityClassExtensions<TeamInviteeId, TeamInviteeDAO>(TeamInviteeModel)

    override var createdAt by TeamInviteeModel.createdAt
    override var modifiedAt by TeamInviteeModel.modifiedAt

    var senderOrgMember by OrgMemberDAO referencedOn TeamInviteeModel.senderOrgMember
    var inviteeOrgMember by OrgMemberDAO referencedOn TeamInviteeModel.inviteeOrgMember
    var eventType by TeamInviteeModel.eventType

    override fun asDataModel() = readValues.let {
        TeamInvitee(
            id = it[TeamInviteeModel.id].value,
            createdAt = it[TeamInviteeModel.createdAt],
            modifiedAt = it[TeamInviteeModel.modifiedAt],
            senderOrgMemberId = it[TeamInviteeModel.senderOrgMember].value,
            inviteeOrgMemberId = it[TeamInviteeModel.inviteeOrgMember].value,
            eventType = it[TeamInviteeModel.eventType],
        )
    }
}

object TeamInviteeIdConverter : ValueIdConverter<TeamInviteeId> {
    override val factory = ::TeamInviteeId
    override val extract = TeamInviteeId::value
}

internal object TeamInviteeIdSerializer : ValueIdSerializer<TeamInviteeId>(
    serialName = "TeamInviteeId",
    converter = TeamInviteeIdConverter,
)

@Serializable(with = TeamInviteeIdSerializer::class)
data class TeamInviteeId(val value: UUID) : ValueId {

    companion object : ValueIdClass<TeamInviteeId>(
        converter = TeamInviteeIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is TeamInviteeId)
        return value.compareTo(other.value)
    }
}

data class TeamInvitee(
    val id: TeamInviteeId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val senderOrgMemberId: OrgMemberId,
    val inviteeOrgMemberId: OrgMemberId,
    val eventType: TeamInviteeEventType,
)
