package com.nextchaptersoftware.scripts.security

import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.crypto.types.Ciphertext
import java.util.HexFormat

/**
 * In order to decrypt a db string, you must first convert it to a hex value:
 * select encode(<field>, 'hex') as hex from <table>
 */
fun main() {
    println("Enter private key: ")
    val privateKey = readln().trim()

    println("Enter encrypted db hex string (look at instructions): ")
    val str = readln().trim()
    val hexStr = HexFormat.of().parseHex(str)

    val decryptionService = RSACryptoSystem.RSADecryption(
        privateKey = privateKey,
    )

    println("Decrypted Value: ")
    println(decryptionService.decrypt(Ciphertext(hexStr)).value)
}
