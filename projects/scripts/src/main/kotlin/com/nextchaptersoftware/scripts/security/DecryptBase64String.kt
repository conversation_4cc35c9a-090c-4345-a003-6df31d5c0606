package com.nextchaptersoftware.scripts.security

import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.utils.Base64.base64DecodeAsByteArray

fun main() {
    println("Enter private key: ")
    val privateKey = readln().trim()

    println("Enter encrypted string: ")
    val str = readln().trim()

    val decryptionService = RSACryptoSystem.RSADecryption(
        privateKey = privateKey,
    )

    println("Decrypted Value: ")
    println(decryptionService.decrypt(Ciphertext(str.base64DecodeAsByteArray())).value)
}
