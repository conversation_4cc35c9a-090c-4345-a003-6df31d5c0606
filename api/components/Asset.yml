type: object
properties:
  id:
    $ref: "./ApiResourceId.yml"
  threadId:
    $ref: "./ApiResourceId.yml"
  messageId:
    $ref: "./ApiResourceId.yml"
  name:
    type: string
    ## 0 length names are accepted in lieu of refactoring this field to optional. Name is not currently used.
  contentLength:
    type: integer
    format: int32
  contentType:
    type: string
  isDeleted:
    type: boolean
required:
  - id
  - name
  - contentLength
  - contentType
