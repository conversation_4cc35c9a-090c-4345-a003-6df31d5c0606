type: object
description: |
  A registered Enterprise Provider.
  Use the ID in login flows.
properties:
  id:
    $ref: "./ApiResourceId.yml"
  displayName:
    description: |
      Name to be used to describe this provider.
      This is typically the hostname and port of the enterprise instance.
    type: string
    example: git.nike.com:123
  provider:
    $ref: "./Provider.yml"
  oauthUrl:
    description: |
      External URL to begin an OAuth login with this provider.
    type: string
  installUrl:
    description: |
      External URL for installing additional SCM accounts.
      This URL is optional because only some providers support this ability.
    type: string
required:
  - id
  - displayName
  - provider
  - oauthUrl
