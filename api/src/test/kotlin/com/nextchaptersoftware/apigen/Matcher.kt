package com.nextchaptersoftware.apigen

internal sealed interface Matcher<T> {
    fun matches(other: T): Boolean
}

internal class OperationMatcher(
    private val operationId: OpenApiOperationId,
) : Matcher<OpenApiOperationId> {
    override fun matches(other: OpenApiOperationId) = other == operationId
}

internal class EndpointMatcher(
    private val endpoint: OpenApiEndpoint,
) : Matcher<OpenApiEndpoint> {
    override fun matches(other: OpenApiEndpoint) = other == endpoint
}

internal interface IgnoreOverride<T> : Matcher<T> {
    val earliestSupportedReleaseTag: ReleaseTag

    fun ignoreRelease(releaseTag: ReleaseTag?): Bo<PERSON>an {
        return releaseTag?.let { releaseTag < earliestSupportedReleaseTag } ?: true
    }
}

internal data class OperationOverrides(
    private val operations: Set<OperationMatcher>,
    override val earliestSupportedReleaseTag: ReleaseTag,
) : IgnoreOverride<OpenApiOperationId> {
    override fun matches(other: OpenApiOperationId): Boolean {
        return operations.any { it.matches(other) }
    }
}

internal data class EndpointOverrides(
    private val endpoints: Set<EndpointMatcher>,
    override val earliestSupportedReleaseTag: ReleaseTag,
) : IgnoreOverride<OpenApiEndpoint> {
    override fun matches(other: OpenApiEndpoint): Boolean {
        return endpoints.any { it.matches(other) }
    }
}
