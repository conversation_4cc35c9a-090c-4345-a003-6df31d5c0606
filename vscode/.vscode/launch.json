{"version": "0.2.0", "configurations": [{"name": "VSCode: watch:dev", "type": "extensionHost", "request": "launch", "skipFiles": ["<node_internals>/**"], "args": ["--extensionDevelopmentPath=${workspaceFolder}/dist"], "outFiles": ["${workspaceFolder}/dist/extension.js"], "preLaunchTask": "watch:dev", "presentation": {"group": "vscode"}}, {"name": "VSCode: watch:prod", "type": "extensionHost", "request": "launch", "skipFiles": ["<node_internals>/**"], "args": ["--extensionDevelopmentPath=${workspaceFolder}/dist"], "outFiles": ["${workspaceFolder}/dist/extension.js"], "preLaunchTask": "watch:prod", "presentation": {"group": "vscode"}}, {"name": "VSCode: watch:local", "type": "extensionHost", "request": "launch", "skipFiles": ["<node_internals>/**"], "args": ["--extensionDevelopmentPath=${workspaceFolder}/dist"], "outFiles": ["${workspaceFolder}/dist/extension.js"], "preLaunchTask": "watch:local", "presentation": {"group": "vscode"}}, {"name": "VSCode: debug:dev", "type": "extensionHost", "request": "launch", "skipFiles": ["<node_internals>/**"], "args": ["--extensionDevelopmentPath=${workspaceFolder}/dist"], "outFiles": ["${workspaceFolder}/dist/extension.js"], "preLaunchTask": "build:dev", "presentation": {"group": "vscode"}}, {"name": "VSCode: debug:prod", "type": "extensionHost", "request": "launch", "skipFiles": ["<node_internals>/**"], "args": ["--extensionDevelopmentPath=${workspaceFolder}/dist"], "outFiles": ["${workspaceFolder}/dist/extension.js"], "preLaunchTask": "build:prod", "presentation": {"group": "vscode"}}, {"name": "VSCode: debug:local", "type": "extensionHost", "request": "launch", "skipFiles": ["<node_internals>/**"], "args": ["--extensionDevelopmentPath=${workspaceFolder}/dist"], "outFiles": ["${workspaceFolder}/dist/extension.js"], "preLaunchTask": "build:local", "presentation": {"group": "vscode"}}]}