import { ThreadInfoAggregate } from '@shared/api/models';

import { createMockMessage, createMockThreadInfo, MockMessageWithoutImage, MockTeamMember } from '@shared-mocks';
import type { Meta, StoryObj } from '@storybook/react';

import { PullRequestBlockView } from './PullRequestBlockViews';

export default {
    title: 'VSCode/PullRequestBlockView',
    component: PullRequestBlockView,
} as Meta<typeof PullRequestBlockView>;

type Story = StoryObj<typeof PullRequestBlockView>;

const map = new Map();

const render = (args: any) => (
    <div style={{ padding: '40px 40px 40px 60px' }}>
        <PullRequestBlockView {...args} />
    </div>
);

export const Primary: Story = {
    render,
    args: {
        block: {
            id: '1234',
            author: MockTeamMember,
            repoId: 'a',
            isAuthorCurrentPerson: true,
            createdAt: new Date(),
            message: createMockMessage(MockMessageWithoutImage),
            htmlUrl: '#',
        },
        getSnippet: (threadInfo: ThreadInfoAggregate) => map.get(threadInfo?.thread?.id),
    },
};

export const SingleThread: Story = {
    render,
    args: {
        block: {
            id: '1234',
            author: MockTeamMember,
            repoId: 'a',
            isAuthorCurrentPerson: true,
            createdAt: new Date(),
            threads: [createMockThreadInfo({})],
            htmlUrl: '#',
        },
        getSnippet: (threadInfo: ThreadInfoAggregate) => map.get(threadInfo?.thread?.id),
    },
};

export const ContentWithThread: Story = {
    render,
    args: {
        block: {
            id: '1234',
            author: MockTeamMember,
            repoId: 'a',
            isAuthorCurrentPerson: true,
            createdAt: new Date(),
            message: createMockMessage(MockMessageWithoutImage),
            threads: [createMockThreadInfo({})],
            htmlUrl: '#',
        },
        getSnippet: (threadInfo: ThreadInfoAggregate) => map.get(threadInfo?.thread?.id),
    },
};

export const ContentWithThreads: Story = {
    render,
    args: {
        block: {
            id: '1234',
            author: MockTeamMember,
            repoId: 'a',
            isAuthorCurrentPerson: true,
            createdAt: new Date(),
            message: createMockMessage(MockMessageWithoutImage),
            threads: [createMockThreadInfo({}), createMockThreadInfo({ threadId: '2345' })],
            htmlUrl: '#',
        },
        getSnippet: (threadInfo: ThreadInfoAggregate) => map.get(threadInfo?.thread?.id),
    },
};
