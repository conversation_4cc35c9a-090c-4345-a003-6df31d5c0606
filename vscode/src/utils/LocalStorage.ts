import { promises as fsPromises } from 'fs';
import { ExtensionContext } from 'vscode';

export class LocalStorage {
    private static context: ExtensionContext | undefined;

    static async init(context: ExtensionContext) {
        this.context = context;

        const path = this.context.globalStorageUri.fsPath;

        // Force create the global storage path
        await fsPromises.mkdir(path, { recursive: true });
    }

    static getValue<T>(key: string) {
        if (!this.context) {
            throw new Error(`No local storage instance: GET ${key}`);
        }
        return this.context.workspaceState.get<T>(key);
    }

    static setValue<T>(key: string, value: T) {
        if (!this.context) {
            throw new Error(`No local storage instance: GET ${key}`);
        }
        this.context.workspaceState.update(key, value);
    }

    static deleteValue(key: string) {
        if (!this.context) {
            throw new Error(`No local storage instance: GET ${key}`);
        }
        this.context.workspaceState.update(key, undefined);
    }

    static getGlobalValue<T>(key: string) {
        if (!this.context) {
            throw new Error(`No global state storage instance: GET ${key}`);
        }
        return this.context.globalState.get<T>(key);
    }

    static setGlobalValue<T>(key: string, value: T) {
        if (!this.context) {
            throw new Error(`No global state storage instance: GET ${key}`);
        }
        this.context.globalState.update(key, value);
    }

    static deleteGlobalValue(key: string) {
        if (!this.context) {
            throw new Error(`No global state storage instance: GET ${key}`);
        }
        this.context.globalState.update(key, undefined);
    }

    static getGlobalStoragePath(): string {
        if (!this.context) {
            throw new Error(`No global state storage instance: getGlobalStoragePath`);
        }

        return this.context.globalStorageUri.fsPath;
    }
}
