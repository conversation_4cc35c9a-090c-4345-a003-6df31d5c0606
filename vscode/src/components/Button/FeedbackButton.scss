@use 'layout' as *;
@use 'misc' as *;
@use 'colors' as *;
@use 'fonts' as *;
@use 'sass:color';

@mixin feedback-button($color) {
    .feedback_button--background {
        transition: opacity 0.2s ease-in-out;
        background: $color;
        opacity: 0.2;
    }

    &:hover {
        .feedback_button--background {
            opacity: 0.4;
        }
    }
}

.feedback_button {
    @include feedback-button(var(--vscode-terminal-foreground));

    padding: 0 $spacer-6 0 $spacer-4 !important;
    height: $spacer-20;
    color: var(--vscode-editor-foreground);

    .button__icon {
        opacity: 0.4;
    }

    .feedback_button--content {
        opacity: 0.8;
        font-size: $font-size-11;
    }

    &:hover {
        color: var(--vscode-editor-foreground);
    }

    &.feedback_button--selected {
        &.feedback_button--positive {
            @include feedback-button(var(--vscode-terminal-ansiGreen));
        }

        &.feedback_button--neutral {
            @include feedback-button(var(--vscode-terminal-ansi<PERSON><PERSON>w));
        }

        &.feedback_button--negative {
            @include feedback-button(var(--vscode-terminal-ansiRed));
        }
    }
}

.feedback_tooltip {
    font-size: $font-size-11;
    row-gap: $spacer-6;
    column-gap: $spacer-4;

    .feedback_tooltip_close {
        .icon {
            width: $size-12;
            height: $size-12;
        }
    }

    .feedback_tooltip__send_feedback {
        padding: 0 $spacer-2 $spacer-1 0;

        color: var(--vscode-button-background);

        &[disabled] {
            color: var(--vscode-tab-unfocusedInactiveForeground);
        }
    }

    &.feedback_invite {
        row-gap: 0;

        .feedback_invite_body {
            .text_input {
                padding: $spacer-5 $spacer-10;
                margin-bottom: $spacer-6;
                border: $border-width $border-style var(--vscode-settings-textInputBorder, transparent);
                background-color: var(--vscode-input-background);
                input {
                    font-size: $font-size-11;
                }
                .icon {
                    height: $size-14;
                    width: $size-14;
                }
            }
        }

        .feedback_invite__description {
            margin-bottom: $spacer-6;
        }

        .feedback_invite__send_invite {
            padding: $spacer-5 $spacer-15 !important;
            font-size: $font-size-13;
        }
    }
}
