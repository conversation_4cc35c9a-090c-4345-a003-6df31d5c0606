@use 'layout' as *;
@use 'misc' as *;
@use 'fonts' as *;

.reference_button {
    padding: $spacer-0 $spacer-6 !important;
    height: $spacer-20;
    font-size: $font-size-11;
    color: var(--vscode-editor-foreground) !important;

    .reference_button__content {
        opacity: 0.8;
    }

    .reference_button__border {
        border: $border-style $border-width var(--vscode-editor-foreground);
        opacity: 0.32;
    }

    .reference_button__background {
        background-color: var(--vscode-editor-foreground);
        opacity: 0;
    }

    &:hover {
        .reference_button__background {
            opacity: 0.16;
        }
    }
}
