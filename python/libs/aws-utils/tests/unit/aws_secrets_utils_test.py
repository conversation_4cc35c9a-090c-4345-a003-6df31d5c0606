import base64
import json
import unittest
from unittest.mock import patch, MagicMock

from botocore.exceptions import ClientError

# 🔄  Adjust this to wherever get_aws_secret really lives
from aws_utils.aws_secrets_utils import get_aws_secret


class TestAwsSecret(unittest.TestCase):

    @patch("boto3.session.Session")
    def test_secretstring_plain_text(self, mock_session_cls):
        """Returns a raw string when SecretString is plain text."""
        mock_client = MagicMock()
        mock_client.get_secret_value.return_value = {"SecretString": "hunter2"}
        mock_session_cls.return_value.client.return_value = mock_client

        secret = get_aws_secret("dev/plain")
        self.assertEqual(secret, "hunter2")

    @patch("boto3.session.Session")
    def test_json_secret_field(self, mock_session_cls):
        """Parses JSON payload and returns a top-level key via ``field``."""
        mock_client = MagicMock()
        payload = json.dumps({"username": "root", "password": "fakepassword"})
        mock_client.get_secret_value.return_value = {"SecretString": payload}
        mock_session_cls.return_value.client.return_value = mock_client

        pw = get_aws_secret("db/creds", field="password")
        self.assertEqual(pw, "fakepassword")

    @patch("boto3.session.Session")
    def test_json_secret_nested_path(self, mock_session_cls):
        """Returns a nested value using dot-notation ``path``."""
        mock_client = MagicMock()
        payload = json.dumps({"db": {"creds": {"password": "fakepassword"}}})
        mock_client.get_secret_value.return_value = {"SecretString": payload}
        mock_session_cls.return_value.client.return_value = mock_client

        pw = get_aws_secret("db/creds", path="db.creds.password")
        self.assertEqual(pw, "fakepassword")

    @patch("boto3.session.Session")
    def test_secretbinary_base64_json(self, mock_session_cls):
        """Handles base64-encoded SecretBinary containing JSON."""
        mock_client = MagicMock()
        encoded = base64.b64encode(b'{"token":"abc123"}').decode()
        mock_client.get_secret_value.return_value = {"SecretBinary": encoded}
        mock_session_cls.return_value.client.return_value = mock_client

        token = get_aws_secret("svc/token", field="token")
        self.assertEqual(token, "abc123")

    @patch("boto3.session.Session")
    def test_default_returned_on_not_found(self, mock_session_cls):
        """Returns provided default when secret is missing."""
        mock_client = MagicMock()
        err = ClientError(
            {"Error": {"Code": "ResourceNotFoundException", "Message": "Missing"}},
            "GetSecretValue",
        )
        mock_client.get_secret_value.side_effect = err
        mock_session_cls.return_value.client.return_value = mock_client

        default_val = get_aws_secret("missing/secret", default="placeholder")
        self.assertEqual(default_val, "placeholder")

    @patch("boto3.session.Session")
    def test_raises_without_default(self, mock_session_cls):
        """Propagates ClientError if no default supplied."""
        mock_client = MagicMock()
        err = ClientError(
            {"Error": {"Code": "AccessDeniedException", "Message": "Denied"}},
            "GetSecretValue",
        )
        mock_client.get_secret_value.side_effect = err
        mock_session_cls.return_value.client.return_value = mock_client

        with self.assertRaises(ClientError):
            get_aws_secret("forbidden/secret")


if __name__ == "__main__":
    unittest.main(verbosity=2)
