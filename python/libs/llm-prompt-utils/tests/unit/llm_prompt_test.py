import unittest
from typing import Union

from langchain_core.prompts import PromptTemplate, BasePromptTemplate
from langchain_core.prompts.chat import BaseMessagePromptTemplate

from llm_prompt_utils.llm_prompt import LLMPrompt


class SimpleLLMPrompt(LLMPrompt):
    _prompt = """test"""
    _inputs: list[str]

    def __init__(self, inputs: list[str]):
        self._inputs = inputs

    def get_prompt_template(self) -> Union[BasePromptTemplate, BaseMessagePromptTemplate]:
        return PromptTemplate(template=self._prompt, input_variables=self._inputs, template_format="jinja2")

    def get_inputs(self) -> list[str]:
        return self._inputs


class LLMPromptTest(unittest.TestCase):
    def test_llm_prompt_validation(self):
        llm_prompt = SimpleLLMPrompt(inputs=["hello"])

        llm_prompt.validate_inputs(inputs={"hello": "goodbye"})

        with self.assertRaises(ValueError):
            llm_prompt.validate_inputs({"document": "Source marks are the way to go!"})

        with self.assertRaises(ValueError):
            llm_prompt.validate_inputs({"bah": "bha"})

    def test_llm_prompt_validation_no_inputs(self):
        llm_prompt = SimpleLLMPrompt(inputs=[])

        llm_prompt.validate_inputs(inputs={})

        with self.assertRaises(ValueError):
            llm_prompt.validate_inputs({"document": "Source marks are the way to go!"})
