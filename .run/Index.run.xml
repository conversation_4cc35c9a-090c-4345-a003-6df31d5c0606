<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Index" type="JetRunConfigurationType" folderName="internal batch">
    <envs>
      <env name="SERVICE_PORT" value="8116" />
      <env name="SERVICE_TARGET_PORT" value="8116" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.nextchaptersoftware.indexservice.ApplicationKt" />
    <module name="unblocked.projects.services.indexservice.main" />
    <shortenClasspath name="NONE" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
      </ENTRIES>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>