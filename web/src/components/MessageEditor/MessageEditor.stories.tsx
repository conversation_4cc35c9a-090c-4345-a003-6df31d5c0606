import type { Meta, StoryObj } from '@storybook/react';

import { Editor<PERSON><PERSON><PERSON>, EditorToBlockTranslator, MessageEditor } from './MessageEditor';

import './MessageEditor.scss';

export default {
    title: 'Web/MessageEditor',
    component: MessageEditor,
    argTypes: {
        backgroundColor: { control: 'color' },
    },
    parameters: {
        chromatic: { disableSnapshot: false },
    },
} as Meta<typeof MessageEditor>;

type Story = StoryObj<typeof MessageEditor>;

const initialContent: EditorContent = [
    {
        type: 'paragraph',
        children: [
            { text: 'A line of text in a paragraph.  ' },
            { text: 'With styling ', bold: true },
            { text: ' and more styling.', italic: true },
        ],
    },
    { type: 'code', children: [{ text: 'A code block.\nThis can be multiline.' }] },
    {
        type: 'quote',
        children: [
            {
                type: 'paragraph',
                children: [
                    { text: 'A Quote Block ' },
                    { text: 'with bold ', bold: true },
                    { text: 'and italic', italic: true },
                ],
            },
        ],
    },
];
const translatedBlockData = EditorToBlockTranslator.translateToBlock(initialContent);

export const Gallery: Story = {
    render: (args) => <MessageEditor {...args} initialContent={translatedBlockData} />,
};
