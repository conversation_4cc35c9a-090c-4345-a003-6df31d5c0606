import {
    ConfluenceConfiguration,
    GoogleDriveWorkspaceConfiguration,
    JiraConfiguration,
    LinearConfiguration,
} from '@shared/api/generatedApi';

export enum ConfigureSettingType {
    all = 'all',
    specific = 'specific',
}

export interface ConfigureSettingForwardedRef {
    onSave: () => void;
    onDeleteInstallation: () => void;
}

export type ConfigurationType =
    | JiraConfiguration
    | ConfluenceConfiguration
    | LinearConfiguration
    | GoogleDriveWorkspaceConfiguration;

export const isJiraConfiguration = (config: ConfigurationType): config is JiraConfiguration => {
    return config && 'projects' in config;
};

export const isConfluenceConfiguration = (config: ConfigurationType): config is ConfluenceConfiguration => {
    return config && 'spaces' in config;
};

export const isLinearConfiguration = (config: ConfigurationType): config is LinearConfiguration => {
    return config && 'teams' in config;
};

export const isGoogleDriveWorkspaceConfiguration = (
    config: ConfigurationType
): config is GoogleDriveWorkspaceConfiguration => {
    return config && 'files' in config;
};
