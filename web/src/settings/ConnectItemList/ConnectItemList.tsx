import { Provider } from '@shared/api/generatedApi';

import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';
import { IntegrationImg } from '@shared/webComponents/IntegrationImg/IntegrationImg';
import { getProviderWorkspaceLabelCounts } from '@shared/webUtils/ProviderUtils';
import { Button, ContextRow } from '@web/components';

import { faChevronRight } from '@fortawesome/pro-solid-svg-icons/faChevronRight';

import './ConnectItemList.scss';

interface Props<T> {
    provider: Provider;
    items: T[];
    itemId: (item: T) => string;
    itemTitle: (item: T) => string;
    itemIcon: (item: T) => IconSrc;
    itemClick: (item: T) => void;
    onReconnect: () => void;
}

export function ConnectItemList<T>({ provider, items, itemId, itemTitle, itemIcon, itemClick, onReconnect }: Props<T>) {
    if (!items) {
        const workspaceLabel = getProviderWorkspaceLabelCounts(provider);

        return (
            <div className="connect_item_list">
                <IntegrationImg provider={provider} />
                <h1>No {workspaceLabel.plural ?? workspaceLabel.singular} are available to add</h1>
                <Button onClick={onReconnect}>Connect another {workspaceLabel.singular}</Button>
            </div>
        );
    }

    return (
        <div className="connect_item_list">
            <IntegrationImg provider={provider} />

            <h1>Select a {getProviderWorkspaceLabelCounts(provider).singular} to use with Unblocked</h1>

            <div className="connect_item_list__items">
                {items.map((item) => (
                    <ContextRow
                        key={itemId(item)}
                        className="connect_item_list__item"
                        as="button"
                        variant="bold"
                        icon={<Icon className="connect_item_list__item_icon" icon={itemIcon(item)} size={40} />}
                        rightContent={<Icon icon={faChevronRight} size="small" />}
                        onClick={() => itemClick(item)}
                    >
                        {itemTitle(item)}
                    </ContextRow>
                ))}
            </div>
        </div>
    );
}
