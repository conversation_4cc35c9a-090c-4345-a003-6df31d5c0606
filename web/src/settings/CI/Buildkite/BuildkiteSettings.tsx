import { Provider } from '@shared/api/generatedApi';

import { CISettings } from '../CISettings';
import { BuildkiteSteps } from './BuildkiteSteps';

interface Props {
    installationId: string;
    initialConnection?: boolean;
}

export const BuildkiteSettings = ({ installationId, initialConnection }: Props) => {
    return (
        <CISettings
            provider={Provider.BuildKite}
            installationId={installationId}
            onboardingSteps={BuildkiteSteps}
            initialConnection={initialConnection}
        />
    );
};
