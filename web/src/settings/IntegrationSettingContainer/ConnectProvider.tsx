import { useEffect, useMemo } from 'react';
import { Navigate } from 'react-router-dom';

import { Provider } from '@shared/api/models';

import { DashboardUrls } from '@shared/webUtils/DashboardUrls';
import { getProviderDisplayName, getProviderWorkspaceLabelCounts, isCiProvider } from '@shared/webUtils/ProviderUtils';
import { StringsHelper } from '@shared/webUtils/StringsHelper/StringsHelper';
import { Loading } from '@web/components/Loading/Loading';
import { useTeamContext } from '@web/components/Team/TeamContext';
import { useDocumentTitle } from '@web/hooks/useDocumentTitle';

import { ConnectIntegrationSetting } from '../ConfigureIntegration/ConnectIntegrationSetting';
import { SettingsIntegrationHeader } from '../IntegrationSettingsUtils';
import { useSettingsContext } from '../SettingsContext';
import { useSettingsOutletContext } from '../SettingsOutletContext';

interface Props {
    provider: Provider;
}

export const ConnectProvider = ({ provider }: Props) => {
    const { currentTeamId } = useTeamContext();
    const { teamIntegrations } = useSettingsContext();
    const { integrations, integrationsLoading } = teamIntegrations;
    const { setSettingsHeader } = useSettingsOutletContext();

    useDocumentTitle(() => {
        return getProviderDisplayName(provider, false);
    }, [provider]);

    useEffect(() => {
        setSettingsHeader(
            <SettingsIntegrationHeader provider={provider} label={getProviderDisplayName(provider, false)} />
        );
    }, [setSettingsHeader, provider]);

    const content = useMemo(() => {
        if (isCiProvider(provider)) {
            return (
                <>
                    When you connect with {getProviderDisplayName(provider)}, Unblocked can assess and triage CI
                    failures using insights from your source code and connected data sources.
                </>
            );
        }

        return (
            <>
                When you connect your {StringsHelper.pluralize(getProviderWorkspaceLabelCounts(provider))}, the
                conversations about your codebase inform and improve Unblocked answers.
            </>
        );
    }, [provider]);

    if (integrationsLoading) {
        return <Loading />;
    }

    const integration = integrations.get(provider);

    if (!integration) {
        return <Navigate to={DashboardUrls.integrationSettings(currentTeamId)} replace />;
    }

    return (
        <ConnectIntegrationSetting
            integration={integration}
            provider={provider}
            teamId={currentTeamId}
            installRedirectUrl={DashboardUrls.integrationRedirect(currentTeamId, { initialConnection: true })}
        >
            {content}
        </ConnectIntegrationSetting>
    );
};
