@use 'layout' as *;
@use 'theme' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;

.insight_feed_header {
    display: grid;

    grid-template: 'icon title actions rightChild' auto / auto 1fr auto auto;

    &:has(.insight_feed_header__subContent) {
        grid-template:
            'icon title actions rightChild' 1fr
            'icon subContent subContent rightChild' auto / auto 1fr auto auto;
    }
    gap: $spacer-2 $spacer-10;
    align-items: center;
    margin: 0 $spacer-16 $spacer-16;

    @include breakpoint(md) {
        margin: 0 0 $spacer-16;
        grid-template: 'icon title actions rightChild' auto / auto 1fr auto auto;

        &:has(.insight_feed_header__subContent) {
            grid-template:
                'icon title actions rightChild' 1fr
                'icon subContent subContent subContent' auto / auto 1fr auto auto;
        }
        gap: $spacer-6 $spacer-8;
    }

    & > .insight_feed_header__icon {
        grid-area: icon;
        justify-self: center;
        margin-bottom: $spacer-4;

        & > .icon {
            @include icon-size(large);
        }

        @include breakpoint(md) {
            margin-bottom: 0;
            & > .icon {
                @include icon-size(xx-large);
            }
        }
    }

    .insight_feed_header__title {
        grid-area: title;

        @include flex-center($spacer-8);
    }

    .insight_feed_header__subContent {
        grid-area: subContent;
    }

    .insight_feed_header__actions {
        grid-area: actions;
    }

    .insight_feed_header__rightChild {
        grid-area: rightChild;
        margin-left: $spacer-4;
        @include flex-center($spacer-8);
    }
}
