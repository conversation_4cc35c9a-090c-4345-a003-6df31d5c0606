import { useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import { Provider } from '@models';

import { useAsyncOperation } from '@shared/hooks/UseAsyncOperation';
import { useQuery } from '@shared/hooks/useQuery';
import { AuthStateCache } from '@shared/stores/AuthStateCache';
import { useStream } from '@shared/stores/DataCacheStream';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { ArrayUtils, DashboardUrls, getProvider, Sorter, StringsHelper } from '@shared-web-utils';
import { EnterpriseScmProvidersStore } from '@web/settings/EnterpriseProviders/EnterpriseScmProvidersStore';
import { GithubEnterpriseManifest } from '@web/settings/EnterpriseProviders/GithubEnterpriseManifest';
import { ManifestHostAndPortForm } from '@web/settings/EnterpriseProviders/ManifestHostAndPortForm';

import { BitbucketDataCenterManifest } from './BitbucketDataCenterManifest';
import { GitlabSelfHostedManifest } from './GitlabSelfHostedManifest';

import './LoginManifest.scss';

export const LoginManifest = () => {
    const query = useQuery();
    const navigate = useNavigate();
    const providerString = query.get('provider');
    const encodedRepoUrlsString = query.get('repoUrls');
    const preauth = query.get('preauth') ?? undefined;
    const [cacheResult] = useAsyncOperation(() => AuthStateCache.getCaches());
    const cachedAuthState = useMemo(() => {
        return cacheResult?.ok ? cacheResult.value : [];
    }, [cacheResult]);

    const repoUrls = useMemo(() => {
        return encodedRepoUrlsString ? encodedRepoUrlsString.split(',').map(StringsHelper.decodeBase64) : [];
    }, [encodedRepoUrlsString]);
    const provider = providerString ? getProvider(providerString) : undefined;
    const store = useMemo(() => {
        return new EnterpriseScmProvidersStore(provider, 'login');
    }, [provider]);

    useEffect(() => {
        const allCaches = cachedAuthState
            .filter((v) => v.provider === provider)
            .sort(Sorter.makeSortFn([Sorter.byValue((t) => t.lastUsedAt, false)]));
        const targetCache = ArrayUtils.firstOrUndefined(allCaches);

        if (repoUrls.length >= 1) {
            store.loadRepos(repoUrls);
        } else if (targetCache && preauth) {
            store.loadEnterpriseProvider(targetCache);
        } else {
            store.reset();
        }
    }, [repoUrls, provider, cachedAuthState, preauth, store]);

    const currentState = useStream(() => store.stream, [store]);

    const renderBody = () => {
        if (!currentState) {
            return <Loading />;
        }
        switch (currentState.$case) {
            case 'loading':
                return <Loading />;
            case 'requiresHostAndPort':
            case 'loadingHostAndPort':
                return (
                    <ManifestHostAndPortForm
                        includeHeader
                        hostAndPort={currentState.hostAndPort}
                        provider={provider}
                        store={store}
                        enterpriseProviderMap={currentState.enterpriseProviderMap}
                        preauthToken={preauth}
                        isLogin
                    />
                );
            case 'unregistered':
                switch (currentState.unregisteredProvider.provider) {
                    case Provider.GithubEnterprise:
                        return (
                            <GithubEnterpriseManifest
                                clientState={{
                                    purpose: 'login',
                                }}
                                hostAndPort={currentState.hostAndPort}
                                repoOrgName={currentState.unregisteredProvider.orgName}
                                unregisteredProvider={currentState.unregisteredProvider}
                                reset={() => store.reset(currentState.hostAndPort)}
                                isLogin
                            />
                        );
                    case Provider.GitlabSelfHosted:
                        return (
                            <GitlabSelfHostedManifest
                                unregisteredProvider={currentState.unregisteredProvider}
                                hostAndPort={currentState.hostAndPort}
                                onEditHost={() => store.reset(currentState.hostAndPort)}
                                onSuccess={(id) => navigate(DashboardUrls.loginEnterpriseOAuth(id))}
                            />
                        );

                    case Provider.BitbucketDataCenter:
                        return (
                            <BitbucketDataCenterManifest
                                unregisteredProvider={currentState.unregisteredProvider}
                                hostAndPort={currentState.hostAndPort}
                                onEditHost={() => store.reset(currentState.hostAndPort)}
                                onSuccess={(id) => navigate(DashboardUrls.loginEnterpriseOAuth(id))}
                            />
                        );

                    default:
                        return (
                            <div>
                                There was an issue loading the {currentState.unregisteredProvider.displayName} data.
                                Please contact support for further assistance.
                            </div>
                        );
                }
        }
    };

    return <section className="login_manifest">{renderBody()}</section>;
};
