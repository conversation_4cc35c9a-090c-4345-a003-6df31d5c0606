package com.nextchaptersoftware.ktlint.rules

import com.pinterest.ktlint.test.KtLintAssertThat.Companion.assertThatRule
import com.pinterest.ktlint.test.LintViolation
import org.junit.jupiter.api.Test

class NoExpressionInLogStatementRuleTest {
    @Test
    fun `ensure log expressions are not allowed`() {
        assertThatRule { NoExpressionInLogStatementRule() }(
            $$"""
import mu.KotlinLogging

fun test() {
    val name = "Richie"
    logger.infoAsync("$name is awesome")
    logger.warnAsync("${name} is awesome")
}
            """.trimIndent(),
        ).hasLintViolationsWithoutAutoCorrect(
            LintViolation(
                line = 5,
                col = 23,
                detail = "Usage of expressions in log statements is forbidden as they cannot be indexed. Use this pattern: LOGGER.infoAsync(\"field\" to field) { \"log statement\" }",
            ),
            LintViolation(
                line = 6,
                col = 23,
                detail = "Usage of expressions in log statements is forbidden as they cannot be indexed. Use this pattern: LOGGER.infoAsync(\"field\" to field) { \"log statement\" }",
            ),
        )
    }

    @Test
    fun `ensure log expressions are allowed if no valid imports`() {
        assertThatRule { NoExpressionInLogStatementRule() }(
            $$"""
import matt.Adam.Is.Lovely

fun test() {
    val name = "Richie"
    logger.infoAsync("$name is awesome")
    logger.warnAsync("${name} is awesome")
}
            """.trimIndent(),
        ).hasNoLintViolations()
    }

    @Test
    fun `ensure regular string literals are allowed`() {
        assertThatRule { NoExpressionInLogStatementRule() }(
            """
fun test() {
    val name = "Richie"
    logger.infoAsync("Richie is awesome")
    logger.infoAsync("name" to name) { "He is awesome" }
}
            """.trimIndent(),
        ).hasNoLintViolations()
    }
}
