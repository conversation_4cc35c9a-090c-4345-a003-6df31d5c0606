package com.nextchaptersoftware.ktlint.rules

import com.nextchaptersoftware.ktlint.utils.PackageValidator
import com.pinterest.ktlint.rule.engine.core.api.ElementType
import org.jetbrains.kotlin.com.intellij.lang.ASTNode
import org.jetbrains.kotlin.psi.KtNamedFunction

class RestrictApiModelFunctionsRule : CustomRule("restrict-api-model-functions-rule") {

    private val packageValidator = PackageValidator(exclusionPatterns = listOf(Regex("models.converters")))

    private val expressions = listOf(
        "asApiModel",
        "toApiModel",
    )

    override fun beforeVisitChildNodes(
        node: ASTNode,
        emit: Emitter,
        ) {
        if (!packageValidator.validate(node)) {
            return
        }

        if (node.elementType == ElementType.FUN) {
            val referencedFunction = node.psi as KtNamedFunction
            referencedFunction.name?.let { functionName ->
                if (expressions.any { functionName.contains(it) }) {
                    val errorMessage =
                        "Conversions functions from data models to api models are restricted to the lib-api-model and lib-public-api-model packages."
                    emit(node.startOffset, errorMessage, false)
                }
            }
        }
    }
}
