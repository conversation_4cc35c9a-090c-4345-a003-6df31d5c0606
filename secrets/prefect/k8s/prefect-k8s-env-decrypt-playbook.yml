---
- hosts: localhost
  roles:
    - common
  tasks:
    # Workaround for import_role bug
    # https://github.com/ansible/ansible/issues/80944
    - include_role:
        name: "{{ env }}"
        public: True
    - name: Create secrets directory
      file:
        path: "{{ secretsOutputDir }}"
        state: directory
    - name: Decrypt secrets to {{ secretsOutputDir }}
      copy:
        src: "{{ item }}"
        dest: "{{ secretsOutputDir }}/{{ item | basename }}"
        decrypt: yes
      with_fileglob:
        - "{{ playbook_dir }}/{{ secretsDir }}/.*"
        - "{{ playbook_dir }}/{{ secretsDir }}/*"
