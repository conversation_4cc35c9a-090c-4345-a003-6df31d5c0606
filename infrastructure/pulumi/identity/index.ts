import * as gcp from "@pulumi/gcp";
import * as config from "./config";
import * as util from "./util";
import * as pulumi from "@pulumi/pulumi";

// Create the GKE cluster admins ServiceAccount.
const adminsName = "admins";
export const adminsAccountId = `k8s-${adminsName}`;
const adminsIamServiceAccount = new gcp.serviceaccount.Account(adminsName, {
    project: config.project,
    accountId: adminsAccountId,
    displayName: "Kubernetes Admins",
});

// Bind the admin ServiceAccount to be a GKE cluster admin.
util.bindToRole(`${adminsName}-k8s`, adminsIamServiceAccount, {
    project: config.project,
    roles: ["roles/container.admin", "roles/container.clusterAdmin", "roles/container.developer"],
});

// Bind the admin ServiceAccount to be a CloudSQL admin.
util.bindToRole(`${adminsName}-cloudsql`, adminsIamServiceAccount, {
    project: config.project,
    roles: ["roles/cloudsql.admin"],
});

// Export the admins ServiceAccount key.
const adminsIamServiceAccountKey = util.createServiceAccountKey(`${adminsName}Key`, adminsIamServiceAccount);

// Export the admins ServiceAccount client secret to authenticate as this service account.
export const adminsIamServiceAccountSecret = util.clientSecret(adminsIamServiceAccountKey);

// Create the GKE cluster developers ServiceAccount.
const devsName = "devs";
export const devsAccountId = `k8s-${devsName}`;
const devsIamServiceAccount = new gcp.serviceaccount.Account(devsName, {
    project: config.project,
    accountId: devsAccountId,
    displayName: "Kubernetes Developers",
});


// Bind the devs ServiceAccount to be a GKE cluster developer.
util.bindToRole(`${devsName}-k8s`, devsIamServiceAccount, {
    project: config.project,
    roles: ["roles/container.developer"],
});

// Export the devs ServiceAccount key.
const devsIamServiceAccountKey = util.createServiceAccountKey(`${devsName}Key`, devsIamServiceAccount);

// Export the devs ServiceAccount client secret to authenticate as this service account.
export const devsIamServiceAccountSecret = util.clientSecret(devsIamServiceAccountKey);

// Export the project name for downstream stacks.
export const project = config.project;

/***********************
 * CI App Deployer Service Account
 ***********************/
const ciServiceAccountName = "cideployer"
const ciServiceAccount = new gcp.serviceaccount.Account(ciServiceAccountName, {
    project: config.project,
    accountId: ciServiceAccountName,
    displayName: "CI GCP service",

});

// Bind the roles
util.bindToRole(`${ciServiceAccountName}-automation`, ciServiceAccount, {
    project: config.project,
    roles: ["roles/container.developer", "roles/artifactregistry.writer", "roles/storage.objectAdmin"],
});

// Create key for CI account
const ciServiceAccountKey = util.createServiceAccountKey(`${ciServiceAccountName}Key`, ciServiceAccount);

// Export client secret to authenticate as this service account.
export const ciServiceAccounteAccountSecret = util.clientSecret(ciServiceAccountKey);

// Export the service account email
export const ciServiceAccountEmail = ciServiceAccount.email;

/***********************
 * CI Infra Deployer Service Account
 ***********************/
const ciInfraServiceAccountName = "ciinfradeployer"
const ciInfraServiceAccount = new gcp.serviceaccount.Account(ciInfraServiceAccountName, {
    project: config.project,
    accountId: ciInfraServiceAccountName,
    displayName: "CI GCP infra service",

});

// Bind the roles
util.bindToRole(`${ciInfraServiceAccountName}-automation`, ciInfraServiceAccount, {
    project: config.project,
    roles: ["roles/owner"],
});

// Create key for CI account
const ciInfraServiceAccountKey = util.createServiceAccountKey(`${ciInfraServiceAccountName}Key`, ciInfraServiceAccount);

// Export client secret to authenticate as this service account.
export const ciInfraServiceAccounteAccountSecret = util.clientSecret(ciInfraServiceAccountKey);

// Export the service account email
export const ciInfraServiceAccountEmail = ciInfraServiceAccount.email;


/***********************
 * Cert-manager Service Account
 ***********************/
const certManagerServiceAccountName = "certmanager"
const certManagerServiceAccount = new gcp.serviceaccount.Account(certManagerServiceAccountName, {
    project: config.project,
    accountId: certManagerServiceAccountName,
    displayName: "CI GCP infra service",

});

// Bind the roles
util.bindToRole(`${certManagerServiceAccountName}-automation`, certManagerServiceAccount, {
    project: config.project,
    roles: ["roles/dns.admin"],
});

// Export client secret to authenticate as this service account.
// Create key for CI account
const certManagerServiceAccountKey = util.createServiceAccountKey(`${certManagerServiceAccountName}Key`, certManagerServiceAccount);

// Export client secret to authenticate as this service account.
export const certManagerServiceAccountSecret = util.clientSecret(certManagerServiceAccountKey);

// Export the service account email
export const certManagerServiceAccountEmail = certManagerServiceAccount.email;

/***********************
 * Custom Metrics Service Account
 ***********************/
const customMetricsServiceAccountName = "custom-metrics-sd-adapter"
const customMetricsServiceAccount = new gcp.serviceaccount.Account(customMetricsServiceAccountName, {
    project: config.project,
    accountId: customMetricsServiceAccountName,
    displayName: "CI GCP infra service",

});

// Bind the roles
util.bindToRole(`${customMetricsServiceAccountName}-automation`, customMetricsServiceAccount, {
    project: config.project,
    roles: ["roles/monitoring.editor"],
});

// IAM Policy binding for Workload Identity
const workloadIdentityBinding = new gcp.projects.IAMMember(`${customMetricsServiceAccountName}-workloadIdentityBinding`, {
    project: config.project,
    role: "roles/iam.workloadIdentityUser",
    member: pulumi.interpolate`serviceAccount:${config.project}.svc.id.goog[custom-metrics/custom-metrics-stackdriver-adapter]`,
    // The member is the Kubernetes service account in the format: "serviceAccount:[PROJECT_ID].svc.id.goog[namespace/k8s-service-account]"
    // Replace "custom-metrics" and "custom-metrics-stackdriver-adapter" with your correct namespace and Kubernetes service account names
});

// Export the service account email
export const  customMetricsServiceAccountEmail = customMetricsServiceAccount.email;
