@use 'layout' as *;
@use 'misc' as *;
@use 'fonts' as *;

.table {
    background: var(--table-row-bg-color);
    border-radius: $border-radius-2;

    .table__input {
        background: var(--table-row-bg-color);
        border-top-left-radius: $border-radius-2;
        border-top-right-radius: $border-radius-2;
    }

    &:has(.table__input):has(.table__header) {
        .table__header {
            border-top: $border-style $border-width var(--table-border-color);
        }
    }

    &:not(:has(.table__input)) {
        .table__header {
            border-top-left-radius: $border-radius-2;
            border-top-right-radius: $border-radius-2;
        }
    }

    .table__header {
        background: var(--table-header-bg-color);
        color: var(--table-header-fg-color);
    }

    .table__row {
        background: var(--table-row-bg-color);

        &:not(:last-of-type) {
            border-bottom: $border-width $border-style var(--table-border-color);
        }

        &.table__row--selected {
            background: var(--table-row-selected-bg-color);
            color: var(--table-row-selected-fg-color);
        }

        &.table__row--clickable {
            &:hover {
                background: var(--table-row-hover-bg-color);
                color: var(--table-row-hover-fg-color);
            }
        }
    }

    .table__empty {
        color: var(--table-empty-fg-color);
    }

    &:not(.table--borderless) {
        border: $border-width $border-style var(--table-border-color);
        border-radius: $border-radius-2;
    }

    &.table--borderless {
        .table__row {
            &:last-of-type {
                border-bottom: $border-width $border-style var(--table-border-color);
            }
        }
    }

    &.table__vertical_dividers {
        .table__cell {
            &:not(:last-child) {
                border-right: $border-width $border-style var(--table-border-color);
            }
        }
    }
}
