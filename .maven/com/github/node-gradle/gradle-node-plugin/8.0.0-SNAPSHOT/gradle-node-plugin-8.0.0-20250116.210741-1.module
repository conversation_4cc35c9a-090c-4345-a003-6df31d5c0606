{"formatVersion": "1.1", "component": {"group": "com.github.node-gradle", "module": "gradle-node-plugin", "version": "8.0.0-SNAPSHOT", "attributes": {"org.gradle.status": "integration"}}, "createdBy": {"gradle": {"version": "7.6.3"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "files": [{"name": "gradle-node-plugin-8.0.0-SNAPSHOT.jar", "url": "gradle-node-plugin-8.0.0-SNAPSHOT.jar", "size": 300905, "sha512": "25ac864d198c6398b0cb65d0f8116260ccc6ce11c9c5c2f2097a77516d451cbee86ae65bfa3f53e0bf26482cf195aa62a790feddb475f82b7bc8f2285f6cfc6d", "sha256": "21962efa89339dfa704a4d6db711da3b3851639a832582bddbcfb3502cf04f1f", "sha1": "73663593b05dfb3ba0343704795d2688af24ab15", "md5": "ffbc0901d5d2eea991414511383522b2"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.14.2"}}], "files": [{"name": "gradle-node-plugin-8.0.0-SNAPSHOT.jar", "url": "gradle-node-plugin-8.0.0-SNAPSHOT.jar", "size": 300905, "sha512": "25ac864d198c6398b0cb65d0f8116260ccc6ce11c9c5c2f2097a77516d451cbee86ae65bfa3f53e0bf26482cf195aa62a790feddb475f82b7bc8f2285f6cfc6d", "sha256": "21962efa89339dfa704a4d6db711da3b3851639a832582bddbcfb3502cf04f1f", "sha1": "73663593b05dfb3ba0343704795d2688af24ab15", "md5": "ffbc0901d5d2eea991414511383522b2"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "gradle-node-plugin-8.0.0-SNAPSHOT-javadoc.jar", "url": "gradle-node-plugin-8.0.0-SNAPSHOT-javadoc.jar", "size": 261, "sha512": "752d4c0c3ca329ffe7af47772f0830ad9cfa2b7efce225960d13a329587035e44c53ebf6b16f1f5fc522f44ac995599bbce68aa1bfb630b14bace8c76929bb95", "sha256": "14b3ab19e413c6a27c6b253780f2a0c6dc93d5baa7a040f64f5691ef290fd93c", "sha1": "04f7b88194b89b4df4d0d202ae7b04d5b37549c1", "md5": "86843edc4b13eb52eac0bbb5414fae98"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "gradle-node-plugin-8.0.0-SNAPSHOT-sources.jar", "url": "gradle-node-plugin-8.0.0-SNAPSHOT-sources.jar", "size": 41534, "sha512": "3ac1054f35d351a07d00a2490dc014ca84e6a380d3cbd56a8f695b39b33002c565eeeb8f04d21bba1656ff236f9e5de48ced0c9376bed5853479ae2db8a9fe2b", "sha256": "dd5f5460302377786855e9f71b9e4e5300c7b4fd4f6a792388affa0bd9b83d3a", "sha1": "10e59409943be8660c2d36905d8934e2c118600a", "md5": "ee4d506bba1d99f60eb868609966628a"}]}]}