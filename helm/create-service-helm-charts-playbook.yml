---
- hosts: localhost
  vars_prompt:
    - name: service
      prompt: What is the name of the service
      private: no
  vars:
    root_path: "{{ playbook_dir }}/../projects/services/."
    scaffold_path: "{{ playbook_dir }}/scaffold"
    base_service_chart_path: "{{ playbook_dir }}/baseservice"
    service_helm_path: "{{ root_path }}/{{ service }}/.helm"
    service_chart_path: "{{ service_helm_path }}/{{ service }}"
    base_service_chart_rel_path: "{{ base_service_chart_path | relpath(service_chart_path) }}"
  tasks:
    - name: Create service helm directory
      file:
        path: "{{ service_helm_path }}"
        state: directory
    - name: Create helm charts
      ansible.builtin.shell:
        cmd: "helm create {{ service }} --starter {{ scaffold_path }}"
        chdir: "{{ service_helm_path }}"
    - name: Add Subchart Dependency
      blockinfile:
        path: "{{ service_chart_path }}/Chart.yaml"
        block: |
          dependencies:
             - name: baseservice
               repository: file://{{ base_service_chart_rel_path }}
               version: 0.0.1
    - name: Update dependency
      ansible.builtin.shell:
        cmd: "helm dependency update {{ service_chart_path }}"
